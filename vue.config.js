module.exports = {
  outputDir: "production",

  devServer: {
    // dev || prod
    proxy: {
      "/api": {
        target: "https://dev.yellosis.com/cym702-pet",
        changeOrigin: true,
      },
    },
    // local
    // proxy: "https://dev.yellosis.com/cym702-pet",
    // overlay: false,
  },

  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",

  transpileDependencies: ["vuetify"],

  css: {
    loaderOptions: {
      scss: {
        additionalData: `
          @import "@/styles/global.scss";
        `,
      },
      sass: {
        additionalData: `
          @import "@/styles/variables.scss"
        `,
      },
    },
  },

  chainWebpack: (config) => {
    const svgRule = config.module.rule("svg");

    svgRule.uses.clear();

    svgRule
      .use("babel-loader")
      .loader("babel-loader")
      .end()
      .use("vue-svg-loader")
      .loader("vue-svg-loader");

    config.module
      .rule("fonts")
      .test(/\.(ttf|otf|eot|woff|woff2)$/)
      .use("file-loader")
      .loader("file-loader")
      .tap((options) => {
        options = {
          // limit: 10000,
          name: "/styles/fonts/[name].[ext]",
        };
        return options;
      })
      .end();
  },

  pluginOptions: {
    i18n: {
      locale: "ko",
      fallbackLocale: "en",
      localeDir: "locales",
      enableInSFC: false,
      enableBridge: false,
    },
  },
};
