# Cym702: For Pet WebView repository

## Development Environment

- Node.js v16
- Vue.js 2.6.11
- Vuetify 2.4.0
- Vue CLI 4.5.0

## Project Setup

### Installation

```bash
npm install
# or
yarn install
```

### Development Server

```bash
# Local development
npm run local
# or
npm run serve

# Development server
npm run dev

# Live server
npm run live
```

### Build

```bash
# Build for development
npm run build:dev

# Build for production
npm run build
```

### Deployment

```bash
# Deploy to development
npm run deploy-dev
npm run invalidate-dev

# Deploy to production
npm run deploy
npm run invalidate
```

## Project Structure

```
src/
├── api/            # API communication modules
├── assets/         # Static resources (images, fonts)
├── components/     # Reusable components
├── locales/        # i18n support (Korean, English, Chinese)
├── pages/          # Page components
├── plugins/        # Vue plugin configurations
├── router/         # Routing configuration
├── store/          # Vuex state management
├── styles/         # Global styles
└── main.js         # Application entry point
```

## Key Features

- Internationalization (i18n)
- Responsive UI (Vuetify)
- State Management (Vuex)
- API Communication (Axios)
- Chart Visualization (ApexCharts)
- Kakao Login Integration
- AWS S3 Deployment

## API Endpoints

- Base URL: `https://dev.yellosis.com/cym702-pet`
- Token handling through interceptors for authenticated requests

## Configuration

- `vue.config.js`: Vue CLI configuration
- `.env.*`: Environment-specific variables
- `babel.config.js`: Babel configuration

## Deployment Environment

- Development: AWS S3 (cym-pet-webview-dev.yellosis.com)
- Production: AWS S3 (cym-pet-webview)
- CDN distribution through CloudFront

## Browser Support

```
> 1%
last 2 versions
not dead
```

## Internationalization

The application supports multiple languages:

- English (en)
- Korean (ko)
- Chinese (cn)
- Japanese (ja)

Language selection is based on the user's browser settings and can be manually changed within the application.

## Service Worker & PWA

- Progressive Web App(PWA) 지원을 위해 `public/service-worker.js`가 포함되어 있습니다.
- 오프라인 상태에서는 `offline.html`이 자동으로 제공됩니다.
- 서비스 워커는 `main.js`에서 등록되며, 캐시 및 오프라인 동작을 관리합니다.
- 최신 브라우저에서 홈 화면 추가, 오프라인 접근 등 PWA 기능을 사용할 수 있습니다.

## State Management

Vuex is used for state management with the following main modules:

- Authentication
- User Settings
- Test Results
- Health Records

## UI Components

The application uses Vuetify components for consistent design and responsive layout. Custom components are built on top of Vuetify's foundation.

## Testing

Run tests using:

```bash
npm run test:unit    # Unit tests
npm run test:e2e     # End-to-end tests
```

## Contributing

1. Create a feature branch from development
2. Make your changes
3. Submit a pull request to development
4. After review, changes will be merged to main

## Version Control

- Development branch: Active development
- Main branch: Production-ready code
- Version tags follow semantic versioning (X.Y.Z)
