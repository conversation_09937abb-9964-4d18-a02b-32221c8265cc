const CACHE_NAME = "cym-pet-pwa-cache-v1";
const OFFLINE_URL = "/offline.html";

const PRECACHE_ASSETS = [OFFLINE_URL];

self.addEventListener("install", (e) => {
  console.log("[Service Worker] Install event");

  e.waitUntil(
    caches.open(CACHE_NAME).then((_cache) => {
      console.log("[Service Worker] Caching app shell");
      return _cache.addAll(PRECACHE_ASSETS);
    }),
  );
});

self.addEventListener("activate", (e) => {
  console.log("[Service Worker] Activating new service worker...");

  e.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((_cacheName) => {
          if (_cacheName !== CACHE_NAME) {
            console.log("[Service Worker] Removing old cache", _cacheName);
            return caches.delete(_cacheName);
          }
        }),
      );
    }),
  );

  return self.clients.claim();
});

self.addEventListener("fetch", (e) => {
  e.respondWith(
    fetch(e.request).catch(() => {
      if (e.request.mode === "navigate") {
        return caches.match(OFFLINE_URL);
      }
    }),
  );
});
