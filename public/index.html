<!DOCTYPE html>
<html lang="en">

<head>
  <base href="/" />
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <!-- <meta name="robots" content="noindex, nofollow" /> -->
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1, user-scalable=0, viewport-fit=cover" />
  <!-- <meta name='viewport' content='width=device-width, initial-scale=1, viewport-fit=cover'> -->
  <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
  <meta name="google-site-verification" content="ucS-P_3KB0j5-aiwzgzw0JaZR7KOm9B-2e7U9RspqEg" />
  <!-- PROD -->
  <!-- <script>
      (function(w, h, _a, t, a, b) {
        w = w[a] = w[a] || {
          config: {
            projectAccessKey: "x6047h881ncv7-xqte6c12d9bqo-xovf3tcul3stn",
            pcode: 34698,
            sampleRate: 100,
            proxyBaseUrl: "https://rum-ap-northeast-2.whatap-browser-agent.io/",
          },
        };
        a = h.createElement(_a);
        a.async = 1;
        a.src = t;
        t = h.getElementsByTagName(_a)[0];
        t.parentNode.insertBefore(a, t);
      })(
        window,
        document,
        "script",
        "https://repo.whatap-browser-agent.io/rum/prod/v1/whatap-browser-agent.js",
        "WhatapBrowserAgent",
        ""
      );
    </script> -->

  <!-- DEV -->

  <script src="https://developers.kakao.com/sdk/js/kakao.js"></script>
  <script src="https://apis.google.com/js/platform.js" async defer></script>
  <script type="text/javascript"
    src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
  <meta name="google-signin-client_id"
    content="1008064697100-gudic3h88vfjnh4uhcdhm7lgpdpt52oh.apps.googleusercontent.com" />
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@100;300;400;500;700;900&display=swap"
    rel="stylesheet" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@latest/css/materialdesignicons.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" />
  <!-- chartjs -->

  <!-- firebase -->
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to continue.
    </strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>