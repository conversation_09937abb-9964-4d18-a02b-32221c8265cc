export default {
  apexchartOptions: () => {
    const chartOptions = {
      offsetX: 0,
      animations: {
        enabled: false
      },
      dropShadow: {
        enabled: true,
        color: "#A7A7A7",
        top: 5,
        left: 0,
        blur: 2,
        opacity: 0.15
      },
      toolbar: {
        show: false
      },
      states: {
        active: {
          allowMultipleDataPointsSelection: true
        }
      }
    };
    return chartOptions;
  },

  strokeOptions: () => {
    const strokeOptions = {
      width: 2.5,
      lineCap: "butt",
      curve: "straight"
    };

    return strokeOptions;
  },

  xaxisOptions: (chartLabels) => {
    const xaxisOptions = {
      tooltip: {
        enabled: false
      },
      axisBorder: {
        show: false
      },
      labels: {
        rotate: 0,
        rotateAlways: false,
        style: {
          colors: ["#646464"],
          cssClass: "apexcharts-yaxis-label"
        },
        // formatter: function(value) {
        //   if (chartLabels.length > 5) {
        //     if (value % 50 === 0) {
        //       return chartLabels[value - 1];
        //     } else {
        //       return "";
        //     }
        //   }
        //   return chartLabels[value - 1];
        // }
      }
    };

    return xaxisOptions;
  },

  markersOptions: () => {
    const markersOptions = {
      strokeColor: ["#41d8e6"],
      colors: ["#fff"],
      size: 5,
      hover: {
        size: 5.5
      }
    };
    return markersOptions;
  },
  gridOptions: () => {
    const gridOptions = {
      yaxis: {
        lines: {
          show: true
        }
      },
      strokeDashArray: 2
    };
    return gridOptions;
  }
};
