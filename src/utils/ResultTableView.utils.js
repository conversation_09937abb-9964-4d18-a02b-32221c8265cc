import i18n from "../../src/i18n.js";

/**
 *
 * 1. if 각 물방울을 클릭했을 때 active
 * 2. if state에 따른 맞는 물방울 배치
 * 3. if 배열  의 length가 0인 경우
 * 4. 무조건 다섯개를 보여줘야 한다. length를 다섯개를 만들어 배치한다.
 * 5. if 클릭하였을 때 최종 점수 보여주기
 */

const splitString = (str) => {
  const newStr = `${str.substring(4, 6)}/${str.substring(6, 8)}`;
  return newStr;
};

export const bloodScoring = (value) => {
  if (value === 1) return i18n.t("main_normal_level");
  if (value === 2) return i18n.t("main_warning_level");
  if (value === 3) return i18n.t("main_caution_level");
  if (value === 4) return i18n.t("main_danger_level");
};

export const proteinAndGlucoseScoring = (value) => {
  if (value === 1 || value === 2) return i18n.t("main_normal_level");
  if (value === 3) return i18n.t("main_warning_level");
  if (value === 4) return i18n.t("main_caution_level");
  if (value === 5 || value === 6) return i18n.t("main_danger_level");
};
export const pHScoring = (value) => {
  if (value === 1 || value === 2 || value === 3 || value === 4) return i18n.t("main_normal_level");
  if (value === 5) return i18n.t("main_warning_level");
};

export const ketoneScoring = (value) => {
  if (value === 1) return i18n.t("main_normal_level");
  if (value === 2) return i18n.t("ketone_caution_level_plus_minus");
  if (value === 3) return i18n.t("ketone_caution_level_plus");
  if (value === 4) return i18n.t("main_warning_level");
  if (value === 5) return i18n.t("main_danger_level");
};
export const UrineTestResultScoring = (type, value) => {
  if (type === "blood") return bloodScoring(value);
  if (type === "glucose") return proteinAndGlucoseScoring(value);
  if (type === "protein") return proteinAndGlucoseScoring(value);
  if (type === "ph") return pHScoring(value);
  if (type === "ketone") return ketoneScoring(value);
};

const createArrayFiveLength = (type, numberArr, urineTestResultArray, value) => {
  /**
          length : 1 -> urineTestResultArray[0]
       *  length : 2 -> urineTestResultArray[idx - 3]
          length : 3 -> urineTestResultArray[idx - 2]
          length : 4 -> urineTestResultArray[idx - 1]
  */
  let substract = 0;
  if (value) {
    substract = 0;
  } else {
    substract = numberArr.length;
  }

  const array = Array.from({ length: 5 }, (_, idx) => {
    if (numberArr.includes(idx)) {
      return {
        state: i18n.t("none"),
        date: i18n.t("none"),
      };
    } else {
      // console.log("idx", idx, "substract", substract, urineTestResultArray[0].level);
      if (urineTestResultArray.length === 1) {
        return {
          state: UrineTestResultScoring(type, urineTestResultArray[0].level),
          date: createdAtStr(urineTestResultArray[0].createdAt),
        };
      } else {
        return {
          state: UrineTestResultScoring(type, urineTestResultArray[idx - substract].level),
          date: createdAtStr(urineTestResultArray[idx - substract].createdAt),
        };
      }
    }
  });

  return array;
};

const createdAtStr = (createdAt) => {
  const splitStr = createdAt.split("T")[0];
  const date = splitStr.split("-");

  return `${date[1]}.${date[2]}`;
};

/** length가 5인 배열을 만들어주는 함수 */
export const createFiveLengthArray = (type, urineTestResultArray) => {
  let createArray = [];
  if (urineTestResultArray.length === 0) {
    // array = [0, 1, 2, 3, 4]
    const numberArr = [0, 1, 2, 3, 4];
    createArray = createArrayFiveLength(type, numberArr, urineTestResultArray);
  }
  if (urineTestResultArray.length === 1) {
    const numberArr = [0, 1, 2, 3];
    const value = true;
    createArray = createArrayFiveLength(type, numberArr, urineTestResultArray, value);
  }

  if (urineTestResultArray.length === 2) {
    const numberArr = [0, 1, 2];
    createArray = createArrayFiveLength(type, numberArr, urineTestResultArray);
  }

  if (urineTestResultArray.length === 3) {
    const numberArr = [0, 1];
    createArray = createArrayFiveLength(type, numberArr, urineTestResultArray);
  }

  if (urineTestResultArray.length === 4) {
    const numberArr = [0];
    createArray = createArrayFiveLength(type, numberArr, urineTestResultArray);
  }

  if (urineTestResultArray.length === 5) {
    createArray = Array.from({ length: 5 }, (_, idx) => {
      return {
        state: UrineTestResultScoring(type, urineTestResultArray[idx].level),
        date: createdAtStr(urineTestResultArray[idx].createdAt),
      };
    });
  }

  return createArray;
};

export const setIsActiveValue = (createUrineTestResultArr, activedIdx) => {
  const setIsActiveValueArr = createUrineTestResultArr.map((result, idx) => {
    if (result.state !== i18n.t("none") && idx === activedIdx) {
      return {
        state: result.state,
        isActive: true,
      };
    }
    return {
      state: result.state,
      isActive: false,
    };
  });

  return setIsActiveValueArr;
};

/**
 * 적  절  : good  <img src="@/assets/_assets/svg/_droplets/good.svg" />
 * 주  의  : warning <img src="@/assets/_assets/svg/_droplets/warning.svg" />
 * 경  고  : caution <img src="@/assets/_assets/svg/_droplets/caution.svg" />
 * 위험  : danger <img src="@/assets/_assets/svg/_droplets/danger.svg" />
 */

/** type별 이미지 반환 함수 */

export const getDropletsImg = (type, array) => {
  // console.log(type);
  const dropletsImg = array.map((item) => {
    if (type === "ketone" && item.state === i18n.t("main_normal_level")) {
      return {
        src: require("@/assets/images/droplets/result_normal.png"),
        isActive: item.isActive,
      };
    }
    if (type === "ketone" && item.state === i18n.t("main_warning_level")) {
      return {
        src: require("@/assets/images/droplets/ketone_caution.png"),
        isActive: item.isActive,
      };
    }
    if (item.state === i18n.t("main_normal_level")) {
      return {
        src: require("@/assets/images/droplets/result_normal.png"),
        isActive: item.isActive,
      };
    }

    if (
      item.state === i18n.t("main_warning_level") ||
      item.state === i18n.t("ketone_warning_level_plus_minus") ||
      item.state === i18n.t("ketone_warning_level_plus")
    ) {
      return {
        src: require("@/assets/images/droplets/result_caution.png"),
        isActive: item.isActive,
      };
    }

    if (item.state === i18n.t("main_caution_level")) {
      return {
        src: require("@/assets/images/droplets/result_warning.png"),
        isActive: item.isActive,
      };
    }

    if (item.state === i18n.t("main_danger_level")) {
      return {
        src: require("@/assets/images/droplets/result_danger.png"),
        isActive: item.isActive,
      };
    }

    if (item.state === i18n.t("main_good_level")) {
      return {
        src: require("@/assets/images/droplets/ketone_good.png"),
        isActive: item.isActive,
      };
    }

    return {
      src: require("@/assets/images/droplets/result_blank.png"),
      isActive: item.isActive,
    };
  });

  return dropletsImg;
};
