export default {
  LOGIN(state, accessToken) {
    state.accessToken = accessToken;
    state.isLogin = true;
    localStorage.setItem("auth", accessToken);
  },
  LOGOUT(state) {
    state.accessToken = null;
    state.isLogin = false;
    localStorage.removeItem("auth");
    localStorage.removeItem("exp");
    localStorage.removeItem("subjectId");
    localStorage.removeItem("surveyStatus");
    localStorage.removeItem("username");
    localStorage.removeItem("ketoneMode");
    localStorage.removeItem("isSnsLogin");
    localStorage.removeItem("auth");
    sessionStorage.clear();
    location.href = location.origin + "/login";
  },
  WITHDRAWAL(state) {
    state.accessToken = null;
    state.isLogin = false;
    localStorage.removeItem("auth");
    sessionStorage.clear();
    location.href = location.origin + "/login";
  },
  SNS_LOGIN(state, accessToken) {
    state.accessToken = accessToken;
    state.isLogin = true;
    localStorage.setItem("auth", accessToken);
  },
  SET_COOKIE(state, token) {
    localStorage.setItem("auth", token);
    state.accessToken = token;
  },
  SET_TOKEN(state, token) {
    localStorage.setItem("auth", token);
    state.accessToken = token;
  },
};
