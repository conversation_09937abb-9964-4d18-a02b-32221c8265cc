export default {
  GET_PHONE(state, phone) {
    state.join.phone = phone;
  },

  GET_COUNTRY(state, country) {
    state.join.country = country;
  },

  GET_USERID(state, userId) {
    state.join.account = userId;
  },

  GET_PASSWORD(state, password) {
    state.join.password = password;
  },

  // GET_NICKNAME(state, nickname) {
  //   state.join.nickname = nickname;
  // },

  GET_MARKETING_ALLOW(state, terms) {
    state.join.marketingAllow = terms.marketingAllow;
  },

  GET_USER_INFO(state, petInfo) {
    state.userImage = petInfo.image;
    state.username = petInfo.username;
  },

  GET_PET_DETAIL(state, petInfo) {
    state.join.petDetail.typeId = petInfo.typeId;
    state.join.petDetail.sex = petInfo.sex;
    state.join.petDetail.neutered = petInfo.neutered;
    state.join.petDetail.nickname = petInfo.nickname;
    state.join.petDetail.initialWeight = petInfo.initialWeight;
    state.join.petDetail.targetWeight = petInfo.targetWeight;
    state.join.petDetail.birth = petInfo.birth;
    state.join.petDetail.adoptionDate = petInfo.adoptionDate;
    state.join.petDetail.registrationNumber = petInfo.registrationNumber;
  },
};
