import authActions from "./auth.js";
import cym702Actions from "./cym702.js";
import { fetchGetUserInfo, fetchGetDeviceInfo } from "../../api/user/index";

export default {
  ...authActions,
  ...cym702Actions,

  GET_USER_INFO({ commit }) {
    return fetchGetUserInfo().then((response) => {
      commit("GET_USER_INFO", {
        username: response.data.userInfo.name,
        image: response.data.userInfo.image
      });
    });
  },
  GET_DEVICE_INFO(_, device) {
    return fetchGetDeviceInfo(device);
  }
};
