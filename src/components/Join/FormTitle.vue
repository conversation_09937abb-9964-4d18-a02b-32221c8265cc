<template>
  <div>
    <div class="form-title__wrapper" v-if="currentStateIdx === 3 && join">
      <img :src="title[currentStateIdx]" alt="slogan" />
    </div>
    <div v-else-if="!join" class="form-title__wrapper">
      {{ title[currentStateIdx + 5] }}
    </div>
    <div class="form-title__wrapper" v-else>
      {{ title[currentStateIdx] }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentStateIdx: Number,
    join: Boolean,
  },
  mounted() {
    console.log(this.currentStateIdx);
  },
  data() {
    return {
      title: [
        this.$i18n.t("comment_phone_cert_title"),
        this.$i18n.t("join_id_title"),
        this.$i18n.t("join_pwd_title"),
        require("@/assets/images/cym702_logo/cym702_slogan.png"),
        this.$i18n.t("join_terms_title"),
        this.$i18n.t("purpose_answer"),
        this.$i18n.t("exercise_answer"),
        this.$i18n.t("chronic_answer"),
        this.$i18n.t("drinking_answer"),
        this.$i18n.t("smoking_answer"),
        this.$i18n.t("diet_answer"),
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.form-title__wrapper {
  width: 100%;
  font-weight: 500;
  font-size: 26px;
  text-align: center;
  letter-spacing: -0.03em;
  padding: 30px 30px 0 30px;
  img {
    padding-top: 10px;
    width: 100%;
  }
}
</style>
