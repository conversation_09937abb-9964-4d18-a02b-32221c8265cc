<template>
  <div>
    <div v-if="currentStateIdx < 6 && join" class="form-subtitle__wrapper" v-html="subtitle[currentStateIdx]"></div>
    <div v-else class="form"></div>
  </div>
</template>

<script>
export default {
  props: {
    currentStateIdx: Number,
    join: Boolean,
  },
  data() {
    return {
      subtitle: [
        this.$i18n.t("comment_phone_cert_one"),
        this.$i18n.t("join_id_subtitle"),
        this.$i18n.t("join_pwd_subtitle"),
        this.$i18n.t("profile_subtitle"),
        this.$i18n.t("join_terms_subtitle"),
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.form-subtitle__wrapper {
  width: 100%;
  padding: 30px 30px;
  font-weight: 400;
  font-size: 18px;
  text-align: left;
  letter-spacing: -0.03em;
  color: #646464;
  line-height: 23px;
  letter-spacing: -0.03em;
}

.form {
  padding: 20px;
}
</style>
