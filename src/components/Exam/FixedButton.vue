<template>
  <div>
    <div class="fixed-btn__wrapper dp-30">
      <div
        class="fixed-btn"
        :class="isKo ? 'fixed-btn__ko' : 'fixed-btn__en'"
        :style="{ backgroundColor: color, color: textColor }"
        @click="btnHandler"
      >
        {{ title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    color: String,
    title: String,
    path: String,
    textColor: String,
  },
  data() {
    return {
      isKo:
        this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn"),
    };
  },
  methods: {
    btnHandler() {
      this.$router.push({ path: this.path });
      this.$emit("changeCurrentState");
      this.$emit("setLocalStorage");
    },
  },

  mounted() {
    this.isKo =
      this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn");
  },
};
</script>

<style lang="scss" scoped>
.fixed-btn__wrapper {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 50px;
  padding: 0 30px;
}

.fixed-btn {
  height: 50px;
  z-index: 999999;
  width: 100%;
  border-radius: 5px;
  line-height: 50px;
  font-size: 20px;
}

.fixed-btn__ko {
  font-family: Noto Sans KR !important;
  background-color: #dadada;
  font-weight: bold;
}

.fixed-btn__en {
  font-family: GilroyBold !important;
  background-color: #dadada;
}
</style>
