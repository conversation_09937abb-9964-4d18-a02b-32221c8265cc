<template>
  <div>
    <LoadingPageVue v-if="showLoading" />
    <ErrorModal
      :error="content"
      v-if="showResultModal"
      type="analysis"
      @isClicked="reshootHandler"
    />
    <div class="content-section">
      <div class="notice-contents__wrapper">
        <div class="notice-content-title__wrapper">
          <span class="notice-content-title__bold">Cym<sup>702</sup> Pet</span>
          <span
            class="before"
            :class="lang === 'ko' || lang === 'cn' ? 'before--ko' : 'before--en'"
            v-html="this.$i18n.t('before_test')"
          ></span>
        </div>
        <div class="content-items__wrapper">
          <div class="content-items" v-for="item in guideContent" :key="item.ic">
            <div class="content-item">
              <div class="item--ic">{{ item.ic }}</div>
              <div class="item--txt">
                <div
                  class="txt--bold"
                  :class="lang === 'ko' || lang === 'cn' ? 'txt--bold--ko' : 'txt--bold--en'"
                >
                  {{ item.textBold }}
                </div>
                <div class="txt--normal">{{ item.textNormal }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- <button @click="requestOpenCamera">REQ-TAKE-SHEET</button> -->
      </div>
    </div>
  </div>
</template>

<script>
import LoadingPageVue from "./LoadingPage.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  components: {
    LoadingPageVue,
    ErrorModal,
  },
  data() {
    return {
      showLoading: false,
      showResultModal: false,
      content: "",
      lang: this.$i18n.locale === "ko" || this.$i18n.locale === "cn" ? "ko" : "en",
      guideContent: [
        {
          ic: "💊",
          textBold: this.$i18n.t("caution_title1"),
          textNormal: this.$i18n.t("caution_content1"),
        },
        {
          ic: "🎈",
          textBold: this.$i18n.t("caution_title2"),
          textNormal: this.$i18n.t("caution_content2"),
        },
      ],
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  methods: {
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null ? subjects[selectedId].id : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    requestOpenCamera() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      // console.log("camera request");
      const subjectId = this.getSubjectId();
      const message = {
        accessToken: this.$store.state.accessToken,
        subjectId: subjectId,
        description: this.$i18n.t("analysis_description"),
        buttonText: this.$i18n.t("direct_shot"),
        previewDescription: this.$i18n.t("shot_confirm_description"),
        previewOk: this.$i18n.t("yes"),
        previewCancel: this.$i18n.t("no"),
      };
      // console.log(message);
      Webview.requestCamera(message);
      this.showLoading = true;
      this.$nextTick(() => this.listener());
    },
    listener() {
      this.isIos
        ? window.addEventListener("message", this.handleMessage)
        : document.addEventListener("message", this.handleMessage);
    },

    handleMessage(e) {
      const data = JSON.parse(e.data);
      // alert(e.data);
      if (data.payload.result === "CANCLED") this.showLoading = false;
      if (data.payload.action === "REQ-TAKE-SHEET") this.examResultHandler(data.payload.result);
    },
    // error modal emit
    reshootHandler(fromChild) {
      if (fromChild) {
        this.requestOpenCamera();
        this.showResultModal = false;
      }
    },
    examResultHandler(result) {
      // alert(result);
      switch (result) {
        case "QR": {
          this.content = this.$i18n.t("qr_error_msg");
          this.showResultModal = true;
          break;
        }
        case "REF": {
          this.content = this.$i18n.t("qr_error_msg");
          this.showResultModal = true;
          break;
        }
        case "QR_BBOX": {
          this.content = this.$i18n.t("qr_error_msg");
          this.showResultModal = true;
          break;
        }
        case "STRIP": {
          this.content = this.$i18n.t("strip_error_msg");
          this.showResultModal = true;
          break;
        }
        case "SHADOW": {
          this.content = this.$i18n.t("shadow_error_msg");
          this.showResultModal = true;
          break;
        }
        case "NOSTRIP": {
          this.content = this.$i18n.t("nostrip_error_msg");
          this.showResultModal = true;
          break;
        }
        case "UNKNOWN": {
          this.content = this.$i18n.t("unknown_error_msg");
          this.showResultModal = true;
          break;
        }
        case "CANCLED": {
          this.showLoading = false;
          break;
        }
        case "SUCCESS": {
          this.$store.commit("completeAlert");
          this.$router.push("/home?analysis=done");
          break;
        }
        default: {
          this.content = this.$i18n.t("unknown_error_msg");
          this.showLoading = false;
          break;
        }
      }
      // this.showLoading = false;
    },
    goToCamera() {
      this.$router.push({ path: "/exam/guide" });
    },
    goToHome() {
      this.$router.push({ path: "/home" });
    },
  },

  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
    document.removeEventListener("message", this.handleMessage);
  },
};
</script>

<style lang="scss" scoped>
button {
  height: 50px;
  z-index: 999999;
  width: 100%;
  border-radius: 5px;
  line-height: 50px;
  font-size: 20px;
  font-weight: bold;
  background-color: #dadada;
}

/* content-section */
.content-section {
  width: 100%;
  height: 90vh;
  display: flex;
  /* justify-content: center; */
  padding: 17vh 30px;
  color: #000000;
}

.content-section .notice-content-title__wrapper {
  width: 100%;
  text-align: left;
  // font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 34px;
  /* padding-bottom: 20px; */
  color: #000000;
  > span {
    width: 100%;
  }
}

.notice-content-title__bold {
  font-family: GilroyBold;
  line-height: 31px;
  letter-spacing: 0;
}

.content-item {
  margin-top: 6vh;
  display: flex;
  align-items: flex-start;
  img {
    padding-top: 5px;
    width: 25px;
    object-fit: contain;
  }
}

.content-items__wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  /* margin-top: 50px; */
  /* align-items: center; */
  /* justify-content: center; */
}

.content-items .content-item .item--txt {
  text-align: left;
  padding-left: 15px;
  font-size: 14px;
  line-height: 25px;
  font-weight: 500;
  letter-spacing: -0.03em;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.content-items .content-item .item--ic {
  font-size: 36px;
}

.content-items .content-item .item--txt .txt--bold {
  font-weight: 500;
  font-size: 22px;
}

.txt--normal {
  font-size: 18px;
  font-weight: 400;
}
/* button-section */

.button-section {
  width: 100%;
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 30px;
}
.exam-next-btn {
  width: 100%;
  height: 40px;
  color: #fff;
  background: #41d8e6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  font-weight: 700;
  font-size: 20px;
}

.skip-btn__box .skip-btn {
  border-radius: 5px;
  box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.05);
  width: 60px;
  background-color: #ffffff !important;
  padding: 3px 7px !important;
  // font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0 !important;
  color: #000000 !important;
}

.content-items .content-item .item--ic {
  font-size: 36px;
}

.before--ko {
  font-family: Noto Sans KR !important;
  font-size: 24px;
  font-weight: 500;
  line-height: 34.75px;
  letter-spacing: -0.03em;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.before--en {
  font-family: GilroyBold !important;
  font-size: 24px;
  font-weight: 500;
  line-height: 29.4px;
  letter-spacing: -0.03em;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.txt--bold--ko {
  font-family: Noto Sans KR !important;
  font-weight: 500;
}

.txt--bold--en {
  font-family: GilroyBold !important;
}
</style>
