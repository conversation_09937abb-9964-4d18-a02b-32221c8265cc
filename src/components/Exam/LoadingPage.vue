<template>
  <div class="loading__wrapper">
    <div class="close-btn__wrapper">
      <div class="close-btn">
        <!-- <img
          src="@/assets/images/guide_img/close_btn.png"
          alt="close btn"
          @click="closeLoadingPage"
        /> -->
      </div>
    </div>
    <div>
      <div class="loading-title">{{ $t("analyzing") }}</div>
      <div
        class="loading-contents"
        v-html="this.$i18n.t('analyzing_body')"
      ></div>
    </div>
    <div class="logo__wrapper">
      <img :src="gifSrc" alt="loading" @load="startAnimation" />
    </div>
    <div class="bottom-contents" :class="isKo ? 'result_ko' : 'result_en'">
      {{ $t("go_to_result_txt") }}
    </div>
  </div>
</template>

<script>
export default {
  name: "LoadingPage",
  data() {
    return {
      gifSrc: require("@/assets/images/cym702_logo/emblem.gif"),
      reloadInterval: null,
      isKo:
        this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn"),
    };
  },
  methods: {
    startAnimation() {
      if (this.reloadInterval) {
        clearInterval(this.reloadInterval); // Clear previous interval if it exists
      }
      this.reloadInterval = setInterval(() => {
        this.gifSrc += "?rand=" + Math.random(); // Add a random query parameter to force the image to reload
      }, 1500); // Adjust the interval duration as needed
    },
    closeLoadingPage() {
      this.$store.commit("setShowExamLoading", false);
    },
  },
  beforeDestroy() {
    clearInterval(this.reloadInterval); // Clear the interval on component destruction to prevent memory leaks
  },

  mounted() {
    this.isKo =
      this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn");
  },
};
</script>

<style lang="scss" scoped>
.loading__wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0%;
  z-index: 999;
  background: linear-gradient(
    180deg,
    #c9f4f8 0%,
    #e0f9fb 20.31%,
    #f6fdfe 38.54%,
    #fcfeff 80.73%,
    #ffffff 100%
  );
  padding-top: 30%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.close-btn__wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  overflow-y: auto;
  padding: 40px 20px;
  font-family: "Noto Sans KR";
}

.close-btn {
  width: 100%;
  height: 24px;
  left: 0;
  text-align: left;
  padding-left: 5px;
  img {
    width: 24px;
  }
}

.loading-title {
  font-size: 26px;
  font-weight: 500;
  margin-bottom: 10px;
}

.loading-contents {
  font-size: 18px;
  line-height: 28px;
  color: #646464;
}

.logo__wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 70px;
  img {
    width: 40vw;
  }
}

.bottom-contents {
  background-color: #41d8e6;
  height: 18%;
  color: #ffffff;
  font-size: 22px;
  padding-top: 10%;
  font-family: "Noto Sans KR";
}

.result_ko {
  font-family: Noto Sans KR !important;
  font-weight: 700;
}

.result_en {
  font-family: GilroyBold !important;
  letter-spacing: -0.8px;
}
</style>
