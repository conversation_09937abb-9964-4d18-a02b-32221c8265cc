<template>
  <div>
    <div class="video-section" v-if="isKo">
      <video
        poster="@/assets/images/guide_thumbnail.png"
        src="@/assets/video/guide_video.mp4"
        type="video/mp4"
        controls
        playsinline
        webkit-playsinline="webkit-playsinline"
        width="100%"
      ></video>
    </div>
    <div class="video-section" v-else>
      <video
        poster="@/assets/images/guide_poster_eng.png"
        src="@/assets/video/eng_guide.mp4"
        type="video/mp4"
        controls
        playsinline
        webkit-playsinline="webkit-playsinline"
        width="100%"
      ></video>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isKo: true,
    };
  },
  mounted() {
    this.isKo = this.$i18n.locale.includes("ko");
  },
};
</script>

<style scoped>
.video-section {
  padding: 0 30px;
  height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
}

.video-section > poster {
  width: 100%;
  height: 100%;
}
.video-section > img {
  width: 100%;
  height: 100%;
}
</style>
