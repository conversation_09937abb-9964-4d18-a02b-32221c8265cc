<template>
  <div>
    <div class="urineTest-table-row">
      <div v-if="isKo" class="col-item urine-item-title">
        <pre>{{ getUrineTestType }}</pre>
      </div>
      <div v-else class="col-item urine-item-title">
        <pre class="en-item-title">{{ getUrineTestType }}</pre>
      </div>
      <div
        class="col-item"
        :class="type"
        v-for="(item, idx) in getDropWaterImages"
        :key="idx"
        @click="getActiveDropletIndex(idx)"
      >
        <div :class="item.isActive ? 'w-30' : 'w-22'">
          <img :src="item.src" />
        </div>
      </div>

      <!-- class="isKo ? col-item result-txt : col-item en-result-txt" -->
      <div
        v-if="isKo"
        class="col-item result-txt"
        :class="getClassNameForResult"
        @click="moveHistoryPage(urineTestItems)"
      >
        <pre class="ko-result" v-html="getScoreResult"></pre>
      </div>
      <div
        v-else
        class="col-item en-result-txt"
        :class="getClassNameForResult"
        @click="moveHistoryPage(urineTestItems)"
      >
        <pre class="en-result" v-html="getScoreResult"></pre>
      </div>
    </div>
  </div>
</template>

<script>
import {
  createFiveLengthArray,
  setIsActiveValue,
  getDropletsImg,
} from "@/utils/ResultTableView.utils.js";

export default {
  props: {
    urineTestItems: Array,
    urineTestState: String,
    urineTestCompleted: Boolean,
    type: String,
  },
  data() {
    return {
      isKo: true,
      activeDroplets: 4,
      nonTestResult: false,
    };
  },
  watch: {
    urineTestCompleted(newVal, oldVal) {
      // console.log(newVal)
      if (!newVal) {
        const activeDroplets = document.querySelectorAll(".w-30");
        for (let i = 0; i < activeDroplets.length; i++) {
          activeDroplets[i].classList.add("active-waterdrop-ic");
          activeDroplets[i].style.animationDelay = `${(activeDroplets.length -
            i) *
            0.05}s`;
        }
      } else {
        // console.log(this.completed);
      }
    },
  },

  computed: {
    getUrineTestType() {
      if (this.type === "blood") return this.$i18n.t("blood");
      if (this.type === "glucose") return this.$i18n.t("glucose");
      if (this.type === "protein") return this.$i18n.t("protein");
      if (this.type === "ph") return this.$i18n.t("ph");
      else return this.$i18n.t("ketone");
    },
    enClassTxt() {
      return this.isKo ? "col-item result-txt" : "col-item en-result-txt";
    },
    getClassNameForResult() {
      // console.log(this.getScoreResult);
      if (
        this.getScoreResult === this.$i18n.t("main_normal_level") &&
        this.type === "ketone"
      ) {
        return "ketone-normal-txt";
      }
      if (this.getScoreResult === this.$i18n.t("main_normal_level"))
        return "normal-txt";
      if (
        this.getScoreResult === this.$i18n.t("main_warning_level") &&
        this.type === "ketone"
      ) {
        return "ketone-warning-txt";
      }
      if (
        this.getScoreResult === this.$i18n.t("main_good_level") &&
        this.type === "ketone"
      ) {
        return "ketone-caution-txt";
      }
      if (
        this.getScoreResult === this.$i18n.t("main_warning_level") ||
        this.getScoreResult ===
          this.$i18n.t("ketone_warning_level_plus_minus") ||
        this.getScoreResult === this.$i18n.t("ketone_warning_level_plus")
      )
        return "warning-txt";
      if (this.getScoreResult === this.$i18n.t("main_caution_level"))
        return "caution-txt";
      if (this.getScoreResult === this.$i18n.t("main_danger_level"))
        return "danger-txt";
      if (this.getScoreResult === this.$i18n.t("main_good_level"))
        return "good-txt";
      return "blank-txt";
    },

    getScoreResult() {
      const activeIdx = this.$store.state.activeDropletIdx;
      return this.createUrineTestItemsArray[activeIdx].state;
    },

    createUrineTestItemsArray() {
      return createFiveLengthArray(this.type, this.urineTestItems);
    },

    getUrineItemsHistory() {
      return setIsActiveValue(
        this.createUrineTestItemsArray,
        this.activeDroplets
      );
    },

    getDropWaterImages() {
      return getDropletsImg(this.type, this.getUrineItemsHistory);
    },
  },

  methods: {
    moveHistoryPage(arr) {
      if (this.type === "blood") {
        this.$router.push({ path: "/home/<USER>" });
      }
      if (this.type === "ph") {
        this.$router.push({ path: "/home/<USER>" });
      }
      if (this.type === "protein") {
        this.$router.push({ path: "/home/<USER>" });
      }
      if (this.type === "glucose") {
        this.$router.push({ path: "/home/<USER>" });
      }
      if (this.type === "ketone") {
        this.$router.push({ path: "/home/<USER>" });
      }
    },

    classNameAddW30RemoveW22(element) {
      return element.classList.add("w-30"), element.classList.remove("w-22");
    },

    classNameRemoveW30AddW22(element) {
      return element.classList.remove("w-30"), element.classList.add("w-22");
    },

    getActiveDropletIndex(activeIdx) {
      if (
        this.urineTestItems.length === 0 ||
        this.createUrineTestItemsArray[activeIdx].state === this.$i18n.t("none")
      ) {
        return;
      } else {
        this.$store.commit("GET_ACTIVE_DROPLET_IDX", activeIdx);
        const bloodDroplets = document.querySelectorAll(".blood");
        const proteinDroplets = document.querySelectorAll(".protein");
        const glucoseDroplets = document.querySelectorAll(".glucose");
        const phDroplets = document.querySelectorAll(".ph");
        const ketoneDroplets = document.querySelectorAll(".ketone");

        bloodDroplets.forEach((_, index) => {
          bloodDroplets[index].children[0].classList.remove(
            "active-waterdrop-ic"
          );
          proteinDroplets[index].children[0].classList.remove(
            "active-waterdrop-ic"
          );
          glucoseDroplets[index].children[0].classList.remove(
            "active-waterdrop-ic"
          );
          phDroplets[index].children[0].classList.remove("active-waterdrop-ic");
          ketoneDroplets[index].children[0].classList.remove(
            "active-waterdrop-ic"
          );

          if (activeIdx === index) {
            this.classNameAddW30RemoveW22(bloodDroplets[index].children[0]);
            this.classNameAddW30RemoveW22(proteinDroplets[index].children[0]);
            this.classNameAddW30RemoveW22(glucoseDroplets[index].children[0]);
            this.classNameAddW30RemoveW22(phDroplets[index].children[0]);
            this.classNameAddW30RemoveW22(ketoneDroplets[index].children[0]);
          } else {
            this.classNameRemoveW30AddW22(bloodDroplets[index].children[0]);
            this.classNameRemoveW30AddW22(proteinDroplets[index].children[0]);
            this.classNameRemoveW30AddW22(glucoseDroplets[index].children[0]);
            this.classNameRemoveW30AddW22(phDroplets[index].children[0]);
            this.classNameRemoveW30AddW22(ketoneDroplets[index].children[0]);
          }
        });
      }
    },
  },
  mounted() {
    this.isKo = this.$i18n.locale.includes("ko");
    // console.log(this.isKo);
  },
};
</script>

<style lang="scss" scoped>
.urineTest-table-row {
  display: flex;
  align-items: center;
}

.col-item {
  font-family: Noto Sans KR !important;
  flex: 1;
  text-align: center;
  margin: 3px 0px;
  display: flex;
  justify-content: center;
}

.w-22 {
  width: 24px;
  opacity: 0.2;
  img {
    width: 100%;
  }
}

.w-30 {
  top: -5px;
  position: relative;
  width: 35px;
}

.w-30 > img {
  width: 100%;
  object-fit: contain;
}

.normal-txt {
  color: #00bb00;
  border: 1px solid #00bb00 !important;
}

.warning-txt {
  color: #ffcc00;
  border: 1px solid #ffcc00 !important;
}

.ketone-normal-txt {
  color: #00bb00;
  border: 1px solid #00bb00 !important;
}

.caution-txt {
  color: #ff6600;
  border: 1px solid #ff6600 !important;
}

.danger-txt {
  color: #646464;
  border: 1px solid #646464 !important;
}

.blank-txt {
  color: #646464;
  border: 1px solid #646464 !important;
}

.good-txt {
  color: #41d8e6;
  border: 1px solid #41d8e6 !important;
}

.urine-item-title {
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  // margin-bottom: 5px;
  color: #000;
  font-family: Noto Sans KR !important;
  letter-spacing: -0.1em;
}
.en-item-title {
  font-size: 16px;
  // font-weight: 500;
  // display: flex;
  // align-items: center;
  // justify-content: center;
  height: 100%;
  margin-bottom: 5px;
  color: #000;
  font-family: GilroyBold !important;
  letter-spacing: -0.1em;
}

code,
kbd,
pre,
samp {
  font-family: Noto Sans KR;
}

.result-txt {
  font-size: 16px;
  font-weight: 700;
  // background: #f8f8f8;
  opacity: 0.8;
  // padding: 2px;
  height: 28px;
  display: flex;
  align-items: center;
  border: 1px solid #ededed;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  width: 5%;
  min-width: 60px;
  max-width: 60px;
}
.en-result-txt {
  font-size: 13px;
  opacity: 0.8;
  padding: 2px 3%;
  border: 1px solid #ededed;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  letter-spacing: -0.07em;
  width: 10%;
  max-width: 70px;
}

.ko-result {
  letter-spacing: -0.05em;
}

.en-result {
  font-family: GilroyBold !important;
}

.active-waterdrop-ic {
  /* opacity: 1; */
  opacity: 0;
  animation: bounce-in-top 0.7s forwards;
}

@keyframes bounce-in-top {
  0% {
    transform: translateY(-100px);
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    transform: translateY(0);
    animation-timing-function: ease-in;
    opacity: 1;
  }

  90% {
    transform: translateY(-20px);
    animation-timing-function: ease-out;
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    animation-timing-function: ease-out;
    opacity: 1;
  }
}
</style>
