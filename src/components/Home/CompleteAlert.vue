<template>
  <div>
    <div class="complete-exam__bg-modal">
      <div class="alert-window">
        <div class="alert-window__txt">
          <span class="success_analysis">{{ $t("success_analysis") }}</span>
          <br />
          <span v-show="isBoat && isKo"
            >{{ formatUsername(username) }}{{ $t("health_score") }}</span
          >
          <span v-show="isBoat && !isKo" v-html="this.$i18n.t('health_score')"></span>
        </div>
        <div v-show="isBoat" class="emblem-score-container">
          <div class="cym-emblem__wrapper">
            <img src="@/assets/images/cym702_logo/emblem.png" alt="" />
          </div>
          <div class="cym-score__wrapper">
            <span class="score">{{ returnTotalScore }}</span>
            <span class="standard-score">/100</span>
          </div>
        </div>
        <div v-show="isPortable">
          <div class="portable-done" v-html="content"></div>
        </div>
        <div class="alert-window__btn" :class="isKo ? 'result-ko' : 'result-en'">
          <button @click="closeModalWindow">{{ $t("go_to_result") }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    totalScore: Number,
    username: String,
    // isBoat: Boolean,
  },
  data() {
    return {
      content: this.$i18n.t("portable_modal_message"),
      isKo: false,
    };
  },
  computed: {
    returnTotalScore() {
      return this.$store.state.totalScore;
    },

    isBoat() {
      return this.$store.state.isBoat;
    },
    isPortable() {
      return !this.$store.state.isBoat;
    },
  },
  methods: {
    closeModalWindow() {
      this.$store.commit("CLOSE_COMPLETE_MODAL");
    },
    formatUsername(username) {
      return username.length >= 5 ? `${username.slice(0, 5)}...` : username;
    },
  },
  mounted() {
    this.isBoat = this.$store.state.isBoat;
    this.isKo = this.$i18n.locale.includes("ko");
  },
};
</script>

<style lang="scss" scoped>
.success_analysis {
  font-size: 18px;
}

.complete-exam__bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-window {
  background-color: #fff;
  width: 300px;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 37px 6px;
}

.alert-window__txt {
  // font-family: Noto Sans KR, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 23px;
  text-align: center;
  letter-spacing: -0.03em;
}

.alert-window__btn > button {
  min-width: 170px;
  background: #41d8e6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  border: none;
  text-align: center;
  letter-spacing: -0.03em;
  color: #ffffff;
  font-size: 18px;
  line-height: 26px;
  padding: 10px 15px;
}

.result-ko > button {
  font-family: Noto Sans KR !important;
  font-weight: 600;
}

.result-en > button {
  font-family: GilroyBold !important;
}

.emblem-score-container {
  margin: 37px 0px;
  position: relative;
}

.cym-emblem__wrapper > img {
  max-width: 158px;
  width: 100%;
}

.cym-score__wrapper {
  width: 100%;
  position: absolute;
  text-align: center;
  text-indent: 35px;
  top: 50%;
  transform: translateY(-50%);
}

.score {
  font-size: 36px;
  font-family: GilroyBold;
  font-weight: bold;
}

.standard-score {
  font-size: 14px;
  font-family: GilroyMedium;
}

.portable-done {
  background-color: #c9f4f8;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  font-weight: 500;
  font-size: 24px;
  line-height: 35px;
  margin: 25px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
