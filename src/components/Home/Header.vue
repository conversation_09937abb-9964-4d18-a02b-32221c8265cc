<template>
  <div>
    <fixed-header :threshold="50">
      <div :class="isIos ? 'nav-space' : 'nav-space-android'">
        <img src="@/assets/images/cym702_logo/cym-pet.png" alt="cym pet logo" />
      </div>
    </fixed-header>

    <div :class="isIos ? 'pet-logo' : 'pet-logo-android'">
      <img src="@/assets/images/cym702_logo/cym-pet.png" alt="cym pet logo" />
      <!-- 개발용 -->
      <strong v-if="isDevelopment" class="dev">dev</strong>
    </div>
  </div>
</template>

<script>
import FixedHeader from "@/components/Common/FixedHeader.vue";

export default {
  components: {
    FixedHeader,
  },

  data() {
    return {
      isDevelopment: process.env.NODE_ENV === "development",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
};
</script>

<style lang="scss" scoped>
.nav-space {
  width: 100%;
  padding-top: 55px;
  padding-bottom: 10px;
  img {
    width: 35%;
  }
}
.nav-space-android {
  width: 100%;
  padding-top: 45px;
  padding-bottom: 5px;
  img {
    width: 35%;
  }
}

.pet-logo {
  width: 100%;
  display: flex;
  padding: 100px 30px 25px;
  img {
    width: 40%;
  }
}
.pet-logo-android {
  width: 100%;
  display: flex;
  padding: 70px 30px 25px;
  img {
    width: 40%;
  }
}

.dev {
  margin-left: 10px;
  font-size: 12px;
}
</style>
