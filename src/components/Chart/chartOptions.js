import i18n from "../../i18n.js";

const computedHistoryChartYaxisOptions = (urineTestItemType) => {
  return {
    yaxisTitleText() {
      if (urineTestItemType === "blood") return "RBC/µL";
      if (urineTestItemType === "ph") return "";
      return "mg/dL";
    },
    yaxisMaxValue() {
      if (urineTestItemType === "protein" || urineTestItemType === "glucose")
        return 6;
      else if (urineTestItemType === "blood") return 4;
      else return 5;
    },
    yaxisLabelsValue(type, value) {
      switch (type) {
        case "blood":
          if (value === 1) return "0";
          if (value === 2) return "10";
          if (value === 3) return "50";
          if (value === 4) return "250";
          break;
        case "protein":
          if (value === 2) return "10";
          else if (value === 3) return "30";
          else if (value === 4) return "100";
          else if (value === 5) return "300";
          break;
        case "glucose":
          if (value === 2) return "100";
          else if (value === 3) return "250";
          else if (value === 4) return "500";
          else if (value === 5) return "1000";
          break;
        case "ph":
          if (value === 1) return "pH 5";
          else if (value === 2) return "pH 6";
          else if (value === 3) return "pH 7";
          else if (value === 4) return "pH 8";
          else if (value === 5) return "pH 9";
          break;
        case "ketone":
          if (value === 3) return "10";
          else if (value === 4) return "50";
          else if (value === 5) return "100";
          break;
      }
    },
  };
};

export const historyChartYaxisOptions = (urineTestItemType) => {
  const computedYaxis = computedHistoryChartYaxisOptions(urineTestItemType);
  const yaxisOptions = {
    logBase: 1,
    forceNiceScale: urineTestItemType === "blood" ? false : true,
    title: {
      text: computedYaxis.yaxisTitleText(urineTestItemType),
      offsetY: 80,
      offsetX: 20,
      style: {
        color: "#a7a7a7",
        fontSize: "10px",
      },
    },
    min: 1,
    max: computedYaxis.yaxisMaxValue(),
    labels: {
      style: {
        colors: ["#646464"],
        cssClass: "apexcharts-yaxis-label",
      },
      offsetX: 3,
      // formatter: (value, idx) => {
      //   // console.log(idx);
      //   if (value === 1) return computedYaxis.yaxisLabelsValue(urineTestItemType, value);
      //   if (value === 2) return computedYaxis.yaxisLabelsValue(urineTestItemType, value);
      //   if (value === 3) return computedYaxis.yaxisLabelsValue(urineTestItemType, value);
      //   if (value === 4) return computedYaxis.yaxisLabelsValue(urineTestItemType, value);
      //   if (value === 5) return computedYaxis.yaxisLabelsValue(urineTestItemType, value);
      //   if (value === 6) return computedYaxis.yaxisLabelsValue(urineTestItemType, value);
      // },
    },
  };
  return yaxisOptions;
};

export const cymHistoryChartYaxisOptions = () => {
  const yaxisOptions = {
    min: 0,
    max: 100,
    tickAmount: 4,
    labels: {
      style: {
        colors: ["#646464"],
        fontSize: "14px",
        cssClass: "apexcharts-yaxis-label",
      },
      formatter(value) {
        return parseInt(value);
      },
    },
  };
  return yaxisOptions;
};

export const weightCareChartYaxisOptions = (minWeight, maxWeight) => {
  // console.log("weight graph");
  const min = Math.ceil((minWeight - 10) / 5) * 5; // 5단위 범위로 반내림
  const max = Math.ceil((maxWeight + 10) / 5) * 5; // 5단위 범위로 반올림
  // console.log(min, max);
  return {
    logBase: 10,
    forceNiceScale: true,
    min: min,
    max: max,
    tickAmount: 4,
    labels: {
      style: {
        colors: ["#646464"],
        fontSize: "14px",
        cssClass: "apexcharts-yaxis-label",
      },
      formatter(value, idx) {
        return value.toFixed(1);
      },
    },
  };
};

export const waterCareChartYaxisOptions = (target) => {
  // target = maxValue > targetWater ? maxValue : targetWater
  const yaxisOptions = {
    min: 0,
    max: target > 2000 ? target : 2000,
    tickAmount: 4,
    labels: {
      formatter: (value, idx) => {
        if (idx % 2 === 0) {
          return `${value / 1000}`;
        }
        return (value / 1000).toFixed(1);
      },
      style: { fontSize: "14px" },
    },
  };
  return yaxisOptions;
};

export const peeCareChartYaxisOptions = () => {
  const yaxisOptions = {
    min: 0,
    max: 10,
    tickAmount: 2,
  };
  return yaxisOptions;
};

// 🎯 chart options
export const chartOptions = () => {
  const defaultChartOptions = {
    offsetX: 0,
    animations: {
      enabled: false,
    },
    zoom: {
      enabled: false,
    },
    toolbar: {
      show: false,
    },
    states: {
      active: {
        allowMultipleDataPointsSelection: true,
      },
    },
  };

  const dropShadowOption = {
    dropShadow: {
      enabled: true,
      color: "#A7A7A7",
      top: 5,
      left: 0,
      blur: 2,
      opacity: 0.15,
    },
  };
  return {
    careLineChartOption() {
      return {
        ...defaultChartOptions,
        ...dropShadowOption,
      };
    },
    careScatterChartOption() {
      return {
        ...defaultChartOptions,
      };
    },
    historyLineChartOptions() {
      return {
        ...defaultChartOptions,
        ...dropShadowOption,
      };
    },
  };
};

// 🎯 stroke options
export const strokeOptions = () => {
  const stroke = {
    width: 2.5,
    lineCap: "butt",
    curve: "straight",
  };
  return stroke;
};

// 🎯 color options
export const colorsOptions = (chartType) => {
  // console.log("chartType", chartType);
  return {
    careLineChartOptions() {
      return chartType === "pee" ? ["#ffcc00"] : ["#41d8e6"];
    },
    historyLineChartOptions() {
      return ["A7A7A7"];
    },
  };
};

// 🎯 marker options => scatter chart circle stroke options
export const markerOptions = (chartType) => {
  const defaultSize = {
    size: 5.5,
    hover: {
      size: 6,
    },
    strokeWidth: 2,
  };

  return {
    careLineChartOptions() {
      const markerColorStyle = {
        strokeColor: chartType === "pee" ? ["#ffcc00"] : ["#41d8e6"],
        colors: ["#fff"],
      };

      return {
        ...defaultSize,
        ...markerColorStyle,
      };
    },
    careScatterChartOptions() {
      let strokeColor;
      if (chartType === "water") {
        strokeColor = ["#41d8e6"];
      }
      if (chartType === "pee") {
        strokeColor = ["#ffcc00"];
      }

      const markerColorStyle = {
        strokeColor: strokeColor,
        colors: ["#fff"],
      };
      return {
        ...defaultSize,
        ...markerColorStyle,
      };
    },
    historyLineChartOptions() {
      const markerColorStyle = {
        strokeColor: ["#A7A7A7"],
        colors: ["#fff"],
      };
      return {
        ...defaultSize,
        ...markerColorStyle,
      };
    },
  };
};

// 🎯 grid options
export const gridOptions = () => {
  const grid = {
    yaxis: {
      lines: {
        show: true,
      },
    },
    strokeDashArray: 2,
  };
  return grid;
};

// 🎯 annotation optioins => not used in current(221129)
// export const annotationsOptions = (yaxisValue) => {
//   const annotations = {
//     position: "back",
//     yaxis: [
//       {
//         y: yaxisValue,
//         y2: null,
//         strokeDashArray: [2, 3],
//         borderColor: "#41D8E6",
//       },
//     ],
//   };
//   return annotations;
// };

// 🎯 xaxis options
export const xaxisOptions = (chartLables) => {
  // console.log(chartLables);
  const min = chartLables[0];
  const max = chartLables[chartLables.length];
  const len = chartLables.length;
  // console.log(len > 20);
  const xaxis = {
    tooltip: {
      enabled: false,
    },
    axisBorder: {
      show: false,
    },
    min: min,
    max: max,
    tickAmount: len > 12 ? 7 : 19,
    categories: chartLables,
    labels: {
      rotate: 0,
      rotateAlways: false,
      style: {
        colors: ["#646464"],
        fontSize: "14px",
        cssClass: "apexcharts-yaxis-label",
      },
    },
  };
  return xaxis;
};

export const scatterChartXaxisOption = (chartLables) => {
  const xaxis = {
    tooltip: {
      enabled: false,
    },
    axisBorder: {
      show: false,
    },
    categories: chartLables,
    labels: {
      rotate: 0,
      rotateAlways: false,
      style: {
        colors: ["#646464"],
        fontSize: "14px",
        cssClass: "apexcharts-yaxis-label",
      },
    },
  };
  return xaxis;
};

// 🎯 scatter chart yaxis options
export const scatterChartYaxisOption = (type) => {
  const yaxis = {
    title: {
      text: i18n.t("time"),
      offsetY: 85,
      offsetX: 20,
      style: {
        color: "#a7a7a7",
        fontSize: "12px",
        fontWeight: 400,
      },
    },
    min: 0,
    max: 24,
    logBase: 1,
    offsetX: 10,
    forceNiceScale: true,
    labels: {
      formatter: (value) => {
        if (value === 0) {
          return "";
        }
        return String(value).toString(0, 2);
      },
      style: {
        colors: ["#646464"],
        fontSize: "14px",
        cssClass: "apexcharts-yaxis-label",
      },
    },
  };
  return yaxis;
};