<template>
  <div>
    <div class="chart">
      <apexchart
        width="100%"
        height="250"
        type="scatter"
        :options="chartOptions"
        :series="series"
        ref="scatterChart"
      ></apexchart>
      <div class="scatter-chart__tooltip">
        <div class="tooltip__title" :class="`${tooltipTitleColor}-txt`">
          <span>{{ tooltipTitle }}{{ unit }}</span>
        </div>
        <div class="tooltip__date">{{ tooltipDate }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  chartOptions,
  scatterChartYaxisOption,
  markerOptions,
  gridOptions,
  scatterChartXaxisOption,
} from "./chartOptions.js";

export default {
  props: {
    page: String,
    type: String,
    weekDayData: Array,
    scatterData: Array,
  },
  data() {
    return {
      tooltipTitle: "",
      tooltipDate: "",
      tooltipTitleColor: "",
      chartData: [],
      chartLabels: [],
      series: [
        {
          name: "Series 1",
          data: [],
        },
      ],
      chartOptions: {},
    };
  },
  computed: {
    unit() {
      if (this.page === "water") {
        return "ml";
      } else {
        return this.$i18n.t("times");
      }
    },
  },
  methods: {
    setInitTooltpPosition: function () {
      return {
        xcrosshairsPosition: function (xPositionValue) {
          const xcrosshairs = document.querySelectorAll(".apexcharts-xcrosshairs");

          xcrosshairs.forEach((line) => {
            line.setAttribute("x1", `${xPositionValue}`);
            line.setAttribute("x2", `${xPositionValue}`);
            line.setAttribute("y1", `-13`);
          });
        },
        setTooltipInitPosition: function (xPositionValue) {
          const tooltip = document.querySelectorAll(".scatter-chart__tooltip");

          tooltip.forEach((item) => {
            item.style.left = `${xPositionValue - 10}px`;
          });
        },
      };
    },

    setTooltipActivate: function () {
      return {
        setTooltipPosition: function (seriesXvalue, dataPointIndex) {
          const tooltip = document.querySelectorAll(".scatter-chart__tooltip");

          tooltip.forEach((item) => {
            item.style.display = "block";
            if (seriesXvalue[dataPointIndex] > 250) {
              item.style.left = `${seriesXvalue[dataPointIndex] - 10}px`;
            } else if (seriesXvalue[dataPointIndex] < 5) {
              item.style.left = `${seriesXvalue[dataPointIndex] + 10}px`;
            } else {
              item.style.left = `${seriesXvalue[dataPointIndex]}px`;
            }
          });
        },
        setTooltipContent: (dateIndex) => {
          // console.log(dateIndex);
          const date = this.weekDayData[dateIndex].value;
          this.tooltipTitle = date.toLocaleString("ko-KR");
          this.tooltipDate = this.weekDayData[dateIndex].createdAt.slice(2);
        },
      };
    },

    setChartOptions: function () {
      const type = 1;
      const chart = chartOptions();
      const markers = markerOptions(this.page);
      const setInitTooltipPosition = this.setInitTooltpPosition();
      const setTooltipActivate = this.setTooltipActivate();

      return {
        chart: {
          ...chart.careScatterChartOption(),
        },
        dataLabels: {
          enabled: false,
        },
        markers: { ...markers.careScatterChartOptions() },
        xaxis: {
          tickAmount: 6,
          ...scatterChartXaxisOption(),
        },
        yaxis: {
          ...scatterChartYaxisOption(),
        },
        grid: { ...gridOptions() },
        tooltip: {
          enabled: true,
          custom: function ({ series, seriesIndex, dataPointIndex, w }) {
            const seriesXvalue = w.globals.seriesXvalues[0];
            setTooltipActivate.setTooltipPosition(seriesXvalue, dataPointIndex);
            let seriesXObj = w.globals.seriesX[0];
            const seriesArr = seriesXObj.filter((item, seriesIndex) => {
              const idx = seriesXObj.indexOf(item);

              if (idx === seriesIndex) {
                return item;
              }
            });
            const capturedSeriesXvalue = seriesXObj[dataPointIndex];
            const getXlabelIndex = seriesArr.reverse();
            setTooltipActivate.setTooltipContent(getXlabelIndex.indexOf(capturedSeriesXvalue));

            const xcrosshairs = document.querySelectorAll(".apexcharts-xcrosshairs");

            xcrosshairs.forEach((line) => {
              line.style.opacity = 1;
              line.setAttribute("y1", `-13`);
            });

            return "";
          },
        },
      };
    },

    updateSeriesScatter: function (chartData) {
      this.$refs.scatterChart.updateSeries(
        [
          {
            name: "History",
            data: chartData,
          },
        ],
        false,
        true,
      );
    },

    makeScatterChart: function () {
      this.chartOptions = this.setChartOptions();
      this.updateSeriesScatter(this.scatterData);
    },
  },
  mounted() {
    // console.log(this.weekDayData);
    // console.log(this.scatterData);
    this.makeScatterChart();
  },
};
</script>

<style lang="scss" scoped>
.chart {
  position: relative;
  padding: 0px 30px 0 15px;
}

.scatter-chart__tooltip {
  display: none;
  min-width: 80px;
  position: absolute;
  top: -30px;
  background: #ededed;
  border-radius: 5px;
  padding: 0 10px;
}

.tooltip__title {
  font-size: 20px;
  font-weight: bold;
  img {
    width: 8px;
    margin-left: 4px;
  }
}

.scatter-chart__tooltip {
  position: absolute;
  top: -30px;
  background: #ededed;
  border-radius: 5px;
  padding: 5px 8px;
}

.tooltip__title {
  font-size: 22px;
  line-height: 20px;
  font-weight: bold;
}

/* 영문일 때 GilroyBold 폰트 적용 */
:global(html[lang="en"]) .tooltip__title,
:global(html[lang="en-US"]) .tooltip__title {
  font-family: GilroyBold !important;
}

.tooltip__date {
  font-family: GilroyMedium;
  font-size: 13px;
  line-height: 12px;
  padding-top: 4px;
  color: #646464;
}

::v-deep .normal-txt {
  color: #00bb00;
}

::v-deep .warning-txt {
  color: #ffcc00 !important;
}

::v-deep .caution-txt {
  color: #ff6600;
}

::v-deep .danger-txt {
  color: #646464;
}

::v-deep .good-txt {
  color: #41d8e6;
}

::v-deep text {
  font-family: GilroyMedium !important;
  color: #a7a7a7;
}

::v-deep .apexcharts-yaxis-label {
  // font-size: 16px;
  font-weight: 500;
  font-family: GilroyMedium !important;
}

::v-deep .apexcharts-xcrosshairs {
  stroke-dasharray: 0 !important;
  stroke: #ededed !important;
}
</style>
