<template>
  <div class="height-form">
    <v-text-field
      color="#41d8e6"
      v-model="height"
      :placeholder="this.$i18n.t('profile_description')"
      :persistent-placeholder="true"
      type="number"
      inputmode="numeric"
      :error-messages="heightError"
    >
      <template v-slot:label>
        <div class="text-field-label">{{ $t("profile_height") }}</div>
      </template>
    </v-text-field>
  </div>
</template>

<script>
export default {
  props: {
    initialHeight: Number,
  },
  data() {
    return {
      height: null,
      validHeight: false,
      heightError: "",
    };
  },
  methods: {
    heightValidation(height) {
      if (Number(height) > 39 && Number(height) < 301) return true;
      return false;
    },
  },
  watch: {
    height(newVal) {
      // console.log("valid check");
      if (this.height.length !== 0 && this.heightValidation(newVal)) {
        this.heightError = "";
        this.validHeight = true;
      } else {
        this.heightError = this.$i18n.t("invalid");
        this.validHeight = false;
      }
      const height = { height: this.height, valid: this.validHeight };
      this.$emit("heightInputHandler", height);
    },
  },
  mounted() {
    this.height = this.initialHeight || null;
  },
};
</script>

<style lang="scss" scoped>
.height-form {
  width: 100%;
  height: 90px;
}

.v-text-field input {
  padding: 0;
}
::v-deep v-text-field input {
  font-size: 18px;
}
.text-field-label {
  font-size: 18px;
  font-weight: 500;
  // font-family: Noto Sans KR;
}

::v-deep .v-input {
  font-family: GilroyMedium !important;
  margin-bottom: 20px;
  font-size: 20px;
  letter-spacing: -0.03em;
}

::v-deep .v-text-field input {
  padding: 10px 0 5px !important;
  font-size: 20px;
}

::v-deep .v-text-field .v-label {
  top: -27px !important;
  font-weight: 500;
}

::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    letter-spacing: -0.03em;
    padding: 0;
    font-weight: 400 !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
