<template>
  <div>
    <CompleteAlert
      class="modal"
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-show="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
    <div class="dp-30">
      <div class="dropdown__wrapper">
        <vue-country-dropdown
          @onSelect="onSelect"
          :disabledFetchingCountry="false"
          :preferredCountries="['KR', 'US', 'GB']"
          :enabledFlags="true"
          :enabledCountryCode="true"
          :showNameInput="true"
        />
      </div>
      <v-text-field
        v-model="phone"
        :label="this.$i18n.t('input_phone')"
        color="#41D8E6"
        type="tel"
        maxlength="15"
        :error-messages="findErrorMsg"
        @keydown.space.prevent
        @focus="focusInputHandler"
        @blur="blurInputHandler"
        required
      >
      </v-text-field>
      <slot></slot>
      <template v-if="showTextField">
        <v-text-field
          v-model="authNumber"
          :label="this.$i18n.t('verification_number')"
          color="#41D8E6"
          type="number"
          inputmode="numeric"
          maxlength="6"
          :rules="validAuthNum"
          :error-messages="authErrorMsg"
          required
        >
          <template v-slot:append>
            <div class="v-timer">{{ minutes }}:{{ seconds }}</div>
            <div class="v-request-btn" @click="reSendSms">
              <button>{{ $t("request_btn") }}</button>
            </div>
          </template>
        </v-text-field>
      </template>
      <v-btn
        class="main-large-btn"
        elevation="0"
        color="#41D8E6"
        :disabled="!isPhoneNumberValid"
        type="submit"
        @click="nextPhaseHandler"
      >
        {{ buttonText }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import API from "@/api/auth/index.js";
import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import VueCountryDropdown from "vue-country-dropdown";
import webview from "../../service/webview";

export default {
  components: {
    CompleteAlert,
    ErrorModal,
    VueCountryDropdown,
  },
  data() {
    return {
      isSns: false,
      phone: this.$store.state.join.phone || "",
      authNumber: "",
      timer: null,
      totalTime: 3 * 60,
      showTextField: false,
      isAlert: true,
      showCompleteAlert: false,
      showErrModal: false,
      content: this.$i18n.t("send_text_modal_msg"),
      alert: this.$i18n.t("error_timeout"),
      error: this.$i18n.t("error_modal_msg"),
      mobileNumberRule: [
        // (v) => /^[0-9]/g.test(v) || "숫자만 기입 가능합니다.",
        // (v) => /01[016789][^0][0-9]{2,4}[0-9]{5,11}/.test(v) || "올바른 양식으로 입력해주세요.",
      ],
      validAuthNum: [(v) => /^[0-9]{1,6}$/.test(v) || this.$i18n.t("invalid_error")],
      findErrorMsg: "",
      authErrorMsg: "",
      selected: "",
      isKo: false,
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      deviceId: "",
    };
  },

  computed: {
    buttonText() {
      if (this.showTextField) {
        return this.$i18n.t("next_btn");
      } else {
        return this.$i18n.t("send_btn");
      }
    },
    isPhoneNumberValid() {
      return this.phone.length > 5 && this.phone.length < 16;
    },
    minutes() {
      const minutes = Math.floor(this.totalTime / 60);
      return this.padTime(minutes);
    },
    seconds() {
      const seconds = this.totalTime - this.minutes * 60;
      return this.padTime(seconds);
    },
  },
  methods: {
    onSelect({ name, iso2, dialCode }) {
      // console.log(name, iso2, dialCode);
      this.isKo = dialCode === "82" ? true : false;
      this.selected = `+${dialCode}`;
    },

    nextPhaseHandler() {
      if (!this.showTextField) {
        this.sendSms();
        // this.showTextField = true;
        // this.showCompleteAlert = true;
      } else {
        this.checkAuth();
      }
      // this.$emit("nextPhaseHandler", 1);
    },
    isConfirmed() {
      // this.$emit("isConfirmed", true);
      this.isAlert ? (this.showCompleteAlert = false) : this.$router.push("/");
    },
    isClicked() {
      this.showErrModal = false;
    },
    async sendSms() {
      const koPhoneNum = this.phone.slice(1);
      // console.log(koPhoneNum);
      const phoneNumber = {
        countryNumber: this.selected,
        phone: this.isKo ? koPhoneNum : this.phone,
        device: this.deviceId,
      };

      try {
        const response = await API.fetchSendSms(phoneNumber);
        // console.log(response);
        if (response.status === 201) {
          this.showTextField = true;
          this.showCompleteAlert = true;
          this.startTimer();
          this.totalTime = 3 * 60;
        }
      } catch (error) {
        if (error) {
          console.error(error);
        }
      }
    },
    async checkAuth() {
      // this.$emit("nextPhaseHandler", 1);
      const koPhoneNum = this.phone.slice(1);
      // console.log(koPhoneNum);
      try {
        const authNumbers = {
          countryNumber: this.selected,
          phone: this.isKo ? koPhoneNum : this.phone,
          code: this.authNumber,
          device: this.deviceId,
        };

        const response = await API.fetchVerifyAuthCode(authNumbers);
        // console.log(response);
        if (response.status === 200) {
          this.$store.commit("GET_PHONE", this.phone);
          this.$store.commit("GET_COUNTRY", this.selected);
          this.isSns ? this.$emit("nextPhaseHandler", 3) : this.$emit("nextPhaseHandler", 1);
        }
      } catch (error) {
        console.log(error.response.data);
        this.showErrModal = true;
        this.authErrorMsg = this.$i18n.t("invalid_error");
      }
    },
    reSendSms() {
      this.totalTime = 3 * 60;
      this.sendSms();
      this.showTextField = true;
      this.showCompleteAlert = true;
    },
    countdown() {
      if (this.totalTime >= 1) {
        this.totalTime--;
      } else {
        this.totalTime = 0;
        this.resetTimer;
        this.isAlert = false;
        this.showCompleteAlert = true;
        this.content = this.$i18n.t("error_timeout");
        this.alertMessage = this.$i18n.t("error_timeout");
      }
    },
    startTimer() {
      clearInterval(this.timer);
      this.timer = setInterval(() => this.countdown(), 1000);
    },
    padTime(time) {
      return (time < 10 ? "0" : "") + time;
    },
    focusInputHandler() {
      // console.log("focus");
    },
    blurInputHandler() {
      // console.log("blur");
    },

    getDeviceId() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const deviceId = Webview.getDeviceId();

      console.log(deviceId);
      this.$nextTick(() => this.listener());
    },

    listener() {
      this.isIos
        ? window.addEventListener("message", this.handleMessage)
        : document.addEventListener("message", this.handleMessage);
    },
    handleMessage(e) {
      const data = JSON.parse(e.data);
      console.log(data);
      this.deviceId = data.payload.result;
    },
  },

  mounted() {
    this.getDeviceId();
    Object.keys(this.$route.params).length === 0 ? (this.isSns = false) : (this.isSns = true);
  },

  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
    document.removeEventListener("message", this.handleMessage);
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 20px;
}

::v-deep .v-menu__content .theme--light .menuable__content__active {
  padding: 0 !important;
  position: absolute !important;
  box-shadow: none !important;
  top: 310px !important;
}

::v-deep .v-list-item {
  &:hover {
    background-color: #c9f4f8 !important;
  }
}

::v-deep .v-list-item__content {
  text-align: left;
  color: #000000 !important;
  caret-color: #41d8e6 !important;
}
::v-deep .v-list-item--active {
  background-color: #c9f4f8 !important;
}

.modal {
  text-align: left;
}

.dropdown__wrapper {
  margin-bottom: 20px;
}

.country {
  width: 60%;
  font-weight: 500;
}

.main-large-btn {
  margin-top: 3px;
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

.v-timer {
  font-size: 18px;
  letter-spacing: -0.03em;
  color: #000 !important;
  display: flex;
  align-items: center;
}
.v-request-btn {
  font-size: 16px;
  line-height: 20px;
  text-indent: 3px;

  button {
    color: #41d8e6;
    white-space: nowrap;
    line-height: 14px;
    padding-bottom: 5px;
    letter-spacing: -0.03em;
  }
}

::v-deep .vue-country-select .dropdown-list {
  max-height: 50vh !important;
  width: 100%;
  border: 1px solid #41d8e6;
  border-radius: 0 0 5px 5px;
  font-size: 16px;
  color: #000000;
  left: 0;
}

::v-deep .vue-country-select {
  border: none;
  border-bottom: 1px solid #a7a7a7 !important;
  border-radius: 0px !important;
  display: flex;
}

::v-deep .vue-country-select .current {
  font-size: 16px;
}

::v-deep .vue-country-select .dropdown {
  width: 100%;
  padding: 10px 0;
}

::v-deep .vue-country-select:focus-within {
  box-shadow: none !important;
  border-color: #41d8e6;
}

::v-deep .vue-country-select .dropdown:hover {
  background-color: transparent;
  border: none !important;
}

::v-deep .vue-country-select .dropdown-item {
  padding: 5px 3px;
}

::v-deep .vue-country-select .dropdown-item.highlighted {
  background-color: #c9f4f8;
}

::v-deep .theme--light.v-text-field > .v-input__control > .v-input__slot:before {
  border-color: #a7a7a7 !important;
}
</style>
