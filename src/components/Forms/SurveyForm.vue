<template>
  <div>
    <div class="option-comment">
      <span>{{ subTitleValue }}</span>
    </div>
    <div class="form__wrapper">
      <v-checkbox
        v-for="(value, idx) in survey.choice"
        :key="idx"
        @click="checkBoxHandler(answers[survey.id].choice)"
        v-model="answers[survey.id].choice"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        color="#41D8E6"
        :value="idx + 1"
        :multiple="isMultiple"
      >
        <template v-slot:label>
          <div class="checkbox-label">
            {{ $t(value.text) }}
          </div>
        </template>
      </v-checkbox>
      <v-checkbox
        v-model="purposeInput"
        v-if="currentStateIdx === 0"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        color="#41D8E6"
      >
        <template v-slot:label>
          <div class="checkbox-label">{{ $t("input_etc") }}</div>
        </template>
      </v-checkbox>
      <v-textarea
        :placeholder="this.$i18n.t('etc_placeholder')"
        v-model="purposeEtc"
        v-if="purposeInput && currentStateIdx === 0"
        height="100"
        outlined
        name="input-7-4"
        maxlength="20"
      ></v-textarea>
      <!-- @change="purposeEtcHandler" -->
      <!-- @change="diseaseInputHandler"  -->
      <v-checkbox
        v-model="diseaseInput"
        v-if="currentStateIdx === 2"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        color="#41D8E6"
      >
        <template v-slot:label>
          <div class="checkbox-label">{{ $t("input_etc") }}</div>
        </template>
      </v-checkbox>
      <v-textarea
        :placeholder="this.$i18n.t('etc_placeholder')"
        v-model="diseaseEtc"
        v-if="diseaseInput && currentStateIdx === 2"
        height="100"
        outlined
        name="input-7-4"
        maxlength="20"
      ></v-textarea>
    </div>
    <div class="fixed-btn">
      <v-btn
        color="#41D8E6"
        class="main-large-btn"
        @click="nextPhaseHandler"
        elevation="0"
        :disabled="!healthInfoValid"
      >
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import API from "@/api/survey/index.js";

export default {
  props: {
    currentStateIdx: Number,
    checkedData: Object,
    questions: Array,
  },
  data() {
    return {
      subjectId: 0,
      customReason: "",
      purposeInput: false,
      diseaseInput: false,
      healthInfoValid: false,
      // questions: {},
      questionsArr: [],
      survey: {},
      isMultiple: false,
      purposeEtc: "",
      diseaseEtc: "",
      answers: [
        { questionId: 0, choice: [] },
        { questionId: 1, choice: [] },
        { questionId: 2, choice: [] },
        { questionId: 3, choice: [] },
        { questionId: 4, choice: [] },
        { questionId: 5, choice: [] },
        { questionId: 6, choice: [] },
        { questionId: 7, choice: [] },
        { questionId: 8, choice: [] },
      ],
      labelsIndex: 0,
    };
  },
  watch: {
    questions() {
      this.getSurveyCheckedData();
    },
    currentStateIdx(newVal) {
      // console.log("currentStateIdx", newVal);
      localStorage.setItem("surveyIdx", newVal);
      this.survey = this.questions[newVal];
      this.isMultiple = this.questions[newVal].isMultipleChoice;
      this.answers[newVal + 1].choice.length !== 0 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);

      // console.log(this.answers[newVal + 1].choice.length);
      // console.log(newVal, this.survey.isMultipleChoice, this.isMultiple);
      // this.healthInfoValid = false;
    },
    purposeInput(newVal) {
      // console.log(newVal);
      if (newVal) this.purposeEtc > 2 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
    },
    diseaseInput(newVal) {
      // console.log(newVal);
      if (newVal) this.diseaseEtc > 2 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
    },
    purposeEtc(newVal) {
      // console.log("purposeEtc", newVal.length > 2);
      newVal.length > 2 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
    },
    diseaseEtc(newVal) {
      // console.log("purposeEtc", newVal.length > 2);
      newVal.length > 2 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
    },
    reloadSurveyData(newVal) {
      // console.log(newVal);
    },
  },
  computed: {
    subTitleValue() {
      if (this.isMultiple) {
        return this.$i18n.t("duplicate_check");
      } else {
        return this.$i18n.t("single_check");
      }
      // return this.$i18n.t("single_check");
    },
  },
  methods: {
    getSurveyData() {
      this.survey = this.questions[this.currentStateIdx];
      this.isMultiple = this.questions[this.currentStateIdx].isMultipleChoice;
      // console.log("survey:", this.survey, this.isMultiple);
      this.$nextTick(() => this.getSurveyCheckedData());
    },
    // TODO: Survey.vue에서 호출하고 props로 내려주기
    getSurveyCheckedData() {
      if (this.checkedData.length !== 0) {
        try {
          const questionArr = this.checkedData.resultAnswers.question;
          this.questionsArr = this.checkedData.resultAnswers.question;
          // console.log(this.checkedData);
          this.answers.map((ans) => {
            // console.log(ans);
            const q = questionArr.find((q) => q.id === ans.questionId);
            // console.log(q);
            if (q) {
              switch (q.id) {
                case 1:
                  return (ans.choice = q.choice.map((c) => c.id));
                case 2:
                  return (ans.choice = Number(q.choice.map((c) => c.id - 5).join()));
                case 3:
                  return (ans.choice = q.choice.map((c) => c.id - 9));
                case 4:
                  return (ans.choice = Number(q.choice.map((c) => c.id - 19).join()));
                case 5:
                  return (ans.choice = Number(q.choice.map((c) => c.id - 23).join()));
                default:
                  return;
              }
            }
            return ans;
          });
          // console.log(this.answers);
          // console.log("data question obj arr:", this.questionsArr);
          if (questionArr[0]?.textAnswer?.[0]?.text) {
            this.purposeInput = true;
            this.purposeEtc = questionArr[0].textAnswer[0].text;
          }
          if (questionArr[4]?.textAnswer?.[0]?.text) {
            this.diseaseInput = true;
            this.diseaseEtc = questionArr[4].textAnswer[0].text;
          }
        } catch (e) {
          console.log(e);
        }
      }
    },

    checkBoxHandler(checkedArr) {
      // console.log(checkedArr);
      // this.smoking = checkedArr;
      if (this.survey.choice.isMultiple) {
        checkedArr.length !== 0 ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
      } else {
        checkedArr !== null ? (this.healthInfoValid = true) : (this.healthInfoValid = false);
      }
    },

    // TODO: Refactoring
    // labelsIndex별 나눠진 함수 하나로 통일
    // post method, delete method 하나의 함수로 & 스코프 밖으로 빼기

    async nextPhaseHandler() {
      let value = this.currentStateIdx;
      this.labelsIndex = value += 1;
      const subjectId = Number(this.subjectId);
      const surveyData = [
        {
          questionId: this.labelsIndex,
          choiceAnswers: this.answers[this.labelsIndex].choice,
        },
      ];
      // console.log(surveyData);

      // 다음버튼 클릭 시 console.log(this.answers[1].choice === localStorage 값이랑 비교)

      // 앱 사용목적(multiple & 주관식)
      if (this.labelsIndex === 1) {
        // console.log(this.labelsIndex);
        this.purpose = this.answers;
        this.purposeEtc !== "" && this.questionsArr[this.labelsIndex - 1]?.textAnswer[0]?.text !== this.purposeEtc
          ? (surveyData[0].textAnswer = this.purposeEtc)
          : null;
        surveyData[0].choiceAnswers.length === 0 ? delete surveyData[0].choiceAnswers : null;
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);
        // console.log("-----------", this.answers[this.labelsIndex].choice);
        // console.log(this.questionsArr[this.labelsIndex - 1]?.textAnswer[0]?.text !== this.purposeEtc);

        const choicesAnswer = this.answers[this.labelsIndex].choice;
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice;
        if (checkedArr !== undefined) {
          const addedArr = choicesAnswer.filter((choice) => !checkedArr.some((item) => item.id === choice));
          const deletedArr = checkedArr
            .filter((item) => !choicesAnswer.includes(item.id))
            .map((item) => item.choiceAnswer[0].id);

          // console.log("Added:", addedArr);
          // console.log("Deleted:", deletedArr);
          if (addedArr.length !== 0) {
            surveyData[0].choiceAnswers = addedArr;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              // console.log(survey);
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                if (deletedArr.length !== 0) {
                  // delete
                  try {
                    const deleteData = {
                      subjectId: subjectId,
                      choiceAnswerId: deletedArr,
                    };
                    const { data, status } = await API.deleteSurveyData(deleteData);
                    if (status === 204) {
                      this.$emit("nextPhaseHandler", this.labelsIndex);
                    }
                  } catch (e) {
                    console.log(e);
                  }
                }
                this.$emit("nextPhaseHandler", this.labelsIndex);
              }
            } catch (e) {
              console.log(e);
            }
          } else if (deletedArr.length !== 0 && addedArr.length === 0) {
            // delete
            try {
              const deleteData = {
                subjectId: subjectId,
                choiceAnswerId: deletedArr,
              };
              const { data, status } = await API.deleteSurveyData(deleteData);
              if (status === 204) {
                this.$emit("nextPhaseHandler", this.labelsIndex);
              }
            } catch (e) {
              console.log(e);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("just first post");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (e) {
            console.log(e);
          }
        } // this.$store.commit("GET_HEALTH_INFO_PURPOSE", this.answers);
        this.$emit("nextPhaseHandler", this.labelsIndex);
      }

      // 평소 운동량
      if (this.labelsIndex === 2) {
        // console.log(surveyData[0].choiceAnswers);
        surveyData[0].choiceAnswers = [surveyData[0].choiceAnswers + 5];
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);

        // console.log("-----------------------", this.answers[this.labelsIndex].choice);
        // console.log(this.questionsArr[this.labelsIndex - 1]?.choice[0]);

        const choicesAnswer = this.answers[this.labelsIndex].choice + 5;
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice[0];
        if (checkedArr !== undefined) {
          // console.log("survey modify");
          const removedItem = checkedArr.id !== choicesAnswer ? [checkedArr.choiceAnswer[0].id] : [];
          const addedItem = checkedArr.id !== choicesAnswer ? [choicesAnswer] : [];
          // console.log(removedItem, addedItem);

          if (addedItem.length !== 0) {
            // 단수선택 항목은 새로운 선택이 있으면 반드시 삭제도 존재
            surveyData[0].choiceAnswers = addedItem;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                // delete
                try {
                  const deleteData = {
                    subjectId: subjectId,
                    choiceAnswerId: removedItem,
                  };
                  const { data, status } = await API.deleteSurveyData(deleteData);
                  if (status === 204) {
                    this.$emit("nextPhaseHandler", this.labelsIndex);
                  }
                } catch (e) {
                  console.log(e);
                }
              }
            } catch (e) {
              console.log(e);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("first survey");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              const choicesData = surveyData[0].choiceAnswers;
              // console.log(choicesData);
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (e) {
            console.log(e);
          }
        }
        this.$emit("nextPhaseHandler", this.labelsIndex);
      }

      // 만성 질환(multiple & 주관식)
      if (this.labelsIndex === 3) {
        surveyData[0].choiceAnswers = surveyData[0].choiceAnswers.map((choiceId) => choiceId + 9);
        this.diseaseEtc !== "" ? (surveyData[0].textAnswer = this.diseaseEtc) : null;
        surveyData[0].choiceAnswers.length === 0 ? delete surveyData[0].choiceAnswers : null;
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);

        // console.log("-----------------------", this.answers[this.labelsIndex].choice);
        // console.log(this.questionsArr[this.labelsIndex - 1]?.choice);

        // this.answers[this.labelsIndex].choice.map((i) => console.log(i));

        const choicesAnswer = this.answers[this.labelsIndex].choice.map((i) => i + 9);
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice;
        // console.log(checkedArr);
        if (checkedArr !== undefined) {
          const addedArr = choicesAnswer.filter((choice) => !checkedArr.some((item) => item.id === choice));
          const deletedArr = checkedArr
            .filter((item) => !choicesAnswer.includes(item.id))
            .map((item) => item.choiceAnswer[0].id);

          // console.log("Added:", addedArr);
          // console.log("Deleted:", deletedArr);
          if (addedArr.length !== 0) {
            surveyData[0].choiceAnswers = addedArr;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                if (deletedArr.length !== 0) {
                  // delete
                  try {
                    const deleteData = {
                      subjectId: subjectId,
                      choiceAnswerId: deletedArr,
                    };
                    const { data, status } = await API.deleteSurveyData(deleteData);
                    if (status === 204) {
                      this.$emit("nextPhaseHandler", this.labelsIndex);
                    }
                  } catch (e) {
                    console.log(e);
                  }
                }
                this.$emit("nextPhaseHandler", this.labelsIndex);
              }
            } catch (e) {
              console.log(e);
            }
          } else if (deletedArr.length !== 0 && addedArr.length === 0) {
            // delete
            try {
              const deleteData = {
                subjectId: subjectId,
                choiceAnswerId: deletedArr,
              };
              const { data, status } = await API.deleteSurveyData(deleteData);
              if (status === 204) {
                this.$emit("nextPhaseHandler", this.labelsIndex);
              }
            } catch (e) {
              console.log(e);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("first survey post");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (e) {
            console.log(e);
          }
        }
        this.$emit("nextPhaseHandler", this.labelsIndex);
      }

      // 음주량
      if (this.labelsIndex === 4) {
        surveyData[0].choiceAnswers = [surveyData[0].choiceAnswers + 19];
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);

        const choicesAnswer = this.answers[this.labelsIndex].choice + 19;
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice[0];
        if (checkedArr !== undefined) {
          // console.log("survey modify");
          const removedItem = checkedArr.id !== choicesAnswer ? [checkedArr.choiceAnswer[0].id] : [];
          const addedItem = checkedArr.id !== choicesAnswer ? [choicesAnswer] : [];
          // console.log(removedItem, addedItem);
          if (addedItem.length !== 0) {
            // 단수선택 항목은 새로운 선택이 있으면 반드시 삭제도 존재
            surveyData[0].choiceAnswers = addedItem;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                // delete
                try {
                  const deleteData = {
                    subjectId: subjectId,
                    choiceAnswerId: removedItem,
                  };
                  const { data, status } = await API.deleteSurveyData(deleteData);
                  if (status === 204) {
                    this.$emit("nextPhaseHandler", this.labelsIndex);
                  }
                } catch (e) {
                  console.log(e);
                }
              }
            } catch (e) {
              console.log(e);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("first post");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (e) {
            console.log(e);
          }
        }
      }

      // 흡연량
      if (this.labelsIndex === 5) {
        surveyData[0].choiceAnswers = [surveyData[0].choiceAnswers + 23];
        const survey = {
          subjectId: subjectId,
          answers: surveyData,
        };
        // console.log(survey);

        const choicesAnswer = this.answers[this.labelsIndex].choice + 23;
        const checkedArr = this.questionsArr[this.labelsIndex - 1]?.choice[0];
        if (checkedArr !== undefined) {
          const removedItem = checkedArr.id !== choicesAnswer ? [checkedArr.choiceAnswer[0].id] : null;
          const addedItem = checkedArr.id !== choicesAnswer ? [choicesAnswer] : [];
          // console.log(removedItem, addedItem);
          if (addedItem.length !== 0) {
            // 단수선택 항목은 새로운 선택이 있으면 반드시 삭제도 존재
            surveyData[0].choiceAnswers = addedItem;
            try {
              const survey = {
                subjectId: subjectId,
                answers: surveyData,
              };
              const { data, status } = await API.postSurveyData(survey);
              // console.log(data, status);
              if (status === 201) {
                // delete
                try {
                  const deleteData = {
                    subjectId: subjectId,
                    choiceAnswerId: removedItem,
                  };
                  const { data, status } = await API.deleteSurveyData(deleteData);
                  if (status === 204) {
                    this.$emit("nextPhaseHandler", this.labelsIndex);
                  }
                } catch (e) {
                  console.log(e);
                }
              }
            } catch (e) {
              console.log(e);
            }
          }
          // console.log("nothing changed");
          this.$emit("nextPhaseHandler", this.labelsIndex);
        } else {
          // console.log("first post");
          try {
            const { data, status } = await API.postSurveyData(survey);
            // console.log(data, status);
            if (status === 201) {
              this.$emit("nextPhaseHandler", this.labelsIndex);
            }
          } catch (e) {
            console.log(e);
          }
        }
      }
      this.healthInfoValid = false;
      localStorage.setItem("surveyIdx", this.labelsIndex);
    },
  },

  mounted() {
    // console.log("mounted");
    this.subjectId = localStorage.getItem("subjectId") || 0;
    this.getSurveyData();
    // console.log("currentStateIdx", this.currentStateIdx);
    // this.survey = this.answerLists[this.currentStateIdx];
    // console.log(this.survey);
    // console.log("subjectId:", this.subjectId);
    // const { health_info } = this.$store.state.join;
    // console.log(health_info.purpose);
    // this.purpose = health_info.purpose || [];
    // this.exercise = health_info.exercise;
    // this.chronic = health_info.chronic;
    // this.drinking = health_info.drinking;
    // this.smoking = health_info.smoking;
  },
};
</script>

<style lang="scss" scoped>
.form__wrapper {
  width: 100%;
  padding: 0 30px 30px;
  height: 65vh;
  overflow-y: scroll;
}
.v-input--selection-controls {
  margin: 0px !important;
  padding: 0px !important;
}

::v-deep .v-input__control {
  display: block !important;
  flex-wrap: nowrap !important;
}

::v-deep .v-messages {
  display: none !important;
}
.checkbox-label {
  font-weight: 400;
  font-size: 20px !important;
  color: black;
  padding-bottom: 5px;
  letter-spacing: -0.03em;
  line-height: 26px;
}

.diabetes-description {
  color: #646464 !important;
  font-size: 18px !important;
}

.option-comment {
  padding: 0 30px 20px 30px;
  text-align: left;
  letter-spacing: -0.03em;
  color: #646464;
}

.fixed-btn {
  position: absolute;
  bottom: 30px;
  width: 100%;
  left: 0;
  padding: 0 30px;
}
::v-deep textarea {
  line-height: 18px !important;
  font-size: 12px !important;
}

::v-deep .v-input__slot {
  align-items: flex-start;
}

::v-deep .v-icon.v-icon {
  padding-top: 5px;
}
</style>
