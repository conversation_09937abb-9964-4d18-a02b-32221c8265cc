<template>
  <div>
    <ConfirmModal
      v-show="showConfirmModal"
      :content="content"
      @isConfirmed="goToLogin"
      :btnText="this.$i18n.t('go_to_login')"
    />
    <div class="dp-30">
      <div class="terms-items__title">
        <div class="terms-items-title">{{ $t("terms_agreement") }}</div>
        <v-checkbox
          v-model="allAgree"
          class="all-agree pa-0 ma-0"
          color="#41D8E6"
          :label="this.$i18n.t('terms_all_agree')"
          hide-details
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          @change="checkTermsAll"
        ></v-checkbox>
      </div>

      <div class="terms-items__wrapper">
        <div class="terms__item" v-for="(item, idx) in labels" :key="idx">
          <div class="terms__item__container">
            <v-checkbox
              class="sub-agree pa-0 ma-0"
              color="#41D8E6"
              :value="`${item.value}`"
              hide-details
              off-icon="$check_box"
              on-icon="$check_box_inactive"
              v-model="checkedNames"
              @change="checkBoxHandler(idx)"
            >
              <template v-slot:label>
                <div class="checkbox-label__wrapper">
                  <div class="checkbox-label">{{ item.label }}</div>
                </div>
              </template>
            </v-checkbox>
          </div>
          <div class="detail">
            <span @click="openAgreeContentModal(idx)">{{ $t("read_more") }}</span>
          </div>
        </div>
      </div>
      <div class="terms-btn__wrapper">
        <v-btn
          color="#41D8E6"
          class="main-large-btn"
          :disabled="isAvailableSignup"
          @click="nextPhaseHandler"
          elevation="0"
        >
          {{ $t("next_btn") }}
        </v-btn>
      </div>
    </div>
    <ExternalUrlModal
      v-if="showAgreeContentModal"
      :content="contents"
      @isConfirmed="closeAgreeContentModal"
    ></ExternalUrlModal>
  </div>
</template>

<script>
import ExternalUrlModal from "@/components/Join/ExternalUrlModal.vue";
import ConfirmModal from "@/components/Common/ConfirmModal.vue";
import API from "@/api/auth/index.js";

export default {
  components: {
    ExternalUrlModal,
    ConfirmModal,
  },
  data() {
    return {
      contents: this.$i18n.t("terms_service_contents"),
      msgInputTerms: this.$i18n.t("join_terms_title"),
      allAgree: false,
      showConfirmModal: false,
      content: this.$i18n.t("join_modal_message"),
      labels: [
        {
          value: "service",
          label: this.$i18n.t("terms_service"),
        },
        {
          value: "privacy",
          label: this.$i18n.t("terms_personal_info"),
        },
        {
          value: "sensitive",
          label: this.$i18n.t("terms_sensitive"),
        },
        {
          value: "marketing",
          label: this.$i18n.t("terms_marketing"),
        },
      ],
      checkedNames: [],
      showAgreeContentModal: false,
      isAvailableSignup: true,
      type: "terms",
      snsId: 0,
      snsType: "",
    };
  },

  computed: {
    goTosurvey() {
      if (this.serviceAgree === "1" && this.privacyAgree === "1" && this.sensitiveAgree === "1") {
        return false;
      } else {
        return true;
      }
    },
  },
  watch: {
    checkedNames(newVal, oldVal) {
      // console.log(newVal);

      if (newVal.includes("service") && newVal.includes("sensitive") && newVal.includes("privacy")) {
        this.isAvailableSignup = false;
        // console.log("its alright");
      } else {
        this.isAvailableSignup = true;
        this.allAgree = false;
      }
    },
  },
  methods: {
    checkTermsAll() {
      if (this.allAgree) {
        this.checkedNames = ["marketing", "sensitive", "service", "privacy"];
      } else {
        this.checkedNames = [];
      }
    },

    // open external url
    checkBoxHandler(idx) {
      // console.log(idx);
    },
    openAgreeContentModal(idx) {
      // console.log(idx);
      if (idx === 0) {
        this.contents = this.$i18n.t("terms_service_contents");
      } else if (idx === 1) {
        this.contents = this.$i18n.t("terms_personal_info_contents");
      } else if (idx === 2) {
        this.contents = this.$i18n.t("terms_sensitive_contents");
      } else if (idx === 3) {
        this.contents = this.$i18n.t("terms_marketing_contents");
      }
      this.showAgreeContentModal = true;
    },
    closeAgreeContentModal() {
      this.showAgreeContentModal = false;
    },
    goToLogin() {
      this.$router.push("/");
    },
    isConfirmed() {
      this.showConfirmModal = false;
      // this.$emit("nextPhaseHandler", 5);
    },
    async nextPhaseHandler() {
      const terms = {
        usesAgree: true,
      };
      // this.showAlertModal = true;
      this.$store.commit("GET_MARKETING_ALLOW", terms);
      const inputData = this.$store.state.join;
      console.log(inputData.petDetail);
      if (this.snsType === "" || this.snsType === undefined) {
        const userData = {
          account: inputData.account,
          password: inputData.password,
          marketing: this.checkedNames.includes("marketing"),
          countryNumber: inputData.country,
          phone: inputData.phone,
          subjectTypeId: inputData.petDetail.typeId,
          sex: inputData.petDetail.sex,
          neutered: inputData.petDetail.neutered,
          nickname: inputData.petDetail.nickname,
          initialWeight: inputData.petDetail.initialWeight,
          birth: inputData.petDetail.birth,
        };
        inputData.petDetail.targetWeight === 0 || inputData.petDetail.targetWeight === null
          ? null
          : (userData.targetWeight = inputData.petDetail.targetWeight);

        inputData.petDetail.adoptionDate === null ||
        inputData.petDetail.adoptionDate === "" ||
        inputData.petDetail.adoptionDate === "null-null-null"
          ? null
          : (userData.adoptionDate = inputData.petDetail.adoptionDate);

        inputData.petDetail.registrationNumber === null || inputData.petDetail.registrationNumber === ""
          ? null
          : (userData.registrationNumber = inputData.petDetail.registrationNumber);

        console.log(userData);
        try {
          const response = await API.registerUser(userData);
          // const response = await API.registerSnsUser(userData);
          if (response.status === 201) {
            // join success modal open func
            this.showConfirmModal = true;
          }
          // this.$emit("nextPhaseHandler", 4);
        } catch (e) {
          console.log(e);
        }
      } else {
        const userData = {
          account: this.snsId,
          marketing: this.checkedNames.includes("marketing"),
          countryNumber: inputData.country,
          phone: inputData.phone,
          subjectTypeId: inputData.petDetail.typeId,
          sex: inputData.petDetail.sex,
          neutered: inputData.petDetail.neutered,
          nickname: inputData.petDetail.nickname,
          initialWeight: inputData.petDetail.initialWeight,
          birth: inputData.petDetail.birth,
        };
        inputData.petDetail.targetWeight === 0 || inputData.petDetail.targetWeight === null
          ? null
          : (userData.targetWeight = inputData.petDetail.targetWeight);

        inputData.petDetail.adoptionDate === null ||
        inputData.petDetail.adoptionDate === "" ||
        inputData.petDetail.adoptionDate === "null-null-null"
          ? null
          : (userData.adoptionDate = inputData.petDetail.adoptionDate);

        inputData.petDetail.registrationNumber === null || inputData.petDetail.registrationNumber === ""
          ? null
          : (userData.registrationNumber = inputData.petDetail.registrationNumber);
        // alert(userData.birth);
        try {
          // const response = await API.registerUser(userData);
          const response = await API.registerSnsUser(this.snsType, userData);
          if (response.status === 201) {
            // join success modal open func
            this.showConfirmModal = true;
          }
          // this.$emit("nextPhaseHandler", 4);
        } catch (e) {
          alert(e.response.data.error.message[0]);
          console.log(e);
        }
      }
    },
  },
  mounted() {
    this.snsId = String(this.$route.params.id);
    // console.log("if sns login params:");
    // console.log(this.snsId);
    this.snsType = this.$route.params.snsType;
    // console.log(this.snsType);
    this.email = this.$route.params.email;
    this.profileImg = this.$route.params.profileImg;
    // const inputData = this.$store.state.join;
    // console.log(inputData.country);
  },
};
</script>

<style lang="scss" scoped>
.terms-items__title {
  display: flex;
  justify-content: space-between;
  padding: 12px 0px;

  b {
    font-size: 14px;
  }
}

.terms__item {
  width: 100%;
  padding: 3px 0px;
  display: flex;
  justify-content: space-between;
}

.terms__item__container {
  width: 75%;
}

.checkbox-label__wrapper {
  padding-bottom: 7px;
  display: flex;
  justify-content: space-between;
  width: 100%;
  line-height: 12px;
}
.checkbox-label {
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  display: flex;
  align-items: center;
  letter-spacing: -0.03em;
  color: #646464;
}
.detail {
  font-size: 12px;
  color: #646464 !important;
  font-weight: 500;
  text-decoration: none;
  width: 24%;
}

.terms-btn__wrapper {
  padding-top: 25px;
}
.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

.terms-items__wrapper {
  background-color: #fff;
  padding: 10px 5px 10px 10px;
  border-radius: 5px;
  opacity: 0.8;
}

.auth-btn__wrapper {
  padding: 15px 0px;
}

::v-deep .sub-agree .v-label {
  // font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 17px;
  display: flex;
  align-items: center;
  letter-spacing: -0.03em;
  color: #a7a7a7;
}

::v-deep .all-agree .v-label {
  // font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  display: flex;
  align-items: center;
  letter-spacing: -0.03em;
  color: #a7a7a7;
}

::v-deep .v-input--selection-controls__input {
  margin-right: 0px !important;
  // top: -5px
}

::v-deep .v-input__slot {
  padding: 0 !important;
}
</style>
