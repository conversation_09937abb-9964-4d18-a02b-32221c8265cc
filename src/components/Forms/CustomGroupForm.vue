<template>
  <div class="input__wrapper">
    <div class="input-title">{{ title }}</div>
    <div>
      <v-checkbox
        v-for="group in groupArr"
        :key="group.id"
        :value="group.id"
        color="#41d8e6"
        off-icon="$check_box"
        on-icon="$check_box_inactive"
        v-model="selectedGroup"
        multiple
      >
        <!-- @click="userTypeHandler" -->
        <template v-slot:label>
          <div class="input-label">{{ group.text }}</div>
        </template>
      </v-checkbox>
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomGroupForm",
  props: {
    title: String,
    groupArr: Array,
    initialGroupId: Array,
  },
  data() {
    return {
      selectedGroup: [],
    };
  },
  watch: {
    selectedGroup(newVal) {
      // console.log(newVal);
      this.$emit("selectedGroup", newVal);
    },
  },
  mounted() {
    // console.log(this.groupArr);
    // console.log(this.initialGroupId);
    this.selectedGroup = this.initialGroupId !== null ? this.initialGroupId : [];
  },
};
</script>

<style lang="scss" scoped>
.input-title {
  width: 100%;
  text-align: left;
  color: #646464;
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 10px;
}

.input-label {
  color: #646464;
  font-size: 18px;
  line-height: 26px;
  margin-left: 10px;
}

::v-deep .v-input--selection-controls {
  padding: 0;
  margin: 0;
}
</style>
