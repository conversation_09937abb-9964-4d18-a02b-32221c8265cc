<template>
  <div class="type-input__wrapper">
    <div class="type-input__title">{{ $t("pet_type") }}</div>
    <div class="type-checkbox__items">
      <div class="type-checkbox__item">
        <v-checkbox
          color="#41d8e6"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          v-model="dog"
          @change="dogCheckBoxHandler"
        >
          <template v-slot:label>
            <div class="type-text">{{ $t("dog") }}</div>
          </template>
        </v-checkbox>
      </div>
      <div class="type-checkbox__item">
        <v-checkbox
          color="#41d8e6"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          v-model="cat"
          @change="catCheckBoxHandler"
        >
          <template v-slot:label>
            <div class="type-text">{{ $t("cat") }}</div>
          </template>
        </v-checkbox>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    initialType: String,
  },
  data() {
    return {
      dog: false,
      cat: false,
    };
  },
  methods: {
    dogCheckBoxHandler() {
      if (this.dog) {
        this.cat = false;
        this.$emit("petTypeHandler", "dog");
      }
    },
    catCheckBoxHandler() {
      if (this.cat) {
        this.dog = false;
        this.$emit("petTypeHandler", "cat");
      }
    },
    typeHandler(type) {
      if (type === "dog") {
        this.dog = true;
        this.cat = false;
        this.$emit("petTypeHandler", "dog");
      } else if (type === "cat") {
        this.cat = true;
        this.dog = false;
        this.$emit("petTypeHandler", "cat");
      } else {
        this.cat = false;
        this.dog = false;
      }
    },
  },
  mounted() {
    this.typeHandler(this.initialType);
  },
};
</script>

<style lang="scss" scoped>
.type-input__wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100px;
}
.type-checkbox__items {
  margin-bottom: 10px;
  display: flex;
  width: 100%;
}

.type-checkbox__item {
  width: 50%;
}
.type-input__title {
  width: 100%;
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  color: #646464;
  padding-bottom: 10px;
}

.type-text {
  color: #646464;
  font-size: 18px;
}

::v-deep .v-input--selection-controls {
  margin-top: 10px;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 5px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0 !important;
}

::v-deep .theme--light.v-messages {
  display: none;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
