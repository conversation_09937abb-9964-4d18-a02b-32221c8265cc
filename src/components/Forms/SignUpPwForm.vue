<template>
  <div>
    <div class="dp-30">
      <div>
        <v-text-field
          v-model="password"
          :label="this.$t('input_pwd')"
          color="#41D8E6"
          :type="showPassword1 ? 'text' : 'password'"
          :append-icon="showPassword1 ? '$eye_show' : '$eye_off'"
          @click:append="showPassword1 = !showPassword1"
          :error-messages="passwordErrorMsg"
          :placeholder="this.$i18n.t('join_pwd_placeholder')"
          required
        >
        </v-text-field>
      </div>
      <div>
        <v-text-field
          v-model="passwordCheck"
          :label="this.$i18n.t('join_pwd_check')"
          color="#41D8E6"
          :type="showPassword2 ? 'text' : 'password'"
          :append-icon="showPassword2 ? '$eye_show' : '$eye_off'"
          @click:append="showPassword2 = !showPassword2"
          :error-messages="passwordCheckErrorMsg"
          required
        >
        </v-text-field>
      </div>

      <v-btn
        class="main-large-btn"
        elevation="0"
        color="#41D8E6"
        type="submit"
        :disabled="!isAvaliablePwd"
        @click="nextPhaseHandler"
      >
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
// import { updateUserPw } from "@/api/user/index";

export default {
  data() {
    return {
      currentStateIdx: 0,
      currentState: false,
      showPassword1: false,
      showPassword2: false,
      password: this.$store.state.join.password || "",
      passwordCheck: this.$store.state.join.password || "",
      passwordErrorMsg: "",
      passwordCheckErrorMsg: "",
      isAvaliablePwd: false,
    };
  },
  computed: {
    showPasswordIconHandler() {
      if (this.showPassword2) {
        return "$eye_show";
      } else {
        return "$eye_off";
      }
    },
  },
  watch: {
    password(newVal) {
      if (this.passwordValidationCheck(newVal)) {
        this.passwordErrorMsg = "";
      } else {
        this.passwordErrorMsg = this.$i18n.t("join_pwd_error");
        this.isAvaliablePwd = false;
      }
    },
    passwordCheck(newVal) {
      if (this.password === newVal) {
        this.passwordCheckErrorMsg = "";
        // this.isAvaliablePwd = true;

        if (this.passwordValidationCheck(this.password)) {
          this.isAvaliablePwd = true;
        }
      } else {
        this.passwordCheckErrorMsg = this.$i18n.t("join_pwd_invalid");
        this.isPwd = false;
        this.isAvaliablePwd = false;
      }
    },
  },
  methods: {
    passwordValidationCheck(pwd) {
      // eslint-disable-next-line no-useless-escape
      return /^(?=.*[A-Za-z])(?=.*\d)(?=.*[~!@#$%^&*()<>_+=|\\€£¥.,‘“\-/;'"\[\]])[A-Za-z\d~!@#$%^&*()<>_+=|\\€£¥.,‘“\-/;'"\[\]]{8,16}$/.test(pwd);
    },

    nextPhaseHandler() {
      // this.passwordErrorMsg = "비밀번호를 입력하세요";
      this.passwordValidationCheck(this.passwordCheck) ? this.$store.commit("GET_PASSWORD", this.password) : "";
      this.$emit("nextPhaseHandler", 3);
    },
    focusInHandler() {
      if (this.password.length === 0 || this.passwordValidationCheck(this.password)) {
        this.passwordErrorMsg = "";
      } else {
        this.passwordErrorMsg = this.$i18n.t("join_pwd_invalid");
      }
    },
    focusOutHandler() {
      this.passwordErrorMsg = "";
      this.passwordCheckErrorMsg = "";
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 20px;
}
.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

// ::v-deep .v-text-field__slot {
//   font-size: 18px;
// }

// ::v-deep .v-input {
//   font-family: GilroyMedium !important;
//   font-size: 1.125em;
// }

::v-deep .v-text-field .v-label {
  // top: -10px !important;
  color: #a7a7a7;
  font-size: 18px;
  letter-spacing: -0.03em !important;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    letter-spacing: -0.03em !important;
    line-height: 15px !important;
  }
}

::v-deep .v-text-field input {
  flex: 1 1 auto;
  line-height: 25px;
  padding: 10px 0 0;
}
::v-deep .v-input input {
  font-size: 20px !important;
  line-height: 23px;
}
// ::v-deep .v-input .v-label {
//   line-height: 15px !important;
// }
// ::v-deep .v-label--active {
//   transform: translateY(0px) !important;
// }
::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
