<template>
  <div class="gender-input__wrapper">
    <div class="gender-input__title">{{ $t("neutered") }}</div>
    <div class="gender-checkbox__items">
      <div class="gender-checkbox__item">
        <v-checkbox
          color="#41d8e6"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          v-model="neutered"
          @change="neuteredCheckBoxHandler"
        >
          <template v-slot:label>
            <div class="gender-text">{{ $t("did") }}</div>
          </template>
        </v-checkbox>
      </div>
      <div class="gender-checkbox__item">
        <v-checkbox
          color="#41d8e6"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          v-model="notNeutered"
          @change="notNeuteredCheckBoxHandler"
        >
          <template v-slot:label>
            <div class="gender-text">{{ $t("did_not") }}</div>
          </template>
        </v-checkbox>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    initialNeutered: Boolean,
  },
  data() {
    return {
      neutered: null,
      notNeutered: null,
    };
  },
  methods: {
    neuteredCheckBoxHandler() {
      if (this.neutered) {
        this.notNeutered = false;
      }
      this.neutered ? this.$emit("neuteredHandler", true) : this.$emit("neuteredHandler", null);
    },
    notNeuteredCheckBoxHandler() {
      if (this.notNeutered) {
        this.neutered = false;
      }
      this.notNeutered ? this.$emit("neuteredHandler", false) : this.$emit("neuteredHandler", null);
    },
    neuteredHandler(neutered) {
      // console.log(neutered);
      if (neutered) {
        this.neutered = true;
        this.notNeutered = false;
        this.$emit("neuteredInputHandler", true);
      } else if (neutered === null) {
        this.neutered = null;
        this.notNeutered = null;
      } else {
        this.neutered = false;
        this.notNeutered = true;
        this.$emit("neuteredInputHandler", false);
      }
    },
  },
  mounted() {
    // console.log(this.initialNeutered);
    this.neuteredHandler(this.initialNeutered);
  },
};
</script>

<style lang="scss" scoped>
.gender-input__wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  // height: 100px;
}
.gender-checkbox__items {
  margin-bottom: 10px;
  display: flex;
  width: 100%;
}

.gender-checkbox__item {
  width: 50%;
}
.gender-input__title {
  width: 100%;
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  color: #646464;
  padding-bottom: 10px;
}

.gender-text {
  color: #646464;
  font-size: 18px;
}

::v-deep .v-input--selection-controls {
  margin-top: 10px;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 5px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0 !important;
}

::v-deep .theme--light.v-messages {
  display: none;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
