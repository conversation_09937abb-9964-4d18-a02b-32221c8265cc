<template>
  <div class="weight-form">
    <div class="text-field-label">{{ $t("pet_registration_number") }}</div>
    <div class="age-input__subtitle">
      {{ $t("registration_description") }}
    </div>
    <v-text-field
      color="#41d8e6"
      v-model="registrationNumber"
      :persistent-placeholder="true"
      type="number"
      inputmode="numeric"
      :error-messages="registrationNumberError"
    >
    </v-text-field>
  </div>
</template>

<script>
export default {
  props: {
    initialRegistrationNumber: String,
  },
  data() {
    return {
      registrationNumber: 0,
      validRegistrationNumber: false,
      registrationNumberError: "",
    };
  },
  methods: {
    registrationNumberValidation(registrationNumber) {
      if (
        registrationNumber === null ||
        registrationNumber === undefined ||
        registrationNumber.length === 0
      )
        return true;

      return registrationNumber > 0 && registrationNumber.length === 15;
    },
  },
  watch: {
    registrationNumber(newVal) {
      if (
        this.registrationNumber === null ||
        this.registrationNumberValidation(newVal)
      )
        this.registrationNumberError = "";
      else this.registrationNumberError = this.$i18n.t("invalid");

      this.registrationNumberValidation(this.registrationNumber) === true
        ? (this.validRegistrationNumber = true)
        : (this.validRegistrationNumber = false);
      const registrationNumber = {
        registrationNumber: this.registrationNumber,
        valid: this.validRegistrationNumber,
      };
      this.$emit("registrationNumberHandler", registrationNumber);
    },
  },
  mounted() {
    this.registrationNumber = this.initialRegistrationNumber || null;
  },
};
</script>

<style lang="scss" scoped>
.weight-form {
  width: 100%;
  // height: 90px;
}

::v-deep v-text-field input {
  font-size: 18px;
}
.text-field-label {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  transition: 0.1s ease-in-out;
  // font-family: Noto Sans KR;
}

.age-input__subtitle {
  font-size: 14px;
  text-align: left;
  color: #a7a7a7;
  letter-spacing: -0.03em;
  padding-bottom: 10px;
}

::v-deep .v-input {
  font-family: GilroyMedium !important;
  margin-bottom: 20px;
  font-size: 20px;
  letter-spacing: -0.03em;
}

::v-deep .v-text-field input {
  padding: 0;
  font-size: 20px;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    letter-spacing: -0.03em;
    padding: 0;
    font-weight: 400 !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
