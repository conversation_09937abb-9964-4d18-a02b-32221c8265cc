<template>
  <div>
    <div class="dp-30">
      <div>
        <v-text-field
          v-model="userId"
          :label="this.$i18n.t('input_id')"
          color="#41D8E6"
          type="text"
          :error-messages="userIdErrorMsg"
          maxLength="10"
          :placeholder="this.$i18n.t('input_id_placeholder')"
          required
          @blur="focusOutHandler"
        >
        </v-text-field>
      </div>
      <v-btn
        class="main-large-btn"
        elevation="0"
        color="#41D8E6"
        type="submit"
        :disabled="!isAvaliableUserId"
        @click="nextPhaseHandler"
      >
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import API from "@/api/auth/index.js";

export default {
  data() {
    return {
      userId: this.$store.state.join.account || "",
      userIdErrorMsg: "",
      isAvaliableUserId: false,
    };
  },

  watch: {
    userId(newVal) {
      const id = newVal.replace(/\s/g, "");
      // console.log(id);
      if (this.userId.length === 0 || this.userIdValidationCheck(newVal)) {
        if (4 < id.length && id.length < 11 && this.userIdValidationCheck(newVal)) {
          this.userIdErrorMsg = "";
          this.isAvaliableUserId = true;
        }
      } else {
        this.userIdErrorMsg = this.$i18n.t("input_id_placeholder");
        this.isAvaliableUserId = false;
      }
    },
  },
  methods: {
    async nextPhaseHandler() {
      const id = this.userId.replace(/\s/g, "");
      // console.log(id);
      try {
        const userId = {
          account: id,
        };
        const { data, status } = await API.fetchCheckUserId(userId);
        // console.log(data, status);
        if (status === 201) {
          if (!data.accountExists) {
            this.$store.commit("GET_USERID", this.userId);
            // console.log(this.$store.state.join.account);
            this.$emit("nextPhaseHandler", 2);
          } else {
            this.userIdErrorMsg = this.$i18n.t("join_id_invalid");
          }
        }
      } catch (e) {
        console.log(e);
        this.userIdErrorMsg = this.$i18n.t("input_id_placeholder");
      }
      // this.$store.commit("GET_USERID", this.userId);
      // this.$emit("nextPhaseHandler", 2);
    },
    userIdValidationCheck(userId) {
      return /([a-zA-Z0-9]).{4,10}$/.test(userId);
    },
    focusOutHandler() {
      // this.userIdErrorMsg = "";
    },
  },
  mounted() {
    // console.log(this.userIdValidationCheck(this.userId));
    this.userIdValidationCheck(this.userId)
      ? ((this.userIdErrorMsg = ""), (this.isAvaliableUserId = true))
      : (this.isAvaliableUserId = false);
  },
};
</script>

<style scoped>
::v-deep .theme--light.v-input input,
.theme--light.v-input textarea {
  color: #000000;
}

::v-deep .v-input input {
  font-size: 20px;
  font-weight: 500;
  font-family: "GilroyMedium";
  line-height: 30px;
}

::v-deep .v-text-field input {
  padding: 0 !important;
}
</style>
