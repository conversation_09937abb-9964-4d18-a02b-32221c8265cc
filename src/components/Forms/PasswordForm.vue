<template>
  <div>
    <ErrorModal v-show="showErrorModal" :error="error" @isClicked="isClicked" />

    <CompleteAlert v-show="showCompleteAlert" :content="content" />
    <div class="pt-30 dp-30">
      <div>
        <v-text-field
          v-model="password"
          :label="this.$i18n.t('profile_pwd')"
          color="#41D8E6"
          :type="showPassword1 ? 'text' : 'password'"
          :append-icon="showPassword1 ? '$eye_show' : '$eye_off'"
          @click:append="showPassword1 = !showPassword1"
          :error-messages="passwordErrorMsg"
          :placeholder="this.$i18n.t('join_pwd_placeholder')"
          @focus="focusInHandler"
          @blur="focusOutHandler"
          required
        >
        </v-text-field>
      </div>
      <div>
        <v-text-field
          v-model="passwordCheck"
          :label="this.$i18n.t('join_pwd_check')"
          color="#41D8E6"
          :type="showPassword2 ? 'text' : 'password'"
          :append-icon="showPassword2 ? '$eye_show' : '$eye_off'"
          @click:append="showPassword2 = !showPassword2"
          :error-messages="passwordCheckErrorMsg"
          @focus="focusInHandler"
          @blur="focusOutHandler"
          required
        >
        </v-text-field>
      </div>

      <v-btn
        class="main-large-btn"
        elevation="0"
        color="#41D8E6"
        type="submit"
        :disabled="isAvaliablePwd"
        @click="nextPhaseHandler"
      >
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import CompleteAlert from "@/components/Common/Modal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { updateUserPw } from "@/api/user/index";

export default {
  components: {
    CompleteAlert,
    ErrorModal,
  },
  data() {
    return {
      currentStateIdx: 0,
      currentState: false,
      showPassword1: false,
      showPassword2: false,
      password: "",
      passwordCheck: "",
      passwordErrorMsg: "",
      passwordCheckErrorMsg: "",
      isAvaliablePwd: true,
      showCompleteAlert: false,
      content: this.$i18n.t("success_message_pwd"),
      error: this.$i18n.t("pwd_error_modal"),
      showErrorModal: false,
    };
  },
  computed: {
    showPasswordIconHandler() {
      if (this.showPassword2) {
        return "$eye_show";
      } else {
        return "$eye_off";
      }
    },
  },
  watch: {
    password(newVal) {
      if (this.passwordValidationCheck(newVal)) {
        this.passwordErrorMsg = "";
      } else {
        this.passwordErrorMsg = this.$i18n.t("join_pwd_error");
      }
    },
    passwordCheck(newVal) {
      if (this.password === newVal) {
        this.passwordCheckErrorMsg = "";
        this.isAvaliablePwd = false;

        if (this.passwordValidationCheck(this.password)) {
          this.isAvaliablePwd = false;
        }
      } else {
        this.passwordCheckErrorMsg = this.$i18n.t("pwd_match_error");
      }
    },
  },
  methods: {
    async updatePw() {
      const curPw = this.$store.state.password;
      if (this.passwordValidationCheck(this.passwordCheck)) {
        const password = { password: curPw, changePassword: this.passwordCheck };
        try {
          const { status } = await updateUserPw(password);
          if (status === 200) {
            this.showCompleteAlert = true;
          }
        } catch (e) {
          this.showErrorModal = true;
          console.log(e);
        }
      } else {
        this.showErrorModal = true;
      }
    },
    nextPhaseHandler() {
      this.updatePw();
    },
    passwordValidationCheck(pwd) {
      // eslint-disable-next-line no-useless-escape
      return /^(?=.*[A-Za-z])(?=.*\d)(?=.*[~!@#$%^&*()<>_+=|\\€£¥.,‘“\-/;'"\[\]])[A-Za-z\d~!@#$%^&*()<>_+=|\\€£¥.,‘“\-/;'"\[\]]{8,16}$/.test(pwd);
    },

    focusInHandler() {
      if (this.password.length > 1 || this.passwordValidationCheck(this.password)) {
        this.passwordErrorMsg = "";
      } else {
        this.passwordErrorMsg = this.$i18n.t("join_pwd_error");
      }
    },
    focusOutHandler() {
      this.passwordErrorMsg = "";
      this.passwordCheckErrorMsg = "";
    },
    isClicked(boolean) {
      this.showErrorModal = false;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 1.125em;
}
.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

// ::v-deep .v-text-field__slot {
//   font-size: 18px;
// }

// ::v-deep .v-input {
//   font-family: GilroyMedium !important;
//   font-size: 1.125em;
// }

::v-deep .v-text-field .v-label {
  // top: -10px !important;
  color: #a7a7a7;
  font-size: 18px;
  letter-spacing: -0.03em !important;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
    letter-spacing: -0.03em !important;
    line-height: 15px !important;
  }
}

::v-deep .v-text-field input {
  flex: 1 1 auto;
  line-height: 25px;
  padding: 10px 0 0;
}
::v-deep .v-input input {
  font-size: 18px !important;
  line-height: 23px;
}
// ::v-deep .v-input .v-label {
//   line-height: 15px !important;
// }
// ::v-deep .v-label--active {
//   transform: translateY(0px) !important;
// }
::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
