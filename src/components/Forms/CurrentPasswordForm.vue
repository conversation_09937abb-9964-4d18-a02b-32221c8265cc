<template>
  <div>
    <ErrorModal v-show="showErrorModal" :error="error" @isClicked="isClicked" />
    <div class="pt-30 dp-30">
      <div>
        <v-text-field
          v-model="password"
          :label="this.$i18n.t('cur_pwd')"
          color="#41D8E6"
          :type="showPassword ? 'text' : 'password'"
          :append-icon="showPassword ? '$eye_show' : '$eye_off'"
          @click:append="showPassword = !showPassword"
          :error-messages="passwordErrorMsg"
          @focus="focusInHandler"
          @blur="focusOutHandler"
          required
        >
        </v-text-field>
      </div>
      <v-btn
        class="main-large-btn"
        elevation="0"
        color="#41D8E6"
        type="submit"
        :disabled="isAvaliablePwd"
        @click="nextPhaseHandler"
      >
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { checkUserPw } from "@/api/user/index";

export default {
  components: { ErrorModal },
  data() {
    return {
      password: "",
      passwordErrorMsg: "",
      showPassword: false,
      isAvaliablePwd: true,
      error: this.$i18n.t("pwd_error"),
      showErrorModal: false,
    };
  },
  methods: {
    async checkPwd() {
      if (this.passwordValidationCheck(this.password)) {
        try {
          const pw = { password: this.password };
          const res = await checkUserPw(pw);
          if (res.status === 201) {
            this.$emit("nextStateHandler");
            this.$store.commit("setCurPw", this.password);
          } else {
            this.showErrorModal = true;
            this.passwordErrorMsg = this.$i18n.t("join_pwd_error");
          }
        } catch (e) {
          this.showErrorModal = true;
          console.log(e);
        }
      } else {
        this.showErrorModal = true;
      }
    },
    errorHandler() {
      this.showErrorModal = false;
    },
    nextPhaseHandler() {
      // this.$emit("nextStateHandler");
      this.checkPwd();
    },
    passwordValidationCheck(pwd) {
      // eslint-disable-next-line no-useless-escape
      return /^(?=.*[A-Za-z])(?=.*\d)(?=.*[~!@#$%^&*()<>_+=|\\€£¥.,‘“\-/;'"\[\]])[A-Za-z\d~!@#$%^&*()<>_+=|\\€£¥.,‘“\-/;'"\[\]]{8,16}$/.test(pwd);
    },
    focusInHandler() {
      if (this.password.length === 0 || this.passwordValidationCheck(this.password)) {
        this.passwordErrorMsg = "";
      } else {
        this.passwordErrorMsg = this.$i18n.t("join_pwd_error");
      }
    },
    focusOutHandler() {
      this.passwordErrorMsg = "";
    },
    isClicked(boolean) {
      this.showErrorModal = false;
    },
  },
  watch: {
    password(newVal) {
      if (this.passwordValidationCheck(newVal)) {
        this.isAvaliablePwd = false;
        this.passwordErrorMsg = "";
      } else {
        this.passwordErrorMsg = this.$i18n.t("join_pwd_error");
      }
    },
  },
  computed: {
    showPasswordIconHandler() {
      if (this.showPassword) {
        return "$eye_show";
      } else {
        return "$eye_off";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 1.125em;
}
.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .v-text-field__slot {
  font-size: 18px;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

// ::v-deep .v-input {
//   font-family: GilroyMedium !important;
//   font-size: 1.125em;
// }

// ::v-deep .v-text-field .v-label {
//   top: -10px !important;
//   font-size: 12px;
//   color: #41d8e6;
//   letter-spacing: -0.03em !important;
// }
// ::v-deep .v-text-field__slot {
//   input::placeholder {
//     font-size: 16px !important;
//     letter-spacing: -0.03em !important;
//     line-height: 15px !important;
//   }
// }
// ::v-deep .v-input input {
//   font-size: 16px !important;
//   line-height: 23px;
// }
// ::v-deep .v-input .v-label {
//   line-height: 15px !important;
// }
// ::v-deep .v-label--active {
//   transform: translateY(0px) !important;
// }
</style>
