<template>
  <div>
    <div class="wrapper">
      <div class="water-subtract-btn__wrapper">
        <img
          @click="subtractWeight"
          width="45"
          src="@/assets/images/minus_circle.png"
        />
      </div>
      <div class="scales__wrapper">
        <img :src="changeWeightImg" @click="saveWeightDataHandler" />
        <div class="weight_value">{{ weightValue }}</div>
      </div>
      <div class="water-plut-btn__wrapper">
        <img
          @click="addWeight"
          width="45"
          src="@/assets/images/plus_circle.png"
        />
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    type: String,
    weight: Number,
  },
  data() {
    return {
      weightValue: 0,
      activeWeightImg: "",
      isSaveAvailable: false,
    };
  },
  mounted() {
    // console.log(this.weight);
    this.weightValue = this.weight;
  },

  watch: {
    weight: function (newVal) {
      // console.log(newVal);
      this.weightValue = newVal;
    },
  },
  computed: {
    changeWeightImg() {
      if (this.isSaveAvailable) {
        return require("@/assets/images/main_scales_on.png");
      }
      return require("@/assets/images/main_scales.png");
    },
  },
  methods: {
    addWeight() {
      this.weightValue = +(parseFloat(this.weightValue) + 0.1).toFixed(1);
      this.isSaveAvailable = true;
    },

    subtractWeight() {
      if (this.weightValue > 0) {
        this.weightValue = +(parseFloat(this.weightValue) - 0.1).toFixed(1);
      }
      this.isSaveAvailable = true;
    },

    async saveWeightDataHandler() {
      if (this.isSaveAvailable) {
        this.isSaveAvailable = false;
        this.$emit("saveBtnHandler", "weight", this.weightValue)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.chart__wrapper {
  padding-bottom: 10px;
}

.gauge-chart__wrapper {
  position: absolute;
  top: 0;
}

.wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin: 0 auto;
  background: #fff;
  position: relative;
  width: 100%;
  //   min-height: 250px;
  padding: 30px;
}

.scales__wrapper {
  position: relative;
  img {
    width: 200px;
  }
}

.weight_value {
  font-family: GilroyMedium;
  color: #a7a7a7;
  font-size: 24px;
  position: absolute;
  top: 35px;
  left: 50%;
  transform: translateX(-50%);
}
</style>