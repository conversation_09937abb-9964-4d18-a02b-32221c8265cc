<template>
  <div>
    <div class="search__wrapper">
      <CompleteAlert
        class="modal"
        :content="content"
        :btnText="this.$i18n.t('confirm_btn')"
        v-show="showCompleteAlert"
        @isConfirmed="isConfirmed"
      />
      <div v-show="beforeCheck">
        <div class="dp-30">
          <div class="subtitle__txt" v-html="this.$i18n.t('comment_subtitle_1')"></div>
        </div>
        <div class="form__wrapper">
          <PhoneForm @phoneInputHandler="phoneInputHandler" />
          <!-- <BirthForm @birthInputHandler="birthInputHandler" /> -->
        </div>
      </div>
      <div v-show="!beforeCheck">
        <div class="dp-30">
          <div class="subtitle__txt">
            {{ $t("account_searched") }}
          </div>
        </div>
        <div class="form__container" v-for="(name, idx) in getUser" :key="idx">
          <div class="find-result">{{ name }}</div>
        </div>
      </div>
      <!-- v-if="showTextField" -->
      <div class="textfield__wrapper" v-if="showTextField">
        <v-text-field
          v-model="authNumber"
          :label="this.$i18n.t('verification_number')"
          color="#41D8E6"
          type="number"
          inputmode="numeric"
          maxlength="6"
          :rules="validAuthNum"
          :error-messages="authErrorMsg"
          required
        >
          <template v-slot:append>
            <div class="v-timer">{{ minutes }}:{{ seconds }}</div>
            <div class="v-request-btn" @click="reSendSms">
              <button>{{ $t("request_btn") }}</button>
            </div>
          </template>
        </v-text-field>
      </div>
      <div class="btn__wrapper">
        <v-btn
          class="main-large-btn"
          :disabled="!isValid"
          elevation="0"
          color="#41D8E6"
          type="submit"
          @click="nextPhaseHandler"
        >
          {{ buttonText }}
        </v-btn>
      </div>
      <div class="access-btn__wrapper">
        <!-- <div class="access-btn" @click="goKakaoChannel">{{ $t("is_changed_phonenumber") }}</div> -->
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/auth/index.js";
import FindAPI from "@/api/auth/findUser.js";

import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import PhoneForm from "@/components/Forms/FindPhoneForm.vue";
// import BirthForm from "@/components/Forms/FindBirthForm.vue";

export default {
  components: { PhoneForm, CompleteAlert },
  data() {
    return {
      subtitle: "",
      beforeCheck: true,
      getUser: [],
      countryCode: "",
      phone: "",
      phoneValid: false,
      isValid: false,
      authNumber: "",
      timer: null,
      totalTime: 3 * 60,
      showTextField: false,
      validAuthNum: [(v) => /^[0-9]{1,6}$/.test(v) || this.$i18n.t("invalid_error")],
      findErrorMsg: "",
      authErrorMsg: "",
      showCompleteAlert: false,
      content: this.$i18n.t("send_text_modal_msg"),
      alert: this.$i18n.t("error_timeout"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      deviceId: "",
    };
  },
  methods: {
    isConfirmed() {
      this.showCompleteAlert = false;
    },
    phoneInputHandler(fromChild) {
      this.phoneValid = fromChild.valid;
      this.countryCode = fromChild.code;
      this.phone = fromChild.phone;
      this.buttonAbled();
    },
    // birthInputHandler(fromChild) {
    //   this.birthValid = fromChild.valid;
    //   this.birth = fromChild.birth;
    //   this.buttonAbled();
    // },
    nextPhaseHandler() {
      if (this.showTextField) {
        this.checkAuth();
      } else if (!this.beforeCheck) {
        this.goToLogin();
      } else {
        this.checkAccount();
      }
    },
    goToLogin() {
      this.$router.push("/login");
    },
    buttonAbled() {
      this.isValid = !!this.countryCode && !!this.phone && !!this.phoneValid;
    },
    async checkAccount() {
      const type = "?type=pet";
      const userData = {
        countryNumber: this.countryCode,
        phone: this.phone,
        device: this.deviceId,
      };
      // console.log(type, userData);
      try {
        const res = await FindAPI.getCheckPhoneNumBirth(type, userData);
        if (res.status === 201) {
          // this.sendSms();
          this.content = this.$i18n.t("send_text_modal_msg");
          this.showCompleteAlert = true;
          this.showTextField = true;
          this.startTimer();
          this.totalTime = 3 * 60;
        } else {
          this.content = this.$i18n.t("error_dismatch");
          this.showCompleteAlert = true;
        }
      } catch (e) {
        console.log(e);
        this.stopTimer();
      }
    },
    // async sendSms() {
    //   const phoneNumber = {
    //     countryNumber: this.countryCode,
    //     phone: this.phone,
    //   };
    //   try {
    //     const response = await API.fetchCheckPhone(phoneNumber);
    //     if (response.status === 201) {
    //       this.content = this.$i18n.t("send_text_modal_msg");
    //       this.showCompleteAlert = true;
    //       this.showTextField = true;
    //       this.startTimer();
    //       this.totalTime = 3 * 60;
    //     }
    //   } catch (e) {
    //     console.log(e);
    //   }
    // },
    async checkAuth() {
      try {
        const type = "?type=pet";
        const authInfo = {
          countryNumber: this.countryCode,
          phone: this.phone,
          device: this.deviceId,
          code: this.authNumber,
        };
        // console.log(type, authInfo);
        const { status, data } = await FindAPI.postCheckVerifyCode(type, authInfo);
        // console.log(status);
        if (status === 200) {
          this.beforeCheck = false;
          this.showTextField = false;
          this.getUser = data;
          this.stopTimer();
        }
      } catch (error) {
        console.log(error);
        this.authErrorMsg = this.$i18n.t("invalid_error");
        this.stopTimer();
      }
    },
    countdown() {
      if (this.totalTime >= 1) {
        this.totalTime--;
      } else {
        this.totalTime = 0;
        this.resetTimer;
        this.showCompleteAlert = true;
        this.content = this.$i18n.t("error_timeout");
      }
    },
    startTimer() {
      clearInterval(this.timer);
      this.timer = setInterval(() => this.countdown(), 1000);
    },
    stopTimer() {
      clearInterval(this.timer);
    },
    padTime(time) {
      return (time < 10 ? "0" : "") + time;
    },
    reSendSms() {
      this.totalTime = 3 * 60;
      this.sendSms();
      this.showTextField = true;
      this.showCompleteAlert = true;
    },
    /*global Webview*/
    /*eslint no-undef: "error"*/
    goKakaoChannel() {
      // 카카오 채널을 이용한 고객센터 문의.
      const bridge_msg = {
        action: "goKakaoChannel",
      };
      Webview.goKakaoChannel(bridge_msg);
    },

    getDeviceId() {
      const deviceId = Webview.getDeviceId();

      this.deviceId = deviceId; 
      this.$nextTick(() => this.listener());
    },

    listener() {
      this.isIos
        ? window.addEventListener("message", this.handleMessage)
        : document.addEventListener("message", this.handleMessage);
    },
    handleMessage(e) {
      const data = JSON.parse(e.data);
      this.deviceId = data.payload.result;
    },
  },
  computed: {
    buttonText() {
      if (this.showTextField) {
        return this.$i18n.t("next_btn");
      } else if (!this.beforeCheck) {
        return this.$i18n.t("login_with_id");
      } else {
        return this.$i18n.t("send_btn");
      }
    },
    minutes() {
      const minutes = Math.floor(this.totalTime / 60);
      return this.padTime(minutes);
    },
    seconds() {
      const seconds = this.totalTime - this.minutes * 60;
      return this.padTime(seconds);
    },
  },
  mounted() {
    this.getDeviceId();
  },
  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
    document.removeEventListener("message", this.handleMessage);
  },
};
</script>

<style lang="scss" scoped>
.search__wrapper {
  height: 70vh;
}

.subtitle__txt {
  padding-top: 30px;
  margin: 0 !important;
  text-align: left;
  letter-spacing: -0.03em;
  font-weight: 400;
  font-size: 18px;
  line-height: 25px;
  color: #646464;
}

.form__wrapper {
  padding-top: 30px;
}

.form__container {
  padding: 0 30px;
}

.subtitle {
  letter-spacing: -0.03em;
  font-size: 18px;
  line-height: 25px;
  color: #646464;
  text-align: left;
  padding: 30px;
}

.find-result {
  border-bottom: 1px solid#41d8e6;
  margin: 25px 0;
  padding-bottom: 5px;
  text-align: left;
  font-size: 20px;
  line-height: 23px;
  font-family: GilroyMedium;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

.access-btn__wrapper {
  width: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.access-btn {
  color: #a7a7a7;
  border-bottom: 0.5px solid #a7a7a7;
  font-size: 14px;
}

.btn__wrapper {
  padding: 0 30px;
}

.main-large-btn {
  margin-top: 3px;
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

.textfield__wrapper {
  width: 100%;
  padding: 0 30px;
}

.v-timer {
  font-size: 18px;
  letter-spacing: -0.03em;
  color: #000 !important;
  display: flex;
  align-items: center;
}
.v-request-btn {
  font-size: 16px;
  line-height: 20px;
  text-indent: 3px;

  button {
    color: #41d8e6;
    // font-weight: 600;
    line-height: 14px;
    padding-bottom: 5px;
    letter-spacing: -0.03em;
  }
}

::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 20px;
}
</style>
