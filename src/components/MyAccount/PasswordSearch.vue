<template>
  <div class="white-bg">
    <CompleteAlert
      class="modal"
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-show="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <div class="dp-30 wrapper">
      <div class="subtitle__txt" v-html="this.$i18n.t('comment_subtitle_2')"></div>
    </div>
    <div class="form__wrapper">
      <PhoneForm v-if="!changeAbled" @phoneInputHandler="phoneInputHandler" />
      <AccountForm v-if="!changeAbled" @accountInputHandler="accountInputHandler" />
      <div class="textfield__wrapper" v-if="showTextField">
        <v-text-field
          v-model="authNumber"
          :label="this.$i18n.t('verification_number')"
          color="#41D8E6"
          type="number"
          inputmode="numeric"
          maxlength="6"
          :rules="validAuthNum"
          :error-messages="authErrorMsg"
          required
        >
          <template v-slot:append>
            <div class="v-timer">{{ minutes }}:{{ seconds }}</div>
            <div class="v-request-btn" @click="reSendSms">
              <button>{{ $t("request_btn") }}</button>
            </div>
          </template>
        </v-text-field>
      </div>
      <div class="set-pw-form__wrapper" v-if="changeAbled">
        <div>
          <v-text-field
            v-model="newPassword"
            :label="this.$i18n.t('profile_pwd')"
            color="#41D8E6"
            :type="showPassword1 ? 'text' : 'password'"
            :append-icon="showPassword1 ? '$eye_show' : '$eye_off'"
            @click:append="showPassword1 = !showPassword1"
            :error-messages="passwordErrorMsg"
            :placeholder="this.$i18n.t('join_pwd_placeholder')"
            required
          >
          </v-text-field>
        </div>
        <div>
          <v-text-field
            v-model="passwordCheck"
            :label="this.$i18n.t('join_pwd_check')"
            color="#41D8E6"
            :type="showPassword2 ? 'text' : 'password'"
            :append-icon="showPassword2 ? '$eye_show' : '$eye_off'"
            @click:append="showPassword2 = !showPassword2"
            :error-messages="passwordCheckErrorMsg"
            required
          >
          </v-text-field>
        </div>
      </div>
    </div>
    <div class="btn__wrapper">
      <v-btn
        class="main-large-btn"
        :disabled="!isValid"
        elevation="0"
        color="#41D8E6"
        type="submit"
        @click="nextPhaseHandler"
      >
        {{ buttonText }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import API from "@/api/auth/index.js";
import FindAPI from "@/api/auth/findUser.js";

import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import PhoneForm from "@/components/Forms/FindPhoneForm.vue";
import AccountForm from "@/components/Forms/FindAccountForm.vue";

export default {
  components: { PhoneForm, CompleteAlert, AccountForm },
  data() {
    return {
      beforeCheck: false,
      showCompleteAlert: false,
      account: "",
      accountValid: false,
      phone: "",
      phoneValid: false,
      countryCode: "",
      content: "",
      showTextField: false,
      // isValid: false,
      password: "",
      showPassword1: false,
      showPassword2: false,
      newPassword: "",
      passwordCheck: "",
      passwordErrorMsg: "",
      passwordCheckErrorMsg: "",
      changeAbled: false,
      authNumber: "",
      validAuthNum: [(v) => /^[0-9]{1,6}$/.test(v) || this.$i18n.t("invalid_error")],
      isAvaliablePwd: false,
      isSuccess: false,
      findErrorMsg: "",
      authErrorMsg: "",
      timer: null,
      totalTime: 3 * 60,
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      deviceId: "",
    };
  },
  computed: {
    buttonText() {
      if (this.showTextField) {
        return this.$i18n.t("next_btn");
      } else if (this.changeAbled) {
        return this.$i18n.t("profile_pwd_title");
      } else {
        return this.$i18n.t("send_btn");
      }
    },
    minutes() {
      const minutes = Math.floor(this.totalTime / 60);
      return this.padTime(minutes);
    },
    seconds() {
      const seconds = this.totalTime - this.minutes * 60;
      return this.padTime(seconds);
    },
    isValid() {
      if (this.changeAbled) {
        return this.isAvaliablePwd;
      } else {
        return !!this.countryCode && !!this.phone && !!this.account && this.phoneValid && this.accountValid;
      }
    },
  },
  watch: {
    newPassword(newVal) {
      // console.log(newVal);
      // if (newVal.length === 0) {
      //   this.isValid = false;
      // }
      if (this.passwordValidationCheck(newVal)) {
        this.passwordErrorMsg = "";
      } else {
        this.passwordErrorMsg = this.$i18n.t("join_pwd_error");
      }
    },
    passwordCheck(newVal) {
      // console.log(newVal);
      // if (newVal.length === 0) {
      //   this.isValid = false;
      // }
      if (
        this.newPassword === newVal &&
        this.passwordValidationCheck(this.newPassword) &&
        this.passwordValidationCheck(newVal)
      ) {
        this.passwordCheckErrorMsg = "";
        this.isAvaliablePwd = true;
        this.buttonAbled();
      } else {
        this.passwordCheckErrorMsg = this.$i18n.t("pwd_match_error");
      }
    },
  },
  methods: {
    isConfirmed() {
      // !this.isAvaliablePwd ||
      this.totalTime === 0 ||
      (this.newPassword === this.passwordCheck &&
        this.passwordValidationCheck(this.newPassword) &&
        this.passwordValidationCheck(this.passwordCheck) &&
        this.isSuccess)
        ? this.$router.push("/login")
        : (this.showCompleteAlert = false);
    },
    phoneInputHandler(fromChild) {
      this.phoneValid = fromChild.valid;
      this.countryCode = fromChild.code;
      this.phone = fromChild.phone;
      this.buttonAbled();
    },
    accountInputHandler(fromChild) {
      this.account = fromChild.account;
      this.accountValid = fromChild.valid;
      this.buttonAbled();
    },
    buttonAbled() {
      // if (this.changeAbled) {
      //   this.isValid = this.isAvaliablePwd;
      // } else {
      //   this.isValid = !!this.countryCode && !!this.phone && !!this.account && this.phoneValid && this.accountValid;
      // }
    },
    nextPhaseHandler() {
      if (this.showTextField) {
        this.checkAuth();
      } else if (this.changeAbled) {
        this.changePassword();
      } else {
        this.checkAccount();
      }
    },
    async checkAccount() {
      const type = "?type=pet";
      const userData = {
        countryNumber: this.countryCode,
        phone: this.phone,
        account: this.account,
        device: this.deviceId,
      };
      try {
        const res = await FindAPI.getCheckPhoneNumId(type, userData);
        // console.log(res);
        if (res.status === 201) {
          this.content = this.$i18n.t("send_text_modal_msg");
          this.showCompleteAlert = true;
          this.showTextField = true;
          this.startTimer();
          this.totalTime = 3 * 60;
        }
      } catch (e) {
        this.content = this.$i18n.t("error_dismatch");
        this.showCompleteAlert = true;
        console.log(e);
      }
    },
    // async sendSms() {
    //   const phoneNumber = {
    //     countryNumber: this.countryCode,
    //     phone: this.phone,
    //   };
    //   try {
    //     // const response = await API.fetchCheckPhone(phoneNumber);
    //     if (response.status === 201) {
    //       this.content = this.$i18n.t("send_text_modal_msg");
    //       this.showCompleteAlert = true;
    //       this.showTextField = true;
    //       this.startTimer();
    //       this.totalTime = 3 * 60;
    //     }
    //   } catch (e) {
    //     console.log(e);
    //   }
    // },
    async checkAuth() {
      try {
        const authInfo = {
          countryNumber: this.countryCode,
          phone: this.phone,
          account: this.account,
          code: this.authNumber,
          device: this.deviceId,
        };
        const type = "?type=pet";
        // this.$emit("nextPhaseHandler", 1);
        const res = await FindAPI.postCheckVerifyCodePw(type, authInfo);
        // console.log(res);
        if (res.status === 200) {
          this.content = this.$i18n.t("success_authentication");
          this.timer = null;
          this.showTextField = false;
          this.showCompleteAlert = true;
          this.changeAbled = true;
          this.stopTimer();
        }
      } catch (error) {
        console.log(error);
        this.authErrorMsg = this.$i18n.t("invalid_error");
        this.stopTimer();
      }
    },
    async changePassword() {
      try {
        const type = "?type=pet";
        const userData = {
          countryNumber: this.countryCode,
          phone: this.phone,
          account: this.account,
          code: this.authNumber,
          password: this.passwordCheck,
          device: this.deviceId,
        };
        const res = await FindAPI.postCheckVerifyCodePw(type, userData);
        // console.log(res);
        if (res.status === 200) {
          this.content = this.$i18n.t("success_change_password");
          this.isSuccess = true;
          this.showCompleteAlert = true;
        }
      } catch (e) {
        console.log(e);
        this.isSuccess = false;
        this.content = this.$i18n.t("pwd_error_modal");
        this.showCompleteAlert = true;
      }
    },
    countdown() {
      if (this.totalTime >= 1) {
        this.totalTime--;
      } else {
        this.totalTime = 0;
        this.resetTimer;
        this.showCompleteAlert = true;
        this.content = this.$i18n.t("error_timeout");
      }
    },
    startTimer() {
      clearInterval(this.timer);
      this.timer = setInterval(() => this.countdown(), 1000);
    },
    stopTimer() {
      clearInterval(this.timer);
    },
    padTime(time) {
      return (time < 10 ? "0" : "") + time;
    },
    reSendSms() {
      this.totalTime = 3 * 60;
      this.sendSms();
      this.showTextField = true;
      this.showCompleteAlert = true;
    },
    passwordValidationCheck(pwd) {
      // eslint-disable-next-line no-useless-escape
      return /^(?=.*[A-Za-z])(?=.*\d)(?=.*[~!@#$%^&*()<>_+=|\\€£¥.,‘“\-/;'"\[\]])[A-Za-z\d~!@#$%^&*()<>_+=|\\€£¥.,‘“\-/;'"\[\]]{8,16}$/.test(
        pwd
      );
    },
    getDeviceId() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      Webview.getDeviceId();
      this.$nextTick(() => this.listener());
    },

    listener() {
      this.isIos
        ? window.addEventListener("message", this.handleMessage)
        : document.addEventListener("message", this.handleMessage);
    },
    handleMessage(e) {
      const data = JSON.parse(e.data);
      this.deviceId = data.payload.result;
    },
  },
  mounted() {
    this.getDeviceId();
  },
  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
    document.removeEventListener("message", this.handleMessage);
  },
};
</script>

<style lang="scss" scoped>
.white-bg {
  background: #fff;
  height: 70vh;
}
.subtitle__txt {
  padding-top: 30px;
  margin: 0 !important;
  text-align: left;
  letter-spacing: -0.03em;
  font-weight: 400;
  font-size: 18px;
  line-height: 25px;
  color: #646464;
}

.form__wrapper {
  width: 100%;
  padding: 30px 0;
}

.textfield__wrapper {
  width: 100%;
  padding: 0 30px;
}
.set-pw-form__wrapper {
  width: 100%;
  padding: 0 30px;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

.btn__wrapper {
  padding: 0 30px;
}

.main-large-btn {
  margin-top: 3px;
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

.v-timer {
  font-size: 18px;
  letter-spacing: -0.03em;
  color: #000 !important;
  display: flex;
  align-items: center;
}
.v-request-btn {
  font-size: 16px;
  line-height: 20px;
  text-indent: 3px;

  button {
    color: #41d8e6;
    // font-weight: 600;
    line-height: 14px;
    padding-bottom: 5px;
    letter-spacing: -0.03em;
  }
}

::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 20px;
}
</style>
