<template>
  <!--=============== weight, water_history_page history table 컴포넌트 =======================-->
  <div class="history-table__wrapper">
    <!--============== 🚨 체중, 수분 history정보 화면에 구현 =======================-->
    <div class="history-table">
      <div class="history-table-header">
        <div class="history-table-title">{{ title }} {{ $t("record") }}</div>
        <button
          v-if="count === 'd' && history.length !== 0"
          @click="selectBtnHandler"
          :class="[active, 'select-btn']"
        >
          {{ selectTxt }}
        </button>
      </div>
      <!--=============== [♦︎] 수분 history정보 화면에 구현 =======================-->
      <div v-if="!noData" class="history-table-body">
        <div
          class="history-table-row"
          v-for="(item, idx) in history"
          :key="idx"
        >
          <div class="body-left">{{ item.createdAt }}</div>
          <div class="table-right">
            <div class="body-right">{{ item.value }} {{ unit }}</div>
            <button
              @click="selectHandler(item.id)"
              v-if="activeDelBtn"
              class="del-btn"
            >
              {{ deleteBtn }}
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="snackbar">
      <v-snackbar v-model="delSuccess" timeout="2000">{{
        successContent
      }}</v-snackbar>
      <v-snackbar v-model="delFail" timeout="2000" color="#EE0000">{{
        failContent
      }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import API from "@/api/care/index.js";

export default {
  props: {
    history: Array,
    type: String,
    noData: Boolean,
    count: String,
  },
  data() {
    return {
      careType: "",
      activeDelBtn: false,
      delSuccess: false,
      successContent: this.$i18n.t("delete_complete"),
      delFail: false,
      failContent: this.$i18n.t("delete_fail"),
      selectTxt: this.$i18n.t("btn_select"),
      deleteBtn: this.$i18n.t("delete_btn"),
    };
  },
  computed: {
    unit() {
      if (this.type === "weight") return "kg";
      if (this.type === "urine") return this.$i18n.t("times");
      else {
        return "ml";
      }
    },

    title() {
      if (this.type === "weight") return this.$i18n.t("weight_title");
      if (this.type === "water") return this.$i18n.t("water_title");
      else {
        return this.$i18n.t("pee_title");
      }
    },
    active() {
      return this.activeDelBtn ? "active-btn " : false;
    },
  },
  watch: {
    history(newVal) {
      // console.log(newVal);
      this.activeDelBtn = false;
    },
  },
  methods: {
    selectBtnHandler() {
      this.activeDelBtn = this.activeDelBtn ? false : true;
    },
    selectHandler(id) {
      // console.log(id);
      if (this.activeDelBtn) {
        this.deleteCareData(id);
      }
    },
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null
          ? subjects[selectedId].id
          : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async deleteCareData(id) {
      const subjectId = this.getSubjectId();
      try {
        const body =
          this.type === "weight" ? { weightId: id } : { urineId: id };
        const { status } = await API.DeleteCareData(subjectId, this.type, body);
        // this.selectUser > 0
        //   ? await API.DeleteCareData(this.selectUser, this.type, body)
        // : await API.DeleteCareData(this.subjecId, this.type, body);
        if (status === 204) {
          this.delSuccess = true;
          this.$emit("reloadData");
        }
      } catch (e) {
        console.log(e);
      }
    },
  },
  mounted() {},
};
</script>
<style lang="scss" scoped>
.weight-info {
  padding: 25px 0px;
  text-align: center;
  font-size: 14px;
  color: #646464;
  font-weight: 500;
}

p {
  margin: 0;
}

.history-table {
  margin-top: 10px;
  padding: 25px 30px 90px 30px;
  border-radius: 20px 20px 0px 0px;
  background-color: #fff;
}

.history-table-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5px solid #a7a7a7;
  padding-bottom: 10px;
}

.history-table-row {
  display: flex;
  justify-content: space-between;
  align-content: center;
  padding: 10px 0px;
}

.history-table-title {
  font-weight: 700;
  font-size: 18px;
  letter-spacing: -0.8px;
}

.select-btn {
  border-radius: 5px;
  border: 1px solid #ededed;
  padding: 0 10px;
  white-space: nowrap;
  color: #858585;
}

.active-btn {
  background-color: #41d8e6;
  border: 1px solid #41d8e6;
  color: #fff;
}
.del-btn {
  border-radius: 5px;
  border: 1px solid #ededed;
  padding: 0 10px;
  white-space: nowrap;
  color: #858585;
  &:active {
    background-color: #a7a7a7;
    border: 1px solid #a7a7a7;
    color: #fff;
  }
}

.body-left {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #a7a7a7;
  font-weight: normal;
  font-family: GilroyMedium !important;
}

.table-right {
  display: flex;
  gap: 10px;
}

.body-right {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 20px;
  font-weight: 400;
  font-family: GilroyMedium !important;
  color: #000;
}

.strong {
  color: #41d8e6;
}

.noData {
  text-align: left;
  padding: 0 30px;
}

.healthkit-btn__wrapper {
  text-align: right;
  letter-spacing: -0.05em;
  color: #a7a7a7;
  font-weight: 400;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.switch-label {
  padding-bottom: 10px;
  padding-right: 10px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0px !important;
}

::v-deep .v-input--selection-controls {
  margin: 0px !important;
  padding: 0px !important;
}

::v-deep .v-messages {
  display: none;
}

// snackbar custom
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
</style>
