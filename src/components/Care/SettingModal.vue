<template>
  <div>
    <div class="care-setting-modal__wrapper">
      <div class="care-setting-modal__nav dp-30">
        <div class="nav-item__canclebtn" @click="closeModalHandler">취소</div>
        <div class="nav-item__title">수분설정</div>
        <div class="nav-item__canclebtn">취소</div>
      </div>
      <div class="care-setting-modal__contents">
        <div class="content-items__wrapper">
          <div class="content-items">
            <div class="content-item">
              <div class="content-item__title">
                <input type="radio" />
                <span> 나의 목표 직접 설정</span>
              </div>
              <div class="content-item__input"><input type="text" /></div>
            </div>
          </div>
          <v-expansion-panels flat="true">
            <v-expansion-panel>
              <v-expansion-panel-header color="transparent">
                <div>
                  <input type="radio" />
                  <span> 나에게 맞는 섭취량</span>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div class="panel-content">
                  <div>
                    <div class="setting-field__wrapper">
                      <div class="setting-filed__title">성별</div>
                      <div class="setting-field__input">
                        <input type="text" class="setting-field" />
                      </div>
                    </div>
                    <div class="setting-field__wrapper">
                      <div class="setting-filed__title">키</div>
                      <div class="setting-field__input">
                        <input type="text" class="setting-field" />
                      </div>
                    </div>
                    <div class="setting-field__wrapper">
                      <div class="setting-filed__title">몸무게</div>
                      <div class="setting-field__input">
                        <input type="text" class="setting-field" />
                      </div>
                    </div>
                  </div>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    closeModalHandler() {},
  },
};
</script>

<style lang="scss" scoped>
.care-setting-modal__wrapper {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.81);
  opacity: 1;
  height: 95%;
  border-radius: 20px 20px 0px 0px;
}

.care-setting-modal__nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  // background-color: #000;
  border-radius: 20px 20px 0px 0px;
  // border-bottom: 1px solid #a7a7a7;
  div {
    color: #fff;
  }
}

.care-setting-modal__contents {
  padding: 40px 50px;
}

.content-item__title {
  // font-family: "Noto Sans KR";
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 29px;
  text-align: left;
  /* identical to box height */
  letter-spacing: -0.02em;
  /* WHT */
  color: #ffffff;
  padding-bottom: 10px;
}

.content-item__input {
  input {
    width: 100%;
    height: 60px;
    min-width: 250px;
    background: #ededed;
    border-radius: 5px;
    text-align: center;
    caret-color: #41d8e6;
    font-size: 35px;
    outline: none;
  }
}

::v-deep .v-expansion-panel-content__wrap {
  padding: 0;
}

::v-deep .theme--light.v-expansion-panels .v-expansion-panel {
  background-color: transparent !important;
  color: white;
}

::v-deep .v-expansion-panel-header {
  padding: 0;
  font-size: 20px;
}

.timeline-item {
  text-align: left;
}

.setting-field__wrapper {
  width: 100%;
}

.setting-filed__title {
  font-size: 16px;
  width: 100%;
  text-align: left;
}

.setting-field__input {
  width: 100%;
  padding: 10px 0px;

  input {
    width: 100%;
    height: 60px;
    background-color: #ededed;
    border-radius: 5px;
  }
}

.timeline-item__ic {
  display: flex;
  flex-direction: column;
}
</style>