<template>
  <div>
    <fixed-header :threshold="50">
      <div class="nav-bar">
        <div class="fixed-header" :class="isIos ? 'nav-space' : 'nav-space-android'">
          <div>
            <div class="history-header_nav">
              <router-link to="/home"><v-icon>$back_btn_bold</v-icon></router-link>
            </div>
          </div>
        </div>
      </div>
    </fixed-header>
    <div class="care-header">
      <div :class="isIos ? 'care-header__wrapper' : 'care-header__wrapper-android'">
        <div class="care-header_nav">
          <router-link to="/home"><v-icon>$back_btn_bold</v-icon></router-link>
        </div>
        <div class="care-header_title">
          <span :class="isKo ? 'care-header__title--ko' : 'care-header__title--en'">{{
            setTitle
          }}</span>
          <span class="care-header__title--en"> Care</span>
        </div>
        <div class="care-header_subtitle">
          <div>
            <div v-if="noData">
              <span class="weight-data">{{ $t("nodata") }}</span>
              <span class="care-header_subtitle--date">{{ date }}</span>
            </div>

            <div v-else>
              <span v-if="count === 'd'" class="weight-data"
                >{{ setSubComment }} {{ avgScore }}{{ unit }}</span
              >
              <span v-else-if="count !== 'd' && type === 'water'" class="weight-data">
                {{ setSubTitle }} {{ avgScore.toLocaleString("ko-KR") }}{{ unit }}
              </span>
              <span v-else class="weight-data">{{ setSubTitle }} {{ avgScore }} {{ unit }}</span>

              <span class="care-header_subtitle--date">{{ date }}</span>
            </div>
          </div>
          <!-- <div>
            <v-icon
              @click="openEditModalWindow"
              v-if="(type === 'water' && count === 'd') || (type === 'weight' && count === 'd')"
              >$setting_btn</v-icon
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FixedHeader from "vue-fixed-header";

export default {
  props: {
    type: String,
    count: String,
    value: Number,
    avgScore: Number,
    date: String,
    noData: Boolean,
  },
  components: { FixedHeader },
  data() {
    return {
      korTitle: "",
      subtitle: "",
      todayDate: "",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      isKo:
        this.$i18n.locale.includes("ko") ||
        this.$i18n.locale.includes("cn") ||
        this.$i18n.locale.includes("ja"),
    };
  },
  computed: {
    setSubTitle() {
      if (this.count === "w") return this.$i18n.t("avg_week_header");
      if (this.count === "m") return this.$i18n.t("avg_month_header");
      if (this.count === "y") return this.$i18n.t("avg_year_header");
      return "";
    },
    setSubComment() {
      if (this.type === "weight") return this.$i18n.t("recent_login");
      if (this.type === "water") return this.$i18n.t("target_water_value");
      if (this.type === "pee") return this.$i18n.t("today_pee_count");
      return "";
    },
    setTitle() {
      if (this.type === "weight") return this.$i18n.t("weight_title");
      if (this.type === "water") return this.$i18n.t("water_title");
      if (this.type === "pee") return this.$i18n.t("pee_title");
      return "";
    },
    unit() {
      if (this.type === "weight") return "kg";
      if (this.type === "water") return this.count === "d" ? "L" : "ml";
      if (this.type === "pee") return this.$i18n.t("times");
      return "";
    },
  },
  methods: {
    openEditModalWindow() {
      this.$emit("openEditModalWindow");
    },
  },

  mounted() {
    switch (this.type) {
      case "water":
        return (this.korTitle = this.$i18n.t("water_title"));
      case "weight":
        return (this.korTitle = this.$i18n.t("weight_title"));
      case "pee":
        return (this.korTitle = this.$i18n.t("pee_title"));
      default:
        return;
    }
  },
};
</script>

<style lang="scss" scoped>
.nav-bar.vue-fixed-header--isFixed .fixed-header {
  background-color: #c9f4f8;
  display: block !important;
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  z-index: 999;
}

.nav-bar.vue-fixed-header .fixed-header {
  display: none;
}

.nav-space {
  padding: 55px 0 15px 10px;
}
.nav-space-android {
  padding: 20px 0 15px 10px;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-left: 20px;
}

.care-header__wrapper {
  padding-top: 65px;
  padding-left: 30px;
}
.care-header__wrapper-android {
  padding-top: 50px;
  padding-left: 30px;
}

.care-header_nav {
  text-align: left;
}

.care-header_title {
  text-align: left;
  font-size: 28px;
  padding-top: 10px;
  line-height: 30px;
}

.care-header_subtitle {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  padding-top: 22px;
  padding-bottom: 10px;
  letter-spacing: -0.03em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 25px;
}

.care-header__title--ko {
  line-height: 30px;
  font-weight: 700;
  font-size: 30px;
  margin-bottom: 5px;
}

.care-header__title--en {
  line-height: 30px;
  font-family: GilroyBold;
  font-size: 36px;
}

.weight-data {
  font-size: 22px;
  font-weight: 500;
}

.care-header_subtitle--date {
  font-size: 14px;
  font-family: GilroyMedium;
  font-weight: 500;
  padding-left: 10px;
  color: #646464;
  letter-spacing: 0.01em;
}
</style>
