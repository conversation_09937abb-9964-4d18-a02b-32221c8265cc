<template>
  <div>
    <div class="bg-modal">
      <div class="week-trend-modal bottomToTop">
        <div class="week-trend-modal__header">
          <div class="header__title" v-if="page === 'pee'">{{ $t("urination_trend") }}</div>
          <div class="header__title" v-else>{{ $t("hydration_trend") }}</div>
          <div class="header__close-btn" @click="closeWeekTrendModal">
            <img src="@/assets/images_assets/icons/close-btn-solid-ic.png" alt="close-btn" />
          </div>
        </div>
        <div>
          <div class="scatter-chart__wrapper">
            <ScatterChart
              :weekDayData="weekDayData"
              :page="page"
              :scatterData="graphData"
              v-if="loaded"
              @getSpecificDate="getSpecificDate"
            />
          </div>
          <div class="chart-btn__wrapper">
            <ChartBtn count="w" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ScatterChart from "@/components/Chart/ScatterChart.vue";
import ChartBtn from "@/components/Care/CareChartBtns.vue";
import API from "@/api/care/index.js";
import dataProcessing from "@/assets/data/manufacturing/care.js";

export default {
  props: {
    page: String,
    specificDate: String,
  },
  components: {
    ScatterChart,
    ChartBtn,
  },
  data() {
    return {
      graphData: [],
      loaded: false,
      weekDayData: [],
    };
  },
  methods: {
    closeWeekTrendModal: function() {
      this.$emit("closeWeekTrendModal", false);
    },
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId = subjects !== null ? subjects[selectedId].id : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async getSpecificWeekData(specificDate, page) {
      const [year, month, date] = specificDate.split(".");
      const subjectId = this.getSubjectId();
      this.loaded = false;
      try {
        const start = new Date(new Date(`${year}-${month}-${date}T00:00:00`).setDate(date - 7)).getTime();
        // console.log(start);
        // const isKo = start.includes(".");
        // let [y, m, d] = isKo
        //   ? start.replace(/ /g, "").split(".") // y, m, d
        //   : start.replace(/ /g, "").split("/"); // m, d, y
        // y = y.length === 1 ? `0${y}` : y;
        // m = m.length === 1 ? `0${m}` : m;
        // d = d.length === 1 ? `0${d}` : d;
        // const utcStart = isKo
        //   ? new Date(`${y}-${m}-${d}T00:00:00`).toISOString()
        //   : new Date(`${d}-${y}-${m}T00:00:00`).toISOString();
        const utcStart = new Date(start).toISOString();
        const end = new Date(`${year}-${month}-${date}T00:00:00`).toISOString();
        const response = await API.GetCareData(subjectId, page, utcStart, end);

        if (response.data) {
          this.loaded = true;
          const ScatterData =
            page === "urine"
              ? dataProcessing.GET_WATER_CARE_DATA(response.data, "w")
              : dataProcessing.GET_WATER_CARE_DATA(response.data, "w");
          this.graphData = ScatterData.graphData.reverse();
          this.weekDayData = ScatterData.dayAvgData.reverse();
        }
      } catch (error) {
        console.log(error);
      }
    },
    getSpecificDate(specificDate) {
      // console.log(specificDate);
    },
  },
  mounted() {
    this.getSpecificWeekData(this.specificDate, this.page);
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
}

.week-trend-modal {
  position: absolute;
  bottom: 0px;
  width: 100%;
  padding-top: 30px;
  background-color: #fff;
  border-radius: 30px 30px 0px 0px;
}

.week-trend-modal__header {
  display: flex;
  justify-content: space-between;
  padding: 0 30px;
  .header__title {
    font-weight: 700;
    font-size: 14px;
    text-align: left;
  }
  .header__close-btn {
    img {
      width: 20px;
    }
  }
}

.scatter-chart__wrapper {
  padding-right: 20px;
  padding-bottom: 80px;
}

.chart-btn__wrapper {
  position: absolute;
  z-index: 99999999;
  bottom: 20px;
  width: 100%;
}

.bottomToTop {
  opacity: 0;
  animation: bottomToTop 0.7s forwards;
}

@keyframes bottomToTop {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
</style>
