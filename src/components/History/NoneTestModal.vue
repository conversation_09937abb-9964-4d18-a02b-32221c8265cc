<template>
  <div class="modal-background">
    <div class="modal-container">
      <div class="icon-container" @click="handleCloseModalClick">
        <img
          src="@/assets/images_assets/icons/close-btn-solid-ic.png"
          alt="close-icon"
          loading="lazy"
          width="20px"
          height="20px"
          class="close-icon"
        />
      </div>
      <div class="upper-container">
        <img
          src="@/assets/images/pet-kit-img.png"
          alt="pet-kit"
          loading="lazy"
          class="kit-image"
          width="220px"
          height="211px"
        />
        <span class="lock-text" :class="isKo ? 'isKo' : 'isEn'">{{
          $t("lock_text")
        }}</span>
        <button class="btn" @click="handleOpenShopClick">
          {{ $t("purchase_text") }}
        </button>
        <button class="btn" @click="handleGoAnalysisClick">
          {{ $t("analysis_text") }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    isTenAnalysisOptions: <PERSON>olean,
  },
  data() {
    return {
      isKo: false,
      isClosing: false,
    };
  },

  methods: {
    handleCloseModalClick() {
      this.$store.commit("GET_NO_DATA_LOCK_MODAL", false);
    },
    handleOpenShopClick() {
      const storeUrl_5 =
        "https://m.smartstore.naver.com/cym702/products/10404396947?nt_source=pet&nt_medium=app&nt_detail=mobile&nt_keyword=pet_kit_5";
      const storeUrl_10 =
        "https://m.smartstore.naver.com/cym702/products/10404396947?nt_source=pet&nt_medium=app&nt_detail=mobile&nt_keyword=pet_kit_5";
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const message = this.isTenAnalysisOptions ? storeUrl_10 : storeUrl_5;
      Webview.openUrl(message);
    },
    handleGoAnalysisClick() {
      this.$store.commit("GET_NO_DATA_LOCK_MODAL", false);
      this.$router.push("/exam/intro");
    },
  },

  mounted() {
    this.isKo = this.$i18n.locale.includes("ko") || this.$i18n.locale === "cn";
  },
};
</script>

<style lang="scss" scoped>
.modal-background {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-container {
  width: 100%;
  height: 345px;
  background-color: #ffffff;
  border-radius: 30px 30px 0 0;
  padding: 30px 8.33%;
  animation: slide-up 0.4s ease-out forwards;
}

.upper-container {
  height: calc(100% - 20px);
  transform: translateY(-65%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
}

.icon-container {
  width: 100%;
  height: 20px;
  position: relative;
  display: flex;
  justify-content: flex-end;
  z-index: 3;
}

.close-icon {
  aspect-ratio: 1/ 1;
  align-self: flex-end;
}

.kit-image {
  aspect-ratio: 220 / 211;
  height: auto;
}

.lock-text {
  margin: 17px 0 32px 0;
}

.isKo {
  font-family: Noto Sans KR !important;
  font-size: 16px;
  font-weight: 400;
  line-height: 23.17px;
  letter-spacing: -0.02em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.isEn {
  font-family: GilroyMedium !important;
  font-size: 16px;
  font-weight: 500;
  line-height: 18.83px;
  letter-spacing: -0.03em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.btn {
  height: 50px;
  position: relative;
  max-width: 340px;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 20px;
  font-weight: bold;
  background-color: #41d8e6;
  color: #ffffff;
}

.btn:first-of-type {
  margin-bottom: 15px;
}

.modal-container.closing {
  animation: slide-down 0.4s ease-out forwards;
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-out {
  from {
    transform: translateY(0%);
  }
  to {
    transform: translateY(100%);
  }
}
</style>
