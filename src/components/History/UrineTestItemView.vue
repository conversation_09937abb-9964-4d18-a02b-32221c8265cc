<template>
  <div class="history-tab-item__container">
    <div class="history-header__wrapper">
      <div
        v-if="
          !loading &&
          (historyData.length <= 0 ||
            (historyData.length > 0 && historyData.every((item) => item.level === null)))
        "
        class="lock-container"
        @click="handleLockClick"
      >
        <img src="@/assets/images/lock.png" alt="lock" loading="lazy" />
        <span v-if="isKo" class="lock-text__isKo"
          >{{ computedKorNam }} 항목 {{ this.$i18n.t("lock_open") }}</span
        >
        <span v-else class="lock-text"
          >{{ this.$i18n.t("lock_open") }} the {{ computedKorNam }} Parameter</span
        >
      </div>
      <LineChart
        :historyData="historyData"
        :clicked="count"
        :type="type"
        @changeCount="changeCount"
      />
      <PageHandle :totalCount="historyData.length" />
    </div>
    <div class="myStatus-section__wrapper">
      <StatueBar :cymScore="cymScore" :type="type" :status="status" :loading="loading" />
    </div>
    <GuideComponent :type="type" />
    <GuideModal v-if="showGuide" :type="type" />
  </div>
</template>

<script>
import LineChart from "./Apexcharts.vue";
import PageHandle from "./PageHandle.vue";
import StatueBar from "./StatusBar.vue";
import GuideComponent from "./GuideSection.vue";
import GuideModal from "./GuideModal.vue";

export default {
  props: {
    type: String,
    historyData: Array,
    totalDataLen: Number,
    loading: Boolean,
  },
  components: {
    LineChart,
    StatueBar,
    GuideComponent,
    GuideModal,
    PageHandle,
  },
  data() {
    return {
      count: "recent",
      loaded: false,
      isKo: true,
      cymScore: "",
      showLockContainer: false,
    };
  },

  computed: {
    computedKorNam() {
      const typeMap = {
        blood: this.$i18n.t("tab_blood"),
        glucose: this.$i18n.t("tab_glucose"),
        protein: this.$i18n.t("tab_protein"),
        ph: this.$i18n.t("tab_ph"),
        ketone: this.$i18n.t("tab_ketone"),
        leukocytes: this.$i18n.t("tab_leukocytes"),
        bilirubin: this.$i18n.t("tab_bilirubin"),
        urobilinogen: this.$i18n.t("tab_urobilinogen"),
        nitrite: this.$i18n.t("tab_nitrite"),
        sg: this.$i18n.t("tab_sg"),
      };
      const typeText = typeMap[this.type] || "";

      return typeText;
    },
    showGuide() {
      return this.$store.state.openGuide;
    },
    getHistoryData() {
      return this.historyData;
    },
    status() {
      if (this.historyData.length > 0) {
        return false;
      } else {
        return true;
      }
    },
  },

  watch: {
    showGuide(newVal) {
      console.log(newVal, this.scrollY);
      if (newVal) {
        this.scrollY = window.scrollY;
        // document.body.classList.add("no-scroll");
        // document.body.style.position = "fixed";
        // document.body.style.width = "100%";
        // document.body.style.top = `-${this.scrollY}px`;
        // document.body.style.overflowY = "scroll";
      } else {
        // document.body.classList.remove("no-scroll");
        // window.scrollTo(0, this.scrollY);
        // document.body.style.position = "";
        // document.body.style.width = "";
        // document.body.style.top = "";
        // document.body.style.overflowY = "";
        // window.scrollTo(0, this.scrollY);
      }
    },
    scrollY(newVal) {},
  },

  methods: {
    changeCount(count, page) {
      // console.log(count);
      console.log(count, page);
      this.$emit("changeCount", count, page);
      // this.getHistoryData();
    },
    handleLockClick() {
      this.$store.commit("GET_NO_DATA_LOCK_MODAL", true);
    },
  },
  mounted() {
    this.$emit("changeCount", this.count);
    window.scrollTo(0, 0);
    this.isKo = this.$i18n.locale.includes("ko") || this.$i18n.locale === "cn";
    // console.log("history data", this.getHistoryData);
  },
};
</script>

<style lang="scss" scoped>
.history-header__wrapper {
  padding: 50px 0 15px;
  margin: auto;
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.myStatus-section__wrapper {
  padding: 25px 30px;
}
.mystate-indicator-title {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  letter-spacing: -0.03em;
}

.no-result {
  font-size: 16px;
  padding: 5px 0px;
  color: #a7a7a7;
  text-indent: 5px;
  font-size: 16px;
  font-weight: 400;
  line-height: 23.17px;
  letter-spacing: -0.03em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  text-align: left;
}

.lock-container {
  width: 100%;
  height: 20%;
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 100;

  > img {
    width: 173px;
    height: 173px;
  }
}

.lock-text {
  font-family: GilroyBold !important;
  font-size: 16px;
  font-weight: 800;
  line-height: 19.6px;
  letter-spacing: -0.03em;
  text-align: right;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #dadada;
}

.lock-text__isKo {
  font-family: Noto Sans KR !important;
  font-size: 16px;
  font-weight: 700;
  line-height: 23.17px;
  letter-spacing: -0.03em;
  text-align: right;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #dadada;
}
</style>
