<template>
  <div>
    <div class="mystate-indicator-title">
      <div :class="isKo ? 'txt----ko' : 'txt----en'">
        {{ username }}{{ $t("users") }} {{ computedKorNam }} {{ $t("status") }}
      </div>
    </div>
    <div class="p-30">
      <div class="mystste-indicator-wrapper">
        <div class="mystate-indicator">
          <div
            class="indicator__item"
            v-for="(item, index) in items"
            :key="index"
            :class="index === curInfo ? itemInfo : ''"
          >
            {{ item }}
            <span class="ph-subtitle" v-if="type === 'ph' && index === 0">&nbsp;(pH5~pH8)</span>
            <span class="ph-subtitle" v-if="type === 'ph' && index === 1">&nbsp;(pH9)</span>
          </div>
        </div>
      </div>
    </div>
    <div class="no-result" v-if="!loading && status">
      {{ $t("cym_no_data") }}
    </div>
    <div class="mystatusTxt-explain__wrapper" v-else>
      <p class="mystatusTxt-explain" v-html="statusTxt"></p>
    </div>
  </div>
</template>
<script>
import { historyTypes } from "@/assets/data/data.js";
import { mapGetters } from "vuex";

export default {
  props: {
    // username: String,
    // historyData: Array,
    type: String,
    status: Boolean,
    loading: Boolean,
  },

  // TODO: 항목별 상태 설명과 상태알림기능
  data() {
    return {
      items: [],
      statusTxt: "",
      username: "",
      isKo: this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn") || this.$i18n.locale.includes("ja"),
    };
  },
  methods: {
    openEditModalWindow() {
      this.$store.commit("openKetoneEditModal", true);
    },
    getTranslatedStatus(key) {
      if (this.isKo) {
        if (key === "warning") return this.$i18n.t("warning");
        if (key === "caution") return this.$i18n.t("caution");
      }
      return this.$i18n.t(key);
    },
    updateStatusText() {
      console.log("updateStatusText called - type:", this.type, "cymScore:", this.cymScore);
      // historyTypes 함수를 호출하여 statusTxt 업데이트
      this.statusTxt = historyTypes(this.type, this.cymScore);
      console.log("statusTxt updated to:", this.statusTxt);
    },
    statusbarColorHandler() {
      console.log("h123", this.cymScore);
      if (this.type === "blood" || this.type === "glucose" || this.type === "protein") {
        this.items = [
          this.getTranslatedStatus("normal"),
          this.getTranslatedStatus("caution"),
          this.getTranslatedStatus("warning"),
          this.getTranslatedStatus("danger"),
        ];
      } else if (this.type === "ph") {
        this.items = [this.getTranslatedStatus("normal"), this.getTranslatedStatus("caution")];
      } else if (this.type === "ketone") {
        this.items = [
          this.getTranslatedStatus("normal"),
          this.getTranslatedStatus("caution"),
          this.getTranslatedStatus("warning"),
          this.getTranslatedStatus("danger"),
        ];
      } else if (this.type === "nitrite") {
        this.items = [this.getTranslatedStatus("normal"), this.getTranslatedStatus("danger")];
      } else {
        this.items = [
          this.getTranslatedStatus("normal"),
          this.getTranslatedStatus("caution"),
          this.getTranslatedStatus("warning"),
          this.getTranslatedStatus("danger"),
        ];
      }

      // 초기 statusTxt 설정
      this.updateStatusText();

      this.username = localStorage.getItem("username");
      // console.log(this.username);
      // this.$store.state.username;
    },
  },
  mounted() {
    this.statusbarColorHandler();
  },
  computed: {
    ...mapGetters(["cymScore"]),
    computedKorNam() {
      switch (this.type) {
        case "blood":
          return this.$i18n.t("hematuria");
        case "glucose":
          return this.$i18n.t("glycosuria");
        case "protein":
          return this.$i18n.t("proteinuria");
        case "ph":
          return this.$i18n.t("tab_ph");
        case "ketone":
          return this.$i18n.t("ketone_status");
        case "leukocytes":
          return this.$i18n.t("wbc");
        case "bilirubin":
          return this.$i18n.t("bil");
        case "urobilinogen":
          return this.$i18n.t("uro");
        case "nitrite":
          return this.$i18n.t("nit");
        case "sg":
          return this.$i18n.t("s.g");
        default:
          return "default";
      }
    },

    itemInfo() {
      if (this.type === "ketone") {
        console.log(this.cymScore.includes("caution"));
        if (this.cymScore.includes("caution")) {
          return "ketone-caution";
        }

        return "ketone-" + this.cymScore;
      }

      return "cym-" + this.cymScore;
    },

    curInfo() {
      console.log("curInfo", this.cymScore);
      if (this.cymScore === null || this.cymScore === undefined || this.cymScore === "") {
        return 0;
      }

      if (this.cymScore === "normal") {
        return 0;
      } else if (
        this.cymScore === "caution" ||
        this.cymScore === "caution_plus_minus" ||
        this.cymScore === "caution_plus"
      ) {
        return 1;
      } else if (this.cymScore === "warning") {
        return 2;
      } else if (this.cymScore === "danger") {
        if (this.type === "nitrite") {
          return 1;
        }

        return 3;
      } else {
        if (this.cymScore === null) {
          console.log("null value");
        }
        return 0;
      }
    },
  },
  watch: {
    curInfo(newVal) {
      // console.log(newVal);
    },
    type(newVal) {
      // console.log("type", newVal);
    },
    cymScore(newVal) {
      console.log("cymScore changed:", newVal);
      // cymScore가 변경될 때마다 statusTxt 업데이트
      this.updateStatusText();
    },
    // store의 historyLevel 값을 watch
    "$store.state.cymHistory.historyLevel"(newVal) {
      console.log("historyLevel changed:", newVal);
      // historyLevel이 변경될 때마다 statusTxt 업데이트
      this.updateStatusText();
    },
    // historyData(newVal) {
    //   // console.log("history data:", newVal);
    //   this.statusbarColorHandler()
    // },
  },
};
</script>

<style lang="scss" scoped>
@mixin cymStateBar($bg-color) {
  background-color: $bg-color;
  color: #fff;
  font-weight: bold;
}

@mixin ketoneStateBar($bg-color, $color: white) {
  background-color: $bg-color;
  color: $color;
  font-weight: bold;
}

$black: #000000;
$danger: #646464;
$green: #00bb00;
$yellow: #ffcc00;
$red: #ff6600;

$ketone-0: #00bb00;
$ketone-1: #ffcc00;
$ketone-2: #ffcc00;
$ketone-3: #ff6600;
$ketone-4: #646464;

// ketoneMode
$ketone-exertion: #fff8f4;
$ketone-enter: #fac9bf;
$ketone-ketone_normal: #f3b6b6;
$ketone-ketone_good: #b83a71;
$ketone-ketone_warning: #8f3f6e;

$primary: #41d8e6;
$lightblue: #c9f4f8;

.mystate-indicator-title {
  display: flex;
  justify-content: center;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  letter-spacing: -0.03em;
}

.setting-icon {
  position: absolute;
  right: 30px;
}

.mystate-indicator {
  display: flex;
  border-radius: 5px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ededed;
  margin: 15px 0px;
}

.indicator__item {
  flex: 1;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  color: #a7a7a7;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: -0.4px;
}

.txt----ko {
  font-family: Noto Sans KR !important;
}

.txt----en {
  font-family: GilroyBold !important;
}

.ph-subtitle {
  font-size: 12px;
  line-height: 32px;
  // font-family: Noto Sans KR;
}

.mystatusTxt-explain {
  text-align: left;
  // font-family: "Noto Sans KR";
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 23px;
  letter-spacing: -0.02em;
  margin-bottom: 0;
}

/* cym 702 state bar */
.cym702-good {
  @include cymStateBar($primary);
  border-radius: 5px 0px 0px 5px;
}

.cym702-normal {
  @include cymStateBar($green);
  border-radius: 0px;
}

.cym702-caution {
  @include cymStateBar($yellow);
}

.cym702-warning {
  @include cymStateBar($red);
  border-radius: 0px 5px 5px 0px;
}

/* blood, protein, glucose state bar */
.cym-normal {
  @include cymStateBar($green);
  border-radius: 5px 0px 0px 5px;
}

.cym-warning {
  @include cymStateBar($red);
}

.cym-caution {
  @include cymStateBar($yellow);
}

.cym-danger {
  @include cymStateBar($danger);
  border-radius: 0px 5px 5px 0px;
}

/* ph state bar */
.ph-warning {
  @include cymStateBar($yellow);

  border-radius: 0px 5px 5px 0px;
}

.ph-good {
  @include cymStateBar($green);
  border-radius: 0px 5px 5px 0px;
}

/* ketone state bar */
.ketone-normal {
  @include ketoneStateBar($ketone-0);
  border-radius: 5px 0px 0px 5px;
}
.ketone-exertion {
  @include ketoneStateBar($ketone-exertion);
  color: #646464;
  border-radius: 5px 0px 0px 5px;
}

.ketone-caution {
  @include ketoneStateBar($ketone-1);
}
.ketone-enter {
  @include ketoneStateBar($ketone-enter);
}

.ketone-caution {
  @include ketoneStateBar($ketone-2);
}
.ketone-ketone_normal {
  @include ketoneStateBar($ketone-ketone_normal);
}

.ketone-warning {
  @include ketoneStateBar($ketone-3);
}
.ketone-ketone_good {
  @include ketoneStateBar($ketone-ketone_good);
}
.ketone-ketone_warning {
  border-radius: 0px 5px 5px 0px;
  @include ketoneStateBar($ketone-ketone_warning);
}

.ketone-danger {
  border-radius: 0px 5px 5px 0px;
  @include ketoneStateBar($ketone-4);
}

.no-result {
  font-size: 14px;
  padding: 5px 0px;
  color: #646464;
  font-weight: 500;
  text-indent: 5px;
  text-align: left;
}
</style>
