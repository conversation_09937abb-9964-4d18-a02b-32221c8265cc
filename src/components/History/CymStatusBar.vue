<template>
  <div>
    <div class="cym702score-txt__wrapper">
      <div class="pt-30">
        <div class="cymscore-title" v-html="$t('cym_score')"></div>
        <div class="icon__wrapper">
          <v-icon size="20" color="#41D8E6" @click="openCymScore">mdi-help-circle-outline </v-icon>
        </div>
      </div>
      <div class="float-right" v-if="noData"></div>
      <div class="float-right" v-else-if="initialState">
        <div v-if="isKo" class="float-right">
          {{ totalCount }}{{ $t("test_count") }} {{ totalScore }}{{ $t("score") }}
        </div>
        <div v-else class="float-right">
          {{ $t("test_count") }} {{ totalCount }} {{ $t("score") }}
          {{ totalScore }}
        </div>
        <div class="float-test-date"></div>
        <!-- {{ cymAvg }} -->
      </div>
      <div class="float-right" v-else>
        <div class="float-right">{{ cymScore }}{{ $t("avg_score") }}</div>
      </div>
    </div>

    <div class="p-30">
      <div class="mystste-indicator-wrapper">
        <div class="mystate-indicator">
          <div
            class="indicator__item"
            v-for="(item, index) in items"
            :key="index"
            :class="index === curInfo ? itemInfo : ''"
          >
            {{ item }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";

export default {
  props: {
    initialState: Boolean,
    cymtestDate: String,
    resultExposeTest: String,
    noData: Boolean,
    totalCount: Number,
    totalScore: Number,
  },

  // TODO: 항목별 상태 설명과 상태알림기능
  data() {
    return {
      currentPosition: null,
      cymScore: "",
      items: [
        this.$i18n.t("good"),
        this.$i18n.t("normal"),
        this.$i18n.t("warning"),
        this.$i18n.t("caution"),
      ],
      isKo: true,
    };
  },
  watch: {
    totalScore(newVal) {
      // console.log(newVal);
    },
    cymAvg(newVal) {
      // console.log(newVal);
      this.cymScore = newVal;
    },
  },
  computed: {
    ...mapGetters(["cymAvg"]),
    itemInfo() {
      return "cym702-" + this.makeScoreText(this.cymAvg);
    },
    curInfo() {
      if (this.cymAvg >= 0 && this.cymAvg <= 100) {
        return Math.floor((100 - this.cymAvg) / 25);
      }

      return "";
    },
  },
  methods: {
    makeScoreText(score) {
      if (score >= 0 && score <= 100) {
        const levels = ["good", "normal", "caution", "warning"];
        return levels[Math.floor((100 - score) / 25)];
      }
      return "";
    },
    openCymScore() {
      this.$store.commit("SHOWGUIDE");
    },
  },
  mounted() {
    this.isKo = this.$i18n.locale.includes("ko") || this.$i18n.locale === "cn";
  },
};
</script>

<style lang="scss" scoped>
@mixin cymStateBar($bg-color) {
  background-color: $bg-color;
  color: #fff;
  font-weight: bold;
}

@mixin ketoneStateBar($bg-color, $color: white) {
  background-color: $bg-color;
  color: $color;
  font-weight: bold;
}

$black: #000000;
$danger: #ee0000;
$green: #00bb00;
$yellow: #ffcc00;
$red: #ff6600;

$ketone-0: #00bb00;
$ketone-1: #00bb00;
$ketone-2: #41d8e6;
$ketone-3: #ffcc00;

$primary: #41d8e6;
$lightblue: #c9f4f8;

.mystate-indicator-title {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 24px;
  text-align: center;
  letter-spacing: -0.03em;
}

.mystate-indicator {
  display: flex;
  border-radius: 5px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ededed;
  margin: 15px 0px;
}

.indicator__item {
  flex: 1;
  text-align: center;
  line-height: 28px;
  font-size: 16px;
  letter-spacing: -0.05em;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #a7a7a7;
}

// .mystatusTxt-explain {
//   text-align: justify;
//    ;
//   font-style: normal;
//   font-weight: 400;
//   font-size: 16px;
//   line-height: 23px;
//   letter-spacing: -0.02em;
// }

/* cym 702 state bar */
.cym702-good {
  @include cymStateBar($primary);
  border-radius: 5px 0px 0px 5px;
}

.cym702-normal {
  @include cymStateBar($green);
  border-radius: 0px;
}

.cym702-caution {
  @include cymStateBar($yellow);
}

.cym702-warning {
  @include cymStateBar($red);
  border-radius: 0px 5px 5px 0px;
}

.cym702score-txt__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.pt-30 {
  height: 100%;
  display: flex;
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 23px;
  align-items: center;
  text-align: left;
  letter-spacing: -0.05em;
}

.cymscore-title {
  font-size: 18px;
  font-weight: 700;
}

.icon__wrapper {
  padding: 0 0 2px 3px;
}

.float-right {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: -0.05em;
}

.float-test-date {
  font-size: 10px;
  color: #a7a7a7;
}

i {
  font-size: 1rem;
}

@media screen and (max-width: 375px) {
  .mystate-indicator-title {
    font-size: 16px;
  }
  .cymscore-title {
    font-size: 16px;
  }
  .float-right {
    font-size: 16px;
  }
  .indicator__item {
    font-size: 16px;
  }
}
</style>
