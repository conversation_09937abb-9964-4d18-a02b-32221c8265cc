<template>
  <div>
    <div class="cymchart__wrapper">
      <div
        class="float-box"
        v-if="totalCount > 0 && (showFloatBox || tabChanged)"
      >
        <div v-if="isKo" class="float-box__title ko_title">
          {{ totalCount }}{{ $t("test_count") }} {{ avgScore }}{{ $t("score") }}
        </div>

        <div v-else class="float-box__title en_title">
          {{ $t("test_count") }} {{ totalCount }} {{ $t("score") }}
          {{ avgScore }}
        </div>

        <div v-if="startDate !== ''" class="float-box__date">
          {{ startDate }} ~ {{ endDate }}
        </div>
      </div>

      <div class="history-header__wrapper">
        <div
          v-if="!loading && totalCount <= 0"
          class="lock-container"
          @click="handleLockClick"
        >
          <img src="@/assets/images/lock.png" alt="lock" />
          <span v-if="isKo" class="lock-text__isKo"
            >심점수 {{ $t("lock_open") }}</span
          >
          <span v-else class="lock-text lock-en"
            >{{ $t("lock_open") }} Cym Score</span
          >
        </div>
        <line-chart
          v-if="loaded"
          :historyData="historyData"
          :page="type"
          :count="count"
          @clickEventHandler="clickEventHandler"
        ></line-chart>
        <PageHandleVue :totalCount="totalCount" />
      </div>
    </div>

    <div class="my-status-section__wrapper">
      <StatusBar
        :initialState="initialState"
        :resultExposeTest="floatTitle"
        :cymtestDate="floatDate"
        :noData="status"
        :totalScore="avgScore"
        :totalCount="totalCount"
      />
      <span v-if="!loading && totalCount <= 0" class="no-result">{{
        $t("cym_no_data")
      }}</span>
    </div>
  </div>
</template>

<script>
import LineChart from "@/components/Chart/LineChart.vue";
import StatusBar from "@/components/History/CymStatusBar.vue";
import PageHandleVue from "./PageHandle.vue";
// import ChartBtn from "@/components/History/ChartBtn.vue";
import { mapGetters } from "vuex";

export default {
  name: "Cym702",
  props: {
    historyData: Array,
    totalCount: Number,
    totalPage: Number,
    avgScore: Number,
    startDate: String,
    endDate: String,
    loading: Boolean,
  },
  components: {
    LineChart,
    StatusBar,
    PageHandleVue,
    // ChartBtn,
  },
  data() {
    return {
      type: "cym",
      count: "recent",
      loaded: false,
      chartData: [],
      chartLabels: [],
      fullTimeDate: [],
      cymScore: "",
      floatTitle: "",
      floatDate: "",
      resultExposeTest: "",
      showFloatBox: true,
      isKo: true,
      initialState: true,
      limitArr: [5, 10, 15, 20, 25, 30],
      // page: 1,
      // limit: 5,
      // totalPage: 1,
      isPrevData: false,
      isNextData: false,
    };
  },
  computed: {
    ...mapGetters(["cymAvg"]),
    status() {
      if (this.historyData.length > 0) {
        return false;
      } else {
        return true;
      }
    },
    setHistoryData() {
      return this.historyData;
    },
    tabChanged() {
      return this.$store.state.tabChange;
    },
  },
  watch: {
    historyData(newVal) {
      // console.log(newVal);
      this.chartData = newVal;
      this.loaded = true;
    },
    cymAvg(newVal) {
      // console.log(newVal);
      this.cymScore = newVal;
    },
    avgScore() {
      this.$store.commit("GET_CYM_AVG", this.avgScore);
    },
    tabChanged() {
      this.$store.commit("GET_CYM_AVG", this.avgScore);
    },
  },
  methods: {
    handleLockClick() {
      console.log("hi");
      this.$store.commit("GET_NO_DATA_LOCK_MODAL", true);
    },
    clickEventHandler() {
      this.showFloatBox = false;
      this.initialState = false;
      this.$store.commit("SET_TAB_STATE", false);
    },
    changeCount(count) {
      // console.log("count clicked");
      this.loaded = false;
      this.count = count;
      this.showFloatBox = true;
      this.$store.commit("GET_CYM_AVG", this.avgScore);
      this.$emit("changeCount", count);
      this.getHistoryData();
      this.initialState = true;
    },
    getHistoryData() {
      this.loaded = true;
      this.chartData = this.historyData;
      this.$store.commit("GET_CYM_AVG", this.avgScore);
    },
  },
  mounted() {
    // console.log("cym score data", this.historyData);
    // console.log(this.startDate);
    // console.log(this.totalPage);
    this.getHistoryData();
    this.isKo = this.$i18n.locale.includes("ko") || this.$i18n.locale === "cn";
    // this.isNextData = this.$store.state.historyPage <= 1 ? false : true;
    // this.isPrevData = this.totalPage > this.$store.state.historyPage ? true : false;
  },
};
</script>

<style lang="scss" scoped>
.cymchart__wrapper {
  padding-top: 50px;
  padding-bottom: 15px;
  margin: auto;
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.my-status-section__wrapper {
  min-height: 30vh;
  padding: 10px 30px 20vh;
}

.float-box {
  position: absolute;
  top: 15px;
  left: 60px;
  background-color: #ededed;
  padding: 0px 5px;
  border-radius: 5px;
  letter-spacing: -0.03em !important;

  .float-box__title {
    font-size: 22px;
    line-height: 29px;
    color: #000000;
  }

  .float-box__date {
    font-family: GilroyMedium;
    color: #646464;
    font-size: 14px;
    font-weight: 400;
    line-height: 17px;
    letter-spacing: 0em;
    text-align: center;
  }
}
.chart-btn__wrapper {
  padding: 0px 30px 0 53px;
  display: flex;
  justify-content: space-between;
}

.select-box {
  border: 1px solid #ededed;
  border-radius: 5px;
  height: 100%;
  padding: 5px 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  position: absolute;
  top: 0;
  img {
    width: 13px;
  }
}

.btn__wrapper {
  // width: 100%;
  display: flex;
  align-items: center;
  gap: 25px;
  padding: 0 0.2rem 0 0 !important;
  img {
    width: 12px;
    height: 19.2px;
  }
}

.limit-list {
  position: relative;
  display: flex;
  flex-direction: column;
  top: 0;
  height: 100%;
}

.no-result {
  font-size: 16px;
  padding: 5px 0px;
  color: #a7a7a7;
  text-indent: 5px;
  font-size: 16px;
  font-weight: 400;
  line-height: 23.17px;
  letter-spacing: -0.03em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  text-align: left;
}

.lock-container {
  width: 100%;
  height: 30%;
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;

  > img {
    width: 173px;
    height: 173px;
  }
}

.lock-text {
  font-family: GilroyMedium;
  font-size: 16px;
  font-weight: 800;
  line-height: 19.6px;
  letter-spacing: -0.03em;
  text-align: right;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #dadada;
}

.lock-text__isKo {
  font-family: Noto Sans KR;
  font-size: 16px;
  font-weight: 700;
  line-height: 23.17px;
  letter-spacing: -0.03em;
  text-align: right;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #dadada;
}

.ko_title {
  font-family: Noto Sans KR !important;
  font-weight: 700;
}

.en_title {
  font-family: GilroyBold !important;
  font-weight: normal;
}
</style>
