<template>
  <div class="chart__wrapper">
    <apexchart width="100%" height="250" type="line" :options="chartOptions" :series="series"></apexchart>
    <div class="tooltip">
      <div class="tooltip-title">적절</div>
      <div class="tooltip-time">22.06.28 12:29AM</div>
    </div>
  </div>
</template>

<script>
import { computeYaxis, computeKorScore, computeEnScore, computeColor } from "../utils/computedChartOptions.js";

export default {
  props: {
    chartData: Array,
    clicked: Number,
    chartLabels: Array,
    type: String,
  },
  data() {
    const vm = this;
    return {
      getLastPointXPosition: 0,
      getLastPointYPosition: 0,
      color: "",
      score: 2,
      series: [
        {
          name: "Series 1",
          data: this.chartData,
        },
      ],
      chartOptions: {
        chart: {
          offsetX: 0,
          animations: {
            enabled: false,
          },

          dropShadow: {
            enabled: true,
            color: "#A7A7A7",
            top: 5,
            left: 0,
            blur: 2,
            opacity: 0.15,
          },
          toolbar: {
            show: false,
          },
          states: {
            active: {
              allowMultipleDataPointsSelection: true,
            },
          },
          events: {
            mounted(chartContext, config) {
              const getLastPointXPosition = config.globals.seriesXvalues[0][4];
              if (getLastPointXPosition) {
                const xcrosshairs = document.querySelectorAll(".apexcharts-xcrosshairs");
                const tooltip = document.querySelectorAll(".tooltip");
                xcrosshairs.forEach((line) => {
                  line.setAttribute("x1", `${getLastPointXPosition}`);
                  line.setAttribute("x2", `${getLastPointXPosition}`);
                  line.setAttribute("y1", `-10`);
                });

                tooltip.forEach((item) => {
                  item.style.left = `${getLastPointXPosition - 10}px`;
                });
              }
            },
            updated(chartContext, config) {
              const getLastPointXPosition = config.globals.seriesXvalues[0][4];
              if (getLastPointXPosition) {
                const xcrosshairs = document.querySelectorAll(".apexcharts-xcrosshairs");
                const tooltip = document.querySelectorAll(".tooltip");
                xcrosshairs.forEach((line) => {
                  line.setAttribute("x1", `${getLastPointXPosition}`);
                  line.setAttribute("x2", `${getLastPointXPosition}`);
                  line.setAttribute("y1", `-10`);
                });

                tooltip.forEach((item) => {
                  item.style.left = `${getLastPointXPosition - 10}px`;
                });
              }
            },
          },
        },

        dataLabels: {
          enabled: false,
        },

        colors: ["#A7A7A7"],

        // 점 point markers 스타일링
        markers: {
          size: vm.clicked === 5 ? 4.5 : 0,
          strokeColor: ["#A7A7A7"],
          colors: ["#fff"],
          radius: 2,
          hover: {
            size: 5,
          },
        },
        stroke: {
          width: 2.5,
          lineCap: "butt",
          curve: vm.clicked === 5 ? "straight" : "smooth",
        },

        // x축 라벨 스타일, 옵션
        xaxis: {
          tooltip: {
            enabled: false,
          },
          axisBorder: {
            show: false,
          },
          categories: vm.chartLabels,

          labels: {
            rotate: 0,
            rotateAlways: false,
            style: {
              colors: ["#646464"],
              cssClass: "apexcharts-yaxis-label",
            },
            formatter(value) {
              if (vm.chartData.length > 5) {
                if (value % 5 === 0) {
                  return value;
                } else {
                  return "";
                }
              } else {
                return value;
              }
            },
          },
        },

        // y축 라벨 스타일, 옵션
        yaxis: {
          logBase: 1,
          title: {
            text: vm.type === "blood" ? "RBC/µL" : vm.type === "ph" ? "" : "mg/dL",
            offsetY: 80,
            offsetX: 20,
            style: {
              color: "#a7a7a7",
              fontSize: "10px",
            },
          },
          min: 1,
          max() {
            if (vm.type === "protein" || vm.type === "glucose") return 6;
            else if (vm.type === "blood") return 4;
            else return 5;
          },
          labels: {
            style: {
              colors: ["#646464"],
              cssClass: "apexcharts-yaxis-label",
            },
            offsetX: 3,
            formatter(value) {
              if (value === 1) return computeYaxis(vm.type, value);
              if (value === 2) return computeYaxis(vm.type, value);
              if (value === 3) return computeYaxis(vm.type, value);
              if (value === 4) return computeYaxis(vm.type, value);
              if (value === 5) return computeYaxis(vm.type, value);
              if (value === 6) return computeYaxis(vm.type, value);
            },
          },
        },

        // 그래프 그리디 선
        grid: {
          yaxis: {
            lines: {
              show: true,
              colors: ["#646464"],
            },
          },
          strokeDashArray: 2,
        },

        tooltip: {
          custom({ series, seriesIndex, dataPointIndex, w }) {
            const points = document.querySelectorAll(".apexcharts-marker");
            const seriesXvalue = w.globals.seriesXvalues[0];
            const tooltip = document.querySelectorAll(".tooltip");

            points.forEach((point, idx) => {
              if (idx === dataPointIndex) {
                point.style.stroke = "#000";
              } else {
                point.style.stroke = "#a7a7a7";
              }
            });

            tooltip.forEach((item) => {
              if (seriesXvalue[dataPointIndex] > 250) {
                item.style.left = `${seriesXvalue[dataPointIndex] - 10}px`;
              } else if (seriesXvalue[dataPointIndex] < 5) {
                item.style.left = `${seriesXvalue[dataPointIndex] + 10}px`;
              } else {
                item.style.left = `${seriesXvalue[dataPointIndex]}px`;
              }
            });

            return "";
          },
        },
      },
    };
  },

  mounted() {
    // console.log(this.chartData);
  },
};
</script>

<style scoped>
::v-deep .chart__tooltip {
  background-color: #ededed !important;
  text-align: center;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 0px !important;
}

::v-deep .chart_tooltip_date {
  font-size: 10px;
  font-weight: 500;
  color: #646464;
}

::v-deep .normal {
  color: #00bb00;
}

::v-deep .warning {
  color: #ffcc00;
}

::v-deep .caution {
  color: #ff6600;
}

::v-deep .danger {
  color: #646464;
}

::v-deep .good {
  color: #41d8e6;
}
::v-deep .apexcharts-tooltip {
  top: -25px !important;
  box-shadow: none !important;
  border: none !important;
}

::v-deep text {
  /* font-size: 14px; */
  font-family: GilroyMedium !important;
  color: #a7a7a7;
}

::v-deep .apexcharts-yaxis-label {
  font-size: 14px;
  font-weight: 500;
  font-family: GilroyMedium !important;
}

::v-deep .apexcharts-xcrosshairs {
  stroke-dasharray: 0 !important;
  opacity: 1 !important;
  stroke: #ededed !important;
}

::v-deep .apexcharts-canvas {
  position: relative !important;
}

.point {
  width: 12px;
  height: 12px;
  border: 2px solid black;
  border-radius: 50%;
  position: absolute;
}

.tooltip {
  position: absolute;
  top: 10px;
  background: #ededed;
  border-radius: 5px;
  padding: 2px 3px;
}

.tooltip-title {
  font-size: 20px;
  font-weight: bold;
}

.tooltip-time {
  font-family: GilroyMedium;
  font-size: 11px;
  color: #a7a7a7;
}
</style>
