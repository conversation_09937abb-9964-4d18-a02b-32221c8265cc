<template>
  <div>
    <div class="resultTypo mx-auto text-center mb-1">
      <span> {{ username }} 님의 케톤 상태 </span>
    </div>

    <div class="p-30">
      <div class="mystste-indicator-wrapper">
        <div class="mystate-indicator">
          <div
            class="indicator__item"
            v-for="(item, index) in items"
            :key="index"
            :class="index === curInfo ? itemInfo : ''"
          >
            {{ item }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cymScore: String,
    username: String,
  },
  data() {
    return {
      currentPosition: 0,
      items: [
        `${this.$t("ketone_good_level")} (-)`,
        `${this.$t("ketone_good_level")} (+)`,
        this.$t("ketone_great_level"),
        this.$t("ketone_danger_level"),
      ],
    };
  },

  computed: {
    itemInfo() {
      if (this.cymScore === "normal") {
        return "ketone-normal";
      } else if (this.cymScore === "good") {
        return "ketone-good";
      } else if (this.cymScore === "warning") {
        return "ketone-warning";
      } else {
        return this.cymScore;
      }
    },

    curInfo() {
      if (this.cymScore === "normal") {
        return 1;
      } else if (this.cymScore === "good") {
        return 2;
      } else {
        return 3;
      }
    },
  },
};
</script>

<style lang="scss"></style>
