export const computeResult = (type, value, en, ko, color) => {
  if (type === "blood") {
    if (value === 1) {
      if (en === "en") {
        return "normal";
      } else if (ko !== "") {
        return "적절";
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 2) {
      if (en !== "") {
        return "warning";
      } else if (ko !== "") {
        return "주의";
      } else if (color !== "") {
        return "#ffcc00";
      }
    } else if (value === 3) {
      if (en !== "") {
        return "caution";
      } else if (ko !== "") {
        return "경고";
      } else if (color !== "") {
        return "#ff6600";
      }
    } else if (value === 4) {
      if (en !== "") {
        return "danger";
      } else if (ko !== "") {
        return "위험";
      } else if (color !== "") {
        return "#646464";
      }
    }
  } else if (type === "protein" || type === "glucose") {
    if (value === 1 || value === 2) {
      if (en === "en") {
        return "normal";
      } else if (ko !== "") {
        return "적절";
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 3) {
      if (en !== "") {
        return "warning";
      } else if (ko !== "") {
        return "주의";
      } else if (color !== "") {
        return "#ffcc00";
      }
    } else if (value === 4) {
      if (en !== "") {
        return "caution";
      } else if (ko !== "") {
        return "경고";
      } else if (color !== "") {
        return "#ff6600";
      }
    } else if (value === 5 || value === 6) {
      if (en !== "") {
        return "danger";
      } else if (ko !== "") {
        return "위험";
      } else if (color !== "") {
        return "#646464";
      }
    }
  } else if (type === "ph") {
    if (value === 1 || value === 2 || value === 3 || value === 4) {
      if (en === "en") {
        return "normal";
      } else if (ko !== "") {
        return "적절";
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 5) {
      if (en === "en") {
        return "warning";
      } else if (ko !== "") {
        return "주의";
      } else if (color !== "") {
        return "#ffcc00";
      }
    }
  } else if (type === "ketone") {
    if (value === 1) {
      if (en === "en") {
        return "normal";
      } else if (ko !== "") {
        return "적절";
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 2 || value === 3) {
      if (en !== "") {
        return "normal";
      } else if (ko !== "") {
        return "적절";
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 4) {
      if (en !== "") {
        return "good";
      } else if (ko !== "") {
        return "좋음";
      } else if (color !== "") {
        return "#41d8e6";
      }
    } else if (value === 5) {
      if (en !== "") {
        return "warning";
      } else if (ko !== "") {
        return "주의";
      } else if (color !== "") {
        return "#ffcc00";
      }
    }
  }
};

export const historyResult = (value, en, ko, color) => {
  if (value === 1 || value === 2) {
    if (en === "en") {
      return "normal";
    } else if (ko !== "") {
      return "적절";
    } else if (color !== "") {
      return "#00bb00";
    }
  } else if (value === 3) {
    if (en !== "") {
      return "warning";
    } else if (ko !== "") {
      return "주의";
    } else if (color !== "") {
      return "#ffcc00";
    }
  } else if (value === 4) {
    if (en !== "") {
      return "caution";
    } else if (ko !== "") {
      return "경고";
    } else if (color !== "") {
      return "#ff6600";
    }
  } else if (value === 5 || value === 6) {
    if (en !== "") {
      return "danger";
    } else if (ko !== "") {
      return "위험";
    } else if (color !== "") {
      return "#646464";
    }
  }
};

export const ketoneResult = (value, en, ko, color) => {
  if (value === 1) {
    if (en === "en") {
      return "normal";
    } else if (ko !== "") {
      return "적절(-)";
    } else if (color !== "") {
      return "#00bb00";
    }
  } else if (value === 2 || value === 3) {
    if (en !== "") {
      return "normal";
    } else if (ko !== "") {
      return "좋음(+)";
    } else if (color !== "") {
      return "#00bb00";
    }
  } else if (value === 4) {
    if (en !== "") {
      return "good";
    } else if (ko !== "") {
      return "좋음";
    } else if (color !== "") {
      return "#41d8e6";
    }
  } else if (value === 5) {
    if (en !== "") {
      return "warning";
    } else if (ko !== "") {
      return "주의";
    } else if (color !== "") {
      return "#ffcc00";
    }
  }
};

export const computedYtics = (type) => {
  if (type === "blood")
    return {
      min: 1,
      max: 4,
      tick: {
        format: function(x) {
          if (x === 1) return "적절";
          else if (x === 2) return "주의";
          else if (x === 3) return "경고";
          else if (x === 4) return "위험";
          else if (x === 5) return "";
          else return "";
        },
      },
    };
  else if (type === "glucose" || type === "protein")
    return {
      min: 1,
      max: 6,
      tick: {
        format: function(x) {
          if (x === 1) return "";
          else if (x === 2) return "적절";
          else if (x === 3) return "주의";
          else if (x === 4) return "경고";
          else if (x === 5) return "위험";
          else if (x === 6) return "";
          else return "";
        },
      },
    };
  else if (type === "ph")
    return {
      min: 1,
      max: 5,
      tick: {
        format: function(x) {
          if (x === 4) return "적절";
          else if (x === 5) return "주의";
          else return "";
        },
      },
    };
  else if (type === "ketone")
    return {
      min: 1,
      max: 5,
      tick: {
        format: function(x) {
          if (x === 1) return "";
          else if (x === 2) return "";
          else if (x === 3) return "적절";
          else if (x === 4) return "좋음";
          else if (x === 5) return "주의";
          else return "";
        },
      },
    };
};

export const computeYgrid = (type) => {
  if (type === "blood")
    return {
      lines: [
        {
          value: 0,
          class: "grid",
        },
        {
          value: 1,
          class: "grid1",
        },
        {
          value: 2,
          class: "grid2",
        },
        {
          value: 3,
          class: "grid3",
        },
        {
          value: 4,
          class: "grid4",
        },
      ],
    };
  if (type === "glucose" || type === "protein")
    return {
      lines: [
        {
          value: 0,
          class: "grid",
        },
        {
          value: 1,
          class: "grid1",
        },
        {
          value: 2,
          class: "grid1",
        },
        {
          value: 3,
          class: "grid2",
        },
        {
          value: 4,
          class: "grid3",
        },
        {
          value: 5,
          class: "grid4",
        },
        {
          value: 6,
          class: "grid4",
        },
      ],
    };
  if (type === "ph")
    return {
      lines: [
        {
          value: 0,
          class: "grid",
        },
        {
          value: 1,
          class: "grid1",
        },
        {
          value: 2,
          class: "grid1",
        },
        {
          value: 3,
          class: "grid1",
        },
        {
          value: 4,
          class: "grid1",
        },
        {
          value: 5,
          class: "grid2",
        },
      ],
    };
  if (type === "ketone")
    return {
      lines: [
        {
          value: 0,
          class: "grid",
        },
        {
          value: 1,
          class: "grid1",
        },
        {
          value: 2,
          class: "grid1",
        },
        {
          value: 3,
          class: "grid1",
        },
        {
          value: 4,
          class: "grid5",
        },
        {
          value: 5,
          class: "grid2",
        },
      ],
    };
};

export const normalStripImg = (value) => {
  switch (value) {
    case "blood":
      return {
        color1: require("@/assets/images/exam-colors/blood_1.png"),
        color2: "",
      };
    case "glucose":
      return {
        color1: require("@/assets/images/exam-colors/glucose_1.png"),
        color2: require("@/assets/images/exam-colors/glucose_2.png"),
      };
    case "protein":
      return {
        color1: require("@/assets/images/exam-colors/protein_1.png"),
        color2: require("@/assets/images/exam-colors/protein_2.png"),
      };
    case "ph":
      return {
        color1: require("@/assets/images/exam-colors/ph_ph5.png"),
        color2: require("@/assets/images/exam-colors/ph_ph6.png"),
        color3: require("@/assets/images/exam-colors/ph_ph7.png"),
        color4: require("@/assets/images/exam-colors/ph_ph8.png"),
      };
    case "ketone":
      return {
        color1: require("@/assets/images/exam-colors/ketone_normal_neg.png"),
        color2: "",
      };

    default:
      break;
  }
};

export const warningStripImg = (value) => {
  switch (value) {
    case "blood":
      return {
        color1: require("@/assets/images/exam-colors/blood_2.png"),
        color2: "",
      };
    case "glucose":
      return {
        color1: require("@/assets/images/exam-colors/glucose_3.png"),
        color2: "",
      };
    case "protein":
      return {
        color1: require("@/assets/images/exam-colors/protein_3.png"),
        color2: "",
      };
    case "ph":
      return {
        color1: require("@/assets/images/exam-colors/ph_ph9.png"),
        color2: "",
      };
    case "ketone":
      return {
        color1: require("@/assets/images/exam-colors/ketone_normal_25.png"),
        color2: require("@/assets/images/exam-colors/ketone_normal_50.png"),
      };
    default:
      break;
  }
};

export const cautionStripImg = (value) => {
  switch (value) {
    case "blood":
      return {
        color1: require("@/assets/images/exam-colors/blood_3.png"),
        color2: "",
      };
    case "glucose":
      return {
        color1: require("@/assets/images/exam-colors/glucose_4.png"),
        color2: "",
      };
    case "protein":
      return {
        color1: require("@/assets/images/exam-colors/protein_4.png"),
        color2: "",
      };
    case "ph":
      return {
        color1: require("@/assets/images/exam-colors/ph_ph9.png"),
        color2: "",
      };
    case "ketone":
      return {
        color1: require("@/assets/images/exam-colors/ketone_good_75.png"),
        color2: "",
      };
    default:
      break;
  }
};

export const dangerStripImg = (value) => {
  switch (value) {
    case "blood":
      return {
        color1: require("@/assets/images/exam-colors/blood_4.png"),
        color2: "",
      };
    case "glucose":
      return {
        color1: require("@/assets/images/exam-colors/glucose_5.png"),
        color2: require("@/assets/images/exam-colors/glucose_6.png"),
      };
    case "protein":
      return {
        color1: require("@/assets/images/exam-colors/protein_5.png"),
        color2: require("@/assets/images/exam-colors/protein_6.png"),
      };

    case "ketone":
      return {
        color1: require("@/assets/images/exam-colors/ketone_warning_100.png"),
        color2: "",
      };
    default:
      break;
  }
};
