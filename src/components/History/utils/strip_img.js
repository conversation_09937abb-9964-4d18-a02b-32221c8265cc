/**
 * 조건
 *
 * 1. 항목에 따라 레벨링이 다름
 * 2. 레벨에 따라 색깔이 다름
 * 3. 한국어로 된 문자열로 변환 되어야 하는 경우
 * 5. 영어로 된 문자열로 반환해야하는 경우
 *
 *
 * 함수 1 항목에 따른 한국어, 영어, 색깔 반환하는 함수
 * 함수 2 level에 따른  반환하는 함수
 * 함수 2 tick수 갯수 반환하는 함수
 * 함수 3 차트 grid의 value, class를 반환해야하는 함수
 */

function getCymValue(value) {
  if (0 <= value && 25 > value) {
    return getLevelValue("경고");
  } else if (25 <= value && 50 > value) {
    return getLevelValue("주의");
  } else if (50 <= value && 75 > value) {
    return getLevelValue("적절");
  } else if (value >= 75) {
    return getLevelValue("좋음");
  }
}

function getLevelValue(level) {
  switch (level) {
    case "좋음":
      return {
        en: "good",
        ko: "좋음",
        color: "#41d8e6",
      };
    case "적절":
      return {
        en: "normal",
        ko: "적절",
        color: "#00bb00",
      };
    case "주의":
      return {
        en: "warning",
        ko: "주의",
        color: "#ffcc00",
      };
    case "경고":
      return {
        en: "caution",
        ko: "경고",
        color: "#ff6600",
      };
    case "위험":
      return {
        en: "danger",
        ko: "위험",
        color: "#646464",
      };
    case "분발":
      return {
        en: "exertion",
        ko: "적절(-)",
        color: "#00bb00",
      };
    default:
      break;
  }
}

function getBloodValue(value) {
  switch (value) {
    case 1:
      return getLevelValue("적절");
    case 2:
      return getLevelValue("주의");
    case 3:
      return getLevelValue("경고");
    case 4:
      return getLevelValue("위험");
    default:
      break;
  }
}

function getProAndGluValue(value) {
  switch (value) {
    case 1:
      return getLevelValue("적절");
    case 2:
      return getLevelValue("적절");
    case 3:
      return getLevelValue("주의");
    case 4:
      return getLevelValue("경고");
    case 5:
      return getLevelValue("위험");
    case 6:
      return getLevelValue("위험");
    default:
      break;
  }
}

function getPhValue(value) {
  switch (value) {
    case 1:
      return {
        ph_ko: "(pH5)",
        ...getLevelValue("적절"),
      };
    case 2:
      return {
        ph_ko: "(pH6)",
        ...getLevelValue("적절"),
      };
    case 3:
      return {
        ph_ko: "(pH7)",
        ...getLevelValue("적절"),
      };
    case 4:
      return {
        ph_ko: "(pH8)",
        ...getLevelValue("적절"),
      };
    case 5:
      return {
        ph_ko: "(pH9)",
        ...getLevelValue("적절"),
      };
    default:
      break;
  }
}

function getKetoneValue(value) {
  switch (value) {
    case 1:
      return {
        ketone_ko: "(-)",
        ...getLevelValue("분발"),
      };
    case 2:
      return {
        ketone_ko: "(+)",
        ...getLevelValue("적절"),
      };
    case 3:
      return {
        ketone_ko: "(+)",
        ...getLevelValue("적절"),
      };
    case 4:
      return getLevelValue("좋음");
    case 5:
      return getLevelValue("주의");
    default:
      break;
  }
}

/**
 *
 * blood 1, 2, 3, 4 (4)
 * glucose, protein 2, 3, 4, 5 (6)
 * ph 4, 5 (5)
 * ketone 3, 4, 5 (5)
 *
 */

function computeYticks(type) {
  switch (type) {
    case "blood":
      return {
        min: 1,
        max: 4,
        tick: {
          format: function(x) {
            if (x === 1) return "적절";
            else if (x === 2) return "주의";
            else if (x === 3) return "경고";
            else if (x === 4) return "위험";
            else return "";
          },
        },
      };
    case "protein":
      return {
        min: 1,
        max: 6,
        tick: {
          format: function(x) {
            if (x === 1) return "";
            else if (x === 2) return "적절";
            else if (x === 3) return "주의";
            else if (x === 4) return "경고";
            else if (x === 5) return "위험";
            else if (x === 6) return "";
            else return "";
          },
        },
      };
    case "glucose":
      return {
        min: 1,
        max: 6,
        tick: {
          format: function(x) {
            if (x === 1) return "";
            else if (x === 2) return "적절";
            else if (x === 3) return "주의";
            else if (x === 4) return "경고";
            else if (x === 5) return "위험";
            else if (x === 6) return "";
            else return "";
          },
        },
      };
    case "ph":
      return {
        min: 1,
        max: 5,
        tick: {
          format: function(x) {
            if (x === 4) return "적절";
            else if (x === 5) return "주의";
            else return "";
          },
        },
      };
    case "ketone":
      return {
        min: 1,
        max: 5,
        tick: {
          format: function(x) {
            if (x === 1) return "";
            else if (x === 2) return "";
            else if (x === 3) return "적절";
            else if (x === 4) return "좋음";
            else if (x === 5) return "주의";
            else return "";
          },
        },
      };
    default:
      break;
  }
}
function computeYgrid(type, amount) {
  let lines = [];
  for (let i = 0; i < amount + 1; i++) {
    lines.push({ value: i, class: `${type}-grid${i}` });
  }
  return lines;
}
// console.log(computeYgrid("protein", 6));
