<template>
  <div>
    <ConfirmModal
      v-show="showConfirmModal"
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-show="showErrorModal" :error="error" @isClicked="isClicked" />
    <div class="profile_box">
      <div class="profile-img">
        <img :src="profile_img" class="profile-img-tg" @error="replaceImage" />
        <div class="profile-uploadBtn-wrapper">
          <label for="upload-btn">
            <v-icon v-if="!clicked">$exam_off</v-icon>
            <v-icon v-else>$exam_on</v-icon>
          </label>
          <input
            id="upload-btn"
            type="file"
            accept="image/png, image/jpeg, image/jpg"
            @change="imgFileHandler"
          />
          <!-- @click="clickHandler" -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import ConfirmModal from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { cym702 } from "@/api";

export default {
  components: { ConfirmModal, ErrorModal },
  props: {
    profileImg: String,
  },
  data() {
    return {
      profile_img: "",
      clicked: false,
      showConfirmModal: false,
      content: this.$i18n.t("image_success_modal"),
      showErrorModal: false,
      error: this.$i18n.t("image_error_modal"),
    };
  },
  mounted() {
    this.profile_img =
      this.profileImg || require("@/assets/images/mypage-icon/profile.png");
  },
  watch: {
    profileImg: function(newVal) {
      this.profile_img = newVal;
    },
  },
  methods: {
    isConfirmed() {
      this.showConfirmModal = false;
    },
    isClicked() {
      this.showErrorModal = false;
    },
    // clickHandler() {
    //   /*global Webview*/
    //   /*eslint no-undef: "error"*/
    //   console.log("image upload clicked");
    //   this.clicked = true;
    //   const message = {
    //     action: "allowUseGallery",
    //   };
    //   Webview.allowUseGallery(message);
    // },
    imgFileHandler(e) {
      this.profile_img = URL.createObjectURL(e.target.files[0]);
      this.image = e.target.files[0];
      // console.log(e.target.files[0]);
      this.fetchImageUpload(e.target.files[0]);
    },
    async fetchImageUpload(image) {
      try {
        const subjectId = Number(localStorage.getItem("subjectId"));
        const selectUser = Number(localStorage.getItem("selectUser"));
        let formData = new FormData();
        formData.append("image", image);
        // selectUser > 0 ? formData.append("subjectId", selectUser) : formData.append("subjectId", subjectId);
        // console.log(formData);
        const res = await cym702.patch(
          `/subjects/${subjectId}/image`,
          formData,
          {
            headers: {
              "content-type": "multipart/form-data",
            },
          }
        );
        if (res.status === 200) {
          this.showConfirmModal = true;
        }
        this.loading = false;
      } catch (error) {
        this.showErrorModal = true;
        this.loading = false;
        console.log(error);
      }
    },
    replaceImage(e) {
      e.target.src = require("@/assets/images/mypage-icon/profile.png");
    },
  },
};
</script>

<style lang="scss" scoped>
.profile_box {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 45px 0px;
}

.profile-img {
  width: 130px;
  height: 130px;
  border-radius: 50%;
  background-color: #f8f8f8;
  position: relative;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #41d8e6;
  }
}

.profile-uploadBtn-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ededed;
  line-height: 40px;
  left: 90px;
  position: absolute;
  top: 90px;
}

input[type="file"] {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
</style>
