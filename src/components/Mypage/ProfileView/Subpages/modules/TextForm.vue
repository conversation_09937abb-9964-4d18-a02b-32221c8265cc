<template>
  <div>
    <div class="text-field__wrapper">
      <div class="email--box">
        <span>{{ placeholder }}</span>
        <div class="box-label">{{ label }}</div>
      </div>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    placeholder: String,
    label: String,
  },
};
</script>

<style lang="scss" scoped>
.text-field__wrapper {
  padding: 0px 30px;
}

.email--box {
  position: relative;
  width: 100%;
  border-radius: 10px;
  border: 1px solid $date-color;
  height: 57px;
  line-height: 60px;
  text-align: left;
  padding: 0 10px;
  font-size: 16px;
}

.box-label {
  position: absolute;
  top: -17px;
  font-size: 12px;
  color: #a7a7a7;
}

.main-large-btn {
  width: 100%;
  height: 50px !important;
  border-radius: 10px;
  color: #646464 !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  letter-spacing: -0.03em;
}

.btn__wrapper {
  padding-top: 15px;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #c9f4f8 !important;
}
</style>
