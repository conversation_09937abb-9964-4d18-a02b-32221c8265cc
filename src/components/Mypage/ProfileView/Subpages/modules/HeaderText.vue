<template>
  <div>
    <div class="form--wrapper dp-30">
      <div class="profile-explain-txt" v-html="txt">
        {{ txt }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    txt: String,
  },
};
</script>

<style lang="scss" scoped>
@mixin profileExplain() {
  // font-family: Noto Sans KR;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  text-align: justify;
  letter-spacing: -0.03em;
  color: #858585;
}

.profile-explain-txt {
  @include profileExplain();
  width: 100%;
  padding: 50px 0px 30px 0px;
}

.form--wrapper {
  width: 100%;
  margin: auto;
}
</style>
