<template>
  <div>
    <HeaderText :txt="txt" />
    <text-form :placeholder="placeholder" :label="label"> </text-form>
    <div class="text-field__wrapper">
      <v-text-field
        v-model="password"
        color="#41D8E6"
        class="default-radius main-btn"
        :append-icon="showPassword ? '$eye_show' : '$eye_off'"
        :type="showPassword ? 'text' : 'password'"
        label="비밀번호"
        height="50"
        clearable
        clear-icon="$x_circle"
        outlined
        required
        :rules="passwordRules"
        :error-messages="passwordMsg"
        @click:append="showPassword = !showPassword"
        @blur="focusOut"
        @focus="focusIn"
        @keydown.enter.prevent="submit"
        @keydown.space.prevent
      ></v-text-field>
    </div>
  </div>
</template>

<script>
import HeaderText from "./modules/HeaderText.vue";
import TextForm from "./modules/TextForm.vue";

export default {
  components: {
    HeaderText,
    TextForm,
  },
  data() {
    return {
      txt: "회원님의 소중한 정보 보호를 위해, 심702 계정의 현재 비밀번호를 확인해 주세요",
      label: "이메일 주소",
      placeholder: "",
      password: ""
    };
  },
  mounted() {
    // console.log(this.$route.params);
    this.placeholder = this.$route.params.email
  },
};
</script>

<style lang="scss" scoped>
.text-field__wrapper {
  padding: 25px 30px 10px 30px;
}
</style>