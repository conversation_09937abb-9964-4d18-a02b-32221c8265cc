<template>
  <div>
    <ErrorModal v-show="showErrorModal" :error="error" @isClicked="isClicked" />
    <CompleteAlert :content="content" v-show="showCompleteAlert" />
    <ConfirmAlert
      :content="sendMessage"
      :btnText="this.$i18n.t('confirm_btn')"
      v-show="showSendCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <div class="pt-30 dp-30">
      <!-- <div> -->
      <div class="country__wrapper">
        <div class="dropdown__wrapper">
          <vue-country-dropdown
            @onSelect="onSelect"
            :disabledFetchingCountry="false"
            :preferredCountries="['KR', 'US', 'GB']"
            :enabledFlags="true"
            :enabledCountryCode="true"
            :showNameInput="true"
          />
        </div>
        <!-- <v-select
            class="country"
            color="#41D8E6"
            v-model="selected"
            :items="country"
            :label="this.$i18n.t('country_code')"
          ></v-select> -->
      </div>
      <v-text-field
        v-model="phone"
        :label="this.$i18n.t('input_phone')"
        color="#41D8E6"
        type="tel"
        maxlength="11"
        :rules="mobileNumberRule"
        :error-messages="findErrorMsg"
        @keydown.space.prevent
        @focus="focusInputHandler"
        @blur="blurInputHandler"
        required
      >
      </v-text-field>
      <!-- </div> -->
      <slot></slot>
      <template v-if="showTextField">
        <v-text-field
          v-model="authNumber"
          :label="this.$i18n.t('verification_number')"
          color="#41D8E6"
          type="tel"
          :error-messages="authErrorMsg"
          required
        >
          <template v-slot:append>
            <div class="v-timer">{{ minutes }}:{{ seconds }}</div>
            <div class="v-request-btn" @click="reSendSms">
              <button>{{ $t("request_btn") }}</button>
            </div>
          </template>
        </v-text-field>
      </template>
      <v-btn
        class="main-large-btn"
        elevation="0"
        color="#41D8E6"
        type="submit"
        @click="nextPhaseHandler"
        :disabled="!isPhoneNumberValid"
      >
        {{ buttonText }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import API from "@/api/auth/index.js";
import { updateUserPhone } from "@/api/user/index.js";
import CompleteAlert from "@/components/Common/Modal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import VueCountryDropdown from "vue-country-dropdown";
import ConfirmAlert from "@/components/Common/ConfirmModal.vue";

export default {
  components: {
    CompleteAlert,
    ErrorModal,
    VueCountryDropdown,
    ConfirmAlert,
  },
  data() {
    return {
      phone: "",
      authNumber: "",
      timer: null,
      totalTime: 3 * 60,
      showTextField: false,
      mobileNumberRule: [(v) => /^[0-9]/g.test(v) || this.$i18n.t("phone_error")],
      findErrorMsg: "",
      authErrorMsg: "",
      error: this.$i18n.t("auth_error_modal"),
      showErrorModal: false,
      content: this.$i18n.t("success_message_phone"),
      showCompleteAlert: false,
      showSendCompleteAlert: false,
      selected: "",
      sendMessage: this.$i18n.t("send_text_modal_msg"),
      deviceId: "",
      isListenerRegistered: false, // 리스너 등록 상태 추적
    };
  },
  computed: {
    buttonText() {
      if (this.showTextField) {
        return this.$i18n.t("confirm_btn");
      } else {
        return this.$i18n.t("send_btn");
      }
    },
    isPhoneNumberValid() {
      return (
        this.phone.length > 5 &&
        this.phone.length < 16 &&
        !this.selected.includes(this.$i18n.t("country_code"))
      );
    },
    minutes() {
      const minutes = Math.floor(this.totalTime / 60);
      return this.padTime(minutes);
    },
    seconds() {
      const seconds = this.totalTime - this.minutes * 60;
      return this.padTime(seconds);
    },
  },
  methods: {
    isConfirmed() {
      this.showSendCompleteAlert = false;
    },
    onSelect({ name, iso2, dialCode }) {
      // console.log(name, iso2, dialCode);
      this.selected = `+${dialCode}`;
    },
    nextPhaseHandler() {
      if (!this.showTextField) {
        this.sendSms();
        this.showTextField = true;
      } else {
        this.checkAuth();
      }
    },
    async sendSms() {
      try {
        const deviceId = this.getDeviceId();
        const phoneNumber = {
          countryNumber: this.selected,
          phone: this.phone,
          device: deviceId,
        };
        const response = await API.fetchCheckPhone(phoneNumber);

        if (response.status === 200) {
          this.showTextField = true;
          this.showSendCompleteAlert = true;
          this.startTimer();
          this.totalTime = 3 * 60;
        }
      } catch (error) {
        console.error(error);
        this.showErrorModal = true;
        if (error?.response?.data?.response_code === 403) {
          this.showJoinAlert = true;
        }
      }
    },
    async checkAuth() {
      try {
        const authNumbers = {
          countryNumber: this.selected,
          phone: this.phone,
          code: this.authNumber,
        };
        const response = await API.fetchCheckAuth(authNumbers);
        // this.$emit("nextPhaseHandler", 1);
        if (response.status === 200) {
          this.showCompleteAlert = true;
        }
      } catch (error) {
        console.error(error);
        this.showErrorModal = true;
        console.log(error?.response?.data);
        this.authErrorMsg = error?.response?.data?.internal;
      }
    },
    reSendSms() {
      this.totalTime = 3 * 60;
    },
    countdown() {
      if (this.totalTime >= 1) {
        this.totalTime--;
      } else {
        this.totalTime = 0;
        this.resetTimer;
        this.alertMessage = this.$i18n.t("error_timeout");
      }
    },
    startTimer() {
      clearInterval(this.timer);
      this.timer = setInterval(() => this.countdown(), 1000);
    },
    padTime(time) {
      return (time < 10 ? "0" : "") + time;
    },
    focusInputHandler() {
      // console.log("focus");
    },
    blurInputHandler() {
      // console.log("blur");
    },
    errorHandler() {
      this.showErrorModal = false;
    },
    isClicked(boolean) {
      this.showErrorModal = false;
    },
    getDeviceId() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const deviceId = Webview.getDeviceId();

      this.deviceId = deviceId;

      this.$nextTick(() => this.listener());
    },
    listener() {
      this.isIos
        ? window.addEventListener("message", this.handleMessage)
        : document.addEventListener("message", this.handleMessage);
    },
    handleMessage(e) {
      const data = JSON.parse(e.data);
      this.deviceId = data.payload.result;
    },
  },
  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
    document.removeEventListener("message", this.handleMessage);
  },
  mounted() {
    this.getDeviceId();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 20px;
}

::v-deep .v-text-field .v-label {
  top: -10px !important;
  font-size: 14px;
  color: #41d8e6;
  letter-spacing: -0.03em !important;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    letter-spacing: -0.03em !important;
    line-height: 15px !important;
  }
}
::v-deep .v-input input {
  font-size: 20px !important;
  line-height: 23px;
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

.main-large-btn {
  margin-top: 3px;
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

.v-timer {
  font-size: 18px;
  letter-spacing: -0.03em;
  color: #000 !important;
}
.v-request-btn {
  font-size: 16px;
  line-height: 20px;
  text-indent: 3px;

  button {
    color: #41d8e6;
    font-weight: 700;
    letter-spacing: -0.03em;
  }
}

.country__wrapper {
  width: 100%;
  // display: flex;
  color: #41d8e6 !important;
}

.dropdown__wrapper {
  margin-bottom: 20px;
}

::v-deep .v-text-field input {
  padding: 8px 0 4px;
  line-height: 23px;
}

::v-deep .v-text-field .v-label {
  top: -15px !important;
  color: #a7a7a7;
  font-size: 14px;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

::v-deep .vue-country-select .dropdown-list {
  max-height: 50vh !important;
  width: 100%;
  border: 1px solid #41d8e6;
  border-radius: 0 0 5px 5px;
  font-size: 16px;
  color: #000000;
  left: 0;
}

::v-deep .vue-country-select {
  border: none;
  border-bottom: 1px solid #a7a7a7 !important;
  border-radius: 0px !important;
  display: flex;
}

::v-deep .vue-country-select .current {
  font-size: 16px;
}

::v-deep .vue-country-select .dropdown {
  width: 100%;
  padding: 10px 0;
}

::v-deep .vue-country-select:focus-within {
  box-shadow: none !important;
  border-color: #41d8e6;
}

::v-deep .vue-country-select .dropdown:hover {
  background-color: transparent;
  border: none !important;
}

::v-deep .vue-country-select .dropdown-item {
  padding: 5px 3px;
}

::v-deep .vue-country-select .dropdown-item.highlighted {
  background-color: #c9f4f8;
}

::v-deep .theme--light.v-text-field > .v-input__control > .v-input__slot:before {
  border-color: #a7a7a7 !important;
}
</style>
