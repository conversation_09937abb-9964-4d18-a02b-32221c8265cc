<template>
  <div>
    <div class="profile-items">
      <!-- =================================================================== -->
      <!-- 🚨 Profile name -->
      <router-link to="/profile/name">
        <div class="menu-item">
          <div class="menu-right">{{ $t("profile_name") }}</div>
          <div class="menu-left">
            <span class="user-info">{{ username }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </router-link>

      <!-- =================================================================== -->
      <!-- 🚨 Profile email  -->
      <!-- <router-link to="/profile/email">
        <div class="menu-item">
          <div class="menu-right">{{ $t("profile_email") }}</div>

          <div class="menu-left">
            <span class="user-info">{{ usermail }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </router-link> -->

      <!-- =================================================================== -->
      <!-- 🚨 Profile phone -->
      <!-- <router-link to="/profile/phone">
        <div class="menu-item">
          <div class="menu-right">{{ $t("profile_phone") }}</div>

          <div class="menu-left">
            <span class="user-info-phone">{{ userphone }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </router-link> -->

      <!-- =================================================================== -->
      <!-- 🚨 Profile password  -->
      <!-- <router-link :to="{ name: 'PasswordEdit', params: { email: this.usermail } }"> -->
      <!-- <div class="menu-item" v-if="!isSns"> -->
      <router-link to="/profile/edit">
        <div class="menu-item">
          <div class="menu-right">{{ $t("profile_info") }}</div>
          <div class="menu-left">
            <span class="user-info">{{ $t("edit") }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </router-link>

      <!-- =================================================================== -->
      <!-- 🚨 Profile survey  -->
      <router-link to="/profile/survey">
        <div class="menu-item">
          <div class="menu-right">{{ $t("profile_survey") }}</div>
          <div class="menu-left">
            <span class="user-info">{{ $t("edit") }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </router-link>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    username: String,
    usermail: String,
    userphone: String,
    isSns: Boolean,
  },
  data() {
    return {};
  },

  mounted() {},
};
</script>

<style lang="scss" scoped>
.profile-items {
  margin: auto;
  margin: 0px 10px 0px 30px;
  a {
    text-decoration: none;
    color: #000;
  }
}
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.menu-right {
  font-weight: 500;
  font-size: 18px;
}

.user-info {
  font-family: GilroyMedium;
  font-weight: 500;
  font-size: 20px;
  margin-right: 5px;
  color: #41d8e6;
}

.user-info-phone {
  font-family: GilroyMedium;
  font-size: 20px;
  font-weight: 500;
  margin-right: 5px;
  color: #41d8e6;
}

.icon {
  margin-top: 10px;
}
</style>
