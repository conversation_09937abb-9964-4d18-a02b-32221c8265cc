<template>
  <div>
    <div class="profile-items">
      <router-link to="/profile/name">
        <div class="menu-item">
          <div class="menu-right">{{ $t("profile_name") }}</div>
          <div class="menu-left">
            <span class="user-info">{{ username }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </router-link>

      <router-link to="/profile/edit/sub">
        <div class="menu-item">
          <div class="menu-right">{{ $t("profile_info") }}</div>
          <div class="menu-left">
            <span class="user-info">{{ $t("edit") }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </router-link>

      <!-- <div @click="genderClicked">
        <div class="menu-item">
          <div class="menu-right">{{ $t("profile_sex") }}</div>
          <div class="menu-left">
            <span class="user-info-phone">{{ gender }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </div>

      <div @click="birthClicked">
        <div class="menu-item">
          <div class="menu-right">{{ $t("birth") }}</div>
          <div class="menu-left">
            <span class="user-info">{{ birth }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </div>

      <div @click="typeClicked">
        <div class="menu-item">
          <div class="menu-right">{{ $t("relationships") }}</div>
          <div class="menu-left">
            <span class="user-info">{{ type }}</span>
            <v-icon class="icon">$right_arrow</v-icon>
          </div>
        </div>
      </div> -->
    </div>
    <div class="delete-user__wrapper">
      <div class="delete-user" @click="deleteSubUser">{{ $t("delete_sub_user") }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    username: String,
    gender: String,
    birth: String,
    type: String,
  },
  data() {
    return {
      nameIsClicked: false,
      genderIsClicked: false,
      birthIsClicked: false,
      typeIsClicked: false,
      deleteIsClicked: false,
    };
  },
  methods: {
    nameClicked() {
      this.nameIsClicked = true;
      this.$emit("nameClicked", this.nameIsClicked);
    },
    genderClicked() {
      this.genderIsClicked = true;
      this.$emit("genderClicked", this.genderIsClicked);
    },
    birthClicked() {
      this.birthIsClicked = true;
      this.$emit("birthClicked", this.birthIsClicked);
    },
    typeClicked() {
      this.typeIsClicked = true;
      this.$emit("typeClicked", this.typeIsClicked);
    },
    deleteSubUser() {
      this.deleteIsClicked = true;
      this.$emit("deleteSubUser", this.deleteIsClicked);
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.profile-items {
  margin: auto;
  margin: 0px 10px 0px 30px;
  a {
    text-decoration: none;
    color: #000;
  }
}
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.menu-right {
  font-weight: 500;
  font-size: 18px;
}

.user-info {
  font-family: GilroyMedium;
  font-weight: 500;
  font-size: 18px;
  margin-right: 5px;
  color: #41d8e6;
}

.user-info-phone {
  font-family: GilroyMedium;
  font-size: 18px;
  font-weight: 500;
  margin-right: 5px;
  color: #41d8e6;
}

.icon {
  margin-top: 10px;
}

.delete-user__wrapper {
  width: 100%;
  position: absolute;
  bottom: 100px;
  display: flex;
}

.delete-user {
  color: #a7a7a7;
  font-size: 14px;
  border-bottom: 0.5px solid #a7a7a7;
  width: 70px;
  margin: 0 auto;
}
</style>
