<template>
  <div>
    <div class="form-subtitle__wrapper pt-30 dp-30" v-html="subtitle[currentStateIdx]"></div>
  </div>
</template>

<script>
export default {
  props: {
    currentStateIdx: Number,
  },
  data() {
    return {
      subtitle: [
        this.$i18n.t("profile_name_txt"),
        this.$i18n.t("profile_authorization_txt"),
        this.$i18n.t("profile_pwd_txt"),
        this.$i18n.t("profile_newpwd_txt"),
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.form-subtitle__wrapper {
  width: 100%;
  font-weight: 400;
  font-size: 18px;
  text-align: left;
  letter-spacing: -0.03em;
  color: #646464;
}
</style>
