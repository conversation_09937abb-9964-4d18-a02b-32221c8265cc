<template>
  <div>
    <div class="guide-back">
      <v-carousel
        v-model="carouselIndex"
        :cycle="false"
        height="100%"
        class="control"
        :hide-delimiters="hideDelimiters ? hideDelimiters : false"
        :touchless="touchless ? touchless : false"
        hide-delimiter-background
        :show-arrows="false"
        :continuous="false"
        @change="checkCarouselIndex"
      >
        <slot></slot>
      </v-carousel>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    hideDelimiters: Boolean,
    touchless: Boolean,
  },
  data: () => ({
    carouselIndex: 0,
  }),
  methods: {
    checkCarouselIndex(carouselIndex) {
      this.$emit("checkCarouselIndex", carouselIndex);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .control {
  overscroll-behavior: contain !important;
  .v-btn {
    width: 8px !important;
    height: 8px !important;
    color: transparent !important;
    background-color: #ffffff;
  }

  .v-btn--active {
    background-color: #41d8e6;
  }
}

.close-layout {
  position: fixed;
  top: 66px;
  left: 30px;
  z-index: 999 !important;
}

::v-deep .v-carousel__controls {
  position: absolute;
  top: 36px !important;
}

.guide-close-btn-area {
  width: 100%;
  display: flex;
  padding: 20px 0px 0px 10px;
}

.guide_first_img {
  padding: 0 15px;
  margin-top: 90px;
}

.guide_first_img > img {
  width: 100%;
  border-radius: 10px;
}

.carousel-item-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.guide_second_img {
  padding: 0 15px;
  margin-top: 50px;
  img {
    width: 100%;
    border-radius: 10px;
  }
}

.guide_third_img {
  display: flex;
  margin-top: 46px;
}

.guide_gif {
  width: 86px;
  margin-top: 76px;
  img {
    object-fit: contain;
    width: 100%;
  }
}
.guide_care_icon {
  height: 76px;
  width: auto;
  margin: 0px 13.5px;
  img {
    object-fit: contain;
    height: 100%;
  }
}

.guide-first-text {
  margin-top: 35px;
  width: 100%;
  text-align: center;
}

.guide-second-text {
  width: 100%;
  text-align: center;
  z-index: 1;
  margin-top: calc(100% / 2);
}

.carousel-box {
  position: relative;
  margin-top: 130px;
  width: 50px;
  z-index: 999;
}

.guide-text-title {
  // font-family: Noto Sans KR;
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 23px;
  color: #41d8e6;
}

.guide-text-content {
  width: 239px;
  // font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #ffffff;
}
</style>
