<template>
  <div>
    <div class="bg-modal">
      <div class="alert-window">
        <div class="alert-window__content" v-html="content"></div>
        <div class="createdAt">{{ createdAt }}</div>
        <div class="alert-window__btn" @click="nextHandler">{{ btnText }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    content: String,
    createdAt: String,
    btnText: String,
  },
  methods: {
    nextHandler() {
      this.$emit("isConfirmed", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 15px;
  text-align: center;
}

.alert-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-window__content {
  font-size: 20px;
  padding-top: 15px;
  color: #323232;
}

.createdAt {
  color: #646464;
  font-size: 14px;
}
.alert-window__btn {
  width: 100%;
  padding: 0px 20px;
  padding-top: 10px;
  text-align: right;
  font-size: 16px;
  font-weight: 700;
  color: #41d8e6;
}
</style>
