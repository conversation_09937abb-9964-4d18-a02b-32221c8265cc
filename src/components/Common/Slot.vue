<template>
  <button :class="[active, 'tab']"></button>
</template>

<script>
export default {
  props: {
    id: Number,
    value: Number,
  },
  computed: {
    active() {
      return this.value === this.id ? "active" : false;
    },
  },
};
</script>

<style scoped>
.tab {
  width: 9px;
  height: 9px;
  background: #ffffff;
  border-radius: 100%;
  margin: 0 9px;
}
.active {
  background: #41d8e6;
  border-radius: 100%;
}
</style>
