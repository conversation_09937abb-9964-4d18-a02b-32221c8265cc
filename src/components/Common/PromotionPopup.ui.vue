<template>
  <div v-if="popupList.length > 0" class="background-modal">
    <div class="alert-window">
      <!-- count idx -->
      <div class="counter-idx">{{ popupCurrentIndex + 1 }} / {{ popupList.length }}</div>

      <!-- show content -->
      <div
        class="alert-content"
        :class="isKo ? 'content-isKo' : 'content-isEn'"
        @click="handlePopupClick"
      >
        <span class="content-title" v-html="currentTitle"></span>
        <span class="highlight-text" v-html="currentHighlightText"></span>

        <img
          v-if="currentImage"
          class="modal-image"
          :src="currentImage"
          alt="popup image"
          loading="lazy"
        />
      </div>

      <!-- button -->
      <div class="btn__wrapper">
        <span
          class="close-text"
          :class="isKo ? 'close-text__isKo' : 'close-text__isEn'"
          @click="handleHideTodayClick"
        >
          {{ $i18n.t("hide_today") }}
        </span>
        <span class="close-text" @click="handleCloseClick">
          {{ $i18n.t("hide_btn") }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: () => ({
    POPUP_ROTATE_TIME: 4300,
    isKo: false,
    popupCurrentIndex: 0,
    intervalId: null,
    popupList: [
      // {
      //   koTitle: "반려동물용 <br/>소변검사 10종 <br/>미국 런칭!",
      //   enTitle: "Pet Urine test <br/>10 Parameters<br/>U.S Launch!",
      //   koHighlightText: "펀딩 하러 가기",
      //   enHighLightText: "Go to Funding",
      //   imgName: "indiegogo",
      //   koUrlPath:
      //     "https://www.indiegogo.com/projects/cym702-pet-easy-at-home-pet-health-test-kits/reft/38410017/cym702petapp",
      //   enUrlPath:
      //     "https://www.indiegogo.com/projects/cym702-pet-easy-at-home-pet-health-test-kits/reft/38410017/cym702petapp",
      // },
    ],
  }),

  computed: {
    // 현재 선택된 팝업 데이터 가져오기
    currentPopup() {
      return this.popupList[this.popupCurrentIndex] || {};
    },

    // 현재 타이틀
    currentTitle() {
      return this.currentPopup[this.isKo ? "koTitle" : "enTitle"] || "";
    },

    // 현재 하이라이트 텍스트
    currentHighlightText() {
      return this.currentPopup[this.isKo ? "koHighlightText" : "enHighLightText"] || "";
    },

    // 현재 이미지 URL
    currentImage() {
      if (!this.popupList[this.popupCurrentIndex].imgName) return "";
      return this.getImageUrl(this.currentPopup.imgName);
    },
  },

  methods: {
    getImageUrl(imgName) {
      const lang = this.isKo ? "ko" : "en";

      return require(`@/assets/images/modals/${imgName}_${lang}.png`);
    },

    closeHandler() {
      this.$emit("handleModalCloseClick", true);
    },

    handleCloseClick() {
      this.closeHandler();
    },

    handleHideTodayClick() {
      const today = new Date();
      const [currentDate] = today.toISOString().split("T");

      localStorage.setItem("lastClosedDate", currentDate);

      this.$emit("handleModalCloseClick", true);
    },

    isMobile() {
      return /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
    },

    handlePopupClick() {
      const url = this.currentPopup[this.isKo ? "koUrlPath" : "enUrlPath"];

      // 만약 외부 링크로 이동하는 경우가 아닐 때 처리하는 분기처리
      if (!url.includes("https://")) {
        this.$emit("closeHandler", true);
        this.$router.push(url);
        return;
      }

      if (this.isMobile()) {
        // 모바일 환경 → Webview 사용
        if (window.Webview) {
          /*global Webview*/
          /*eslint no-undef: "error"*/
          Webview.openUrl(url);
        } else {
          // 일반 모바일 브라우저라면 새 창으로 열기
          window.location.href = url;
        }
      } else {
        // PC 환경 → 새 창(탭)으로 외부 링크 열기
        window.open(url, "_blank", "noopener,noreferrer");
      }
    },

    startPopupListRotate() {
      const { POPUP_ROTATE_TIME } = this;
      const LENGTH = this.popupList.length;

      this.intervalId = setInterval(() => {
        this.popupCurrentIndex = (this.popupCurrentIndex + 1) % LENGTH;
      }, POPUP_ROTATE_TIME);
    },
  },

  beforeDestroy() {
    clearInterval(this.intervalId);
  },

  mounted() {
    this.isKo = this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn");
    this.startPopupListRotate();
  },
};
</script>

<style lang="scss" scoped>
.background-modal {
  position: fixed;
  width: 100%;
  max-width: 450px;
  height: 100%;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: flex-end;
  padding: 0;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  height: 345px;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border-radius: 30px 30px 0 0;
}

.close-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 23.17px;
  letter-spacing: -0.05em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #646464;
}

.close-text__isKo {
  font-family: Noto Sans KR !important;
}

.close-text__isEn {
  font-family: GilroyMedium !important;
}

.alert-content {
  width: 100%;
  height: 245px;
  background: #c9f4f8;
  border-radius: 30px 30px 0 0;
  padding: 40px 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.content-isKo {
  font-family: Noto Sans KR !important;
}

.content-isEn {
  font-family: GilroyMedium !important;
}

.content-title {
  min-height: 68px;
  text-align: start;
  font-weight: 500;
  font-size: 20px;
  line-height: 25px;
  letter-spacing: -3%;
}

.highlight-text {
  color: #ff6600;
  font-weight: 500;
  font-size: 16px;
  line-height: 18.83px;
  letter-spacing: -3%;
  margin-top: 15px;
}

.btn__wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 12px 30px 0 25px;
}

.modal-image {
  width: 160px;
  height: 160px;
  position: relative;
  left: calc(
    100% - 30px - clamp(120px, 35%, 160px)
  ); // screen view px(max-w: 450px) - padding px - image px
  bottom: 70px;
}

.counter-idx {
  width: 40px;
  height: 20px;
  position: absolute;
  float: right;
  align-self: flex-end;
  margin-right: 30px;
  margin-top: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background: #646464a5;
  color: #ffffff;
  font-family: Noto Sans KR !important;
  font-size: 12px;
  font-weight: 500;
  line-height: 17.38px;
  letter-spacing: -0.02em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}
</style>
