<template>
  <!-- <transition name="left" mode="in-out"> -->
  <div class="carousel-slide" v-show="visibleSlide === index">
    <slot></slot>
  </div>
  <!-- </transition> -->
</template>

<script>
export default {
  props: ["visibleSlide", "index"],
  data() {
    return {};
  },
};
</script>

<style>
/* .carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
.left-enter-active {
  animation: leftInAnimation 0.8s ease;
}
.left-leave-active {
  animation: leftOutAnimation 0.8s ease;
}

@keyframes leftInAnimation {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
@keyframes leftOutAnimation {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
} */
</style>
