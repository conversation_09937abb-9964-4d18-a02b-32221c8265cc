<template>
  <div>
    <div class="bg-modal">
      <!-- <div class="close-icon__wrapper" @click="cancelHandler">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div> -->
      <div class="alert-window">
        <div class="alert-window__content" v-html="content"></div>
        <div class="btn__wrapper">
          <div class="left-btn" @click="cancelHandler">{{ leftBtnTxt }}</div>
          <div class="right-btn" @click="confirmHandler">{{ rightBtnTxt }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    content: String,
    leftBtnTxt: String,
    rightBtnTxt: String,
  },
  methods: {
    cancelHandler() {
      this.$emit("cancelHandler", true);
    },
    confirmHandler() {
      this.$emit("confirmHandler", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 65px;
  left: 30px;
  img {
    width: 26px;
  }
}

.alert-window {
  background-color: #fff;
  width: 100%;
  /* height: 180px; */
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 15px;
  text-align: center;
}

.alert-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-window__content {
  font-size: 20px;
  // padding-top: 15px;
  color: #323232;
}

.btn__wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.left-btn {
  width: 45%;
  height: 55px;
  background: #41d8e6;
  border-radius: 5px;
  margin: 20px 8px 0 8px;
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
  color: #fff;
}
.right-btn {
  width: 45%;
  height: 55px;
  background: #c8c8c8;
  border-radius: 5px;
  margin: 20px 8px 0 8px;
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
  color: #fff;
}
</style>
