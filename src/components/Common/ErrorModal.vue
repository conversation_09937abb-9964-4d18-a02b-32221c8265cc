<template>
  <div>
    <div class="bg-modal">
      <div class="alert-window">
        <div class="alert-window__content" v-html="error"></div>
        <div class="alert-window__btn" @click="nextHandler">
          {{ btnText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    error: String,
    type: String,
  },
  computed: {
    btnText() {
      return this.type !== "analysis"
        ? this.$t("confirm_btn")
        : this.$t("recapture_btn");
    },
  },
  methods: {
    nextHandler() {
      this.$emit("isClicked", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  /* height: 180px; */
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 15px;
}

.alert-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-window__content {
  font-size: 17px;
  padding-top: 15px;
  color: #323232;
}

.alert-window__btn {
  width: 100%;
  padding: 0px 20px;
  padding-top: 10px;
  text-align: right;
  font-size: 16px;
  font-weight: 700;
  color: #41d8e6;
}
</style>
