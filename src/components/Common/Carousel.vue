<template>
  <div class="carousel" @keydown="checkSlice($event)" tabindex="0">
    <slot></slot>
    <div class="next-btn__wrapper">
      <button class="next-btn" @click="next">{{ $t("next_btn") }}</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      index: 0,
      slides: [],
      slideDirection: "",
    };
  },
  // computed: {
  //   slidesLength() {
  //     return this.slides.length;
  //   },
  // },
  // mounted() {
  //   this.slides = this.$children;
  //   this.slides.map((slide, index) => {
  //     slide.index = index;
  //   });
  // },
  methods: {
    next() {
      this.$emit("next");
    },
  },
};
</script>

<style>
.carousel {
  position: relative;
  width: 100%;
}

.next-btn__wrapper {
  width: 100%;
  position: fixed;
  bottom: 5vh;
  left: 0;
  padding: 0px 30px;
}
.next-btn {
  height: 60px;
  position: relative;
  max-width: 640px;
  min-width: 320px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: -0.03em;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 22px;
  font-weight: bold;
  color: #ffffff;
  background: #41d8e6;
}
</style>
