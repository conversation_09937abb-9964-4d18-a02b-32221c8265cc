<template>
  <div>
    <div class="alert-tooltip-top" v-if="position === 'top'">준비 중입니다</div>
    <div class="alert-tooltip-bottom" v-if="position === 'bottom'">{{ $t("current_no_service") }}</div>
    <div class="alert-tooltip-right-bottom" v-if="position === 'right-bottom'">잘못된 입력입니다.</div>
  </div>
</template>

<script>
export default {
  props: {
    position: {
      type: String,
    },
  },

  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.alert-tooltip-bottom {
  width: 90px;
  text-align: center;
  color: #fff;
  background-color: #41d8e6;
  border-radius: 5px;
  // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  font-weight: 500;
  letter-spacing: -0.1em;
  position: absolute;
  top: -45px;
  left: -20px;
  transition: ease-in 0.4s;
}

.alert-tooltip-bottom:after {
  border-top: 8px solid #41d8e6;
  border-left: 2px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  top: 34px;
  left: 60px;
}

.alert-tooltip-top {
  left: -70px;
  top: 25px;
  width: 90px;
  text-align: center;
  color: #fff;
  background-color: #41d8e6;
  border-radius: 5px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  font-weight: 500;
  letter-spacing: -0.1em;
  position: absolute;

  transition: ease-in 0.4s;
}

.alert-tooltip-top:after {
  border-top: 0px solid transparent;
  border-left: 8px solid transparent;
  border-right: 2px solid transparent;
  border-bottom: 8px solid #41d8e6;
  content: "";
  position: absolute;
  top: -8px;
  left: 60px;
}

.alert-tooltip-right-bottom {
  width: 120px;
  text-align: center;
  color: #fff;
  background-color: #ff6600;
  border-radius: 5px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  font-weight: 500;
  letter-spacing: -0.1em;
  position: absolute;
  top: -45px;
  left: 40px;
  transition: ease-in 0.4s;
}

.alert-tooltip-right-bottom:after {
  border-top: 8px solid #ff6600;
  border-left: 2px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  top: 34px;
  left: 60px;
}
</style>
