<script>
export default {
  name: "ModalBackground",

  props: {
    handleBackgroundClick: {
      type: Function,
      default: () => {},
    },
  },
};
</script>

<template>
  <div class="modal-background" @click="$emit('handleBackgroundClick')">
    <slot></slot>
  </div>
</template>

<style lang="scss" scoped>
.modal-background {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}
</style>
