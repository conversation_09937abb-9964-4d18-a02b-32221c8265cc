<template>
  <li class="accordion__item">
    <div class="accordion__trigger" :class="{ accordion__trigger_active: visible }" @click="open">
      <slot name="accordion-trigger"></slot>
      <div class="fold" :class="{ unfold: visible }">
        <img class="icon" src="@/assets/images/bottom-arrow.png" />
      </div>
    </div>

    <!-- <transition name="accordion" @enter="start" @after-enter="end" @before-leave="start" @after-leave="end"> -->
    <div class="accordion__content" v-show="visible">
      <ul>
        <slot name="accordion-content"></slot>
      </ul>
    </div>
    <!-- </transition> -->
  </li>
</template>

<script>
export default {
  props: {},
  inject: ["Accordion"],
  data() {
    return {
      index: null,
    };
  },
  computed: {
    visible() {
      return this.index === this.Accordion.active;
    },
  },
  methods: {
    open() {
      if (this.visible) {
        this.Accordion.active = null;
      } else {
        this.Accordion.active = this.index;
      }
    },
    start(el) {
      el.style.height = el.scrollHeight + "px";
    },
    end(el) {
      el.style.height = "";
    },
  },
  created() {
    this.index = this.Accordion.count++;
  },
};
</script>

<style lang="scss" scoped>
.accordion__item {
  // padding: 0 30px;
  position: relative;
}

ul {
  text-align: left;
  padding: 0;
  font-size: 14px;
}

.accordion__trigger {
  display: flex;
  justify-content: space-between;
  height: 100%;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 10px 0;
}

.accordion__content {
  display: flex;
  padding: 20px 0;
  color: #000000;
}

.fold {
  width: 12px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

img {
  width: 100%;
}

.unfold {
  transform: rotate(180deg);
}
</style>
