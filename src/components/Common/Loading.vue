<template>
  <div>
    <div class="loading-bg">
      <div>
        <div>
          <v-progress-circular
            indeterminate
            :size="40"
            :width="5"
            color="#41d8e6"
          ></v-progress-circular>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.loading-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;

  div {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70%;
  }
}

.loading-txt {
  font-family: GilroyMedium;
  font-weight: 600;
  color: #646464;
}
</style>