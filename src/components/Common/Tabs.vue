<template>
  <div>
    <div>
      <v-tabs
        v-model="tabName"
        color="#C9F4F8"
        class="history-tabs pa-0"
        center-active
        grow
        @change="changeTabs"
        mobile-breakpoint="xs"
        slider-color="#41D8E6"
        slider-size="3"
        height="40px"
      >
        <v-tab class="px-0 mx-0" href="#tab-cym702">Cym702</v-tab>
        <v-tab class="px-0 mx-0" href="#tab-blood">blood</v-tab>
      </v-tabs>
    </div>

    <v-tabs-items v-model="tabName" color="transparent">
      <v-tab-item value="tab-cym702" class="tab-item">tab-cym702</v-tab-item>
      <v-tab-item value="tab-blood">tab-blood</v-tab-item>
    </v-tabs-items>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentScrollValue: 0,
      tabName: "tab-cym702",
    };
  },

  mounted() {
    window.scrollTo(0, 0);

    let tab = this.$route.query.tab;

    if (tab == 0) {
      this.tabName = "tab-cym702";
    } else if (tab == 1) {
      this.tabName = "tab-blood";
    } else if (tab == 2) {
      this.tabName = "tab-prot";
    } else if (tab == 3) {
      this.tabName = "tab-glu";
    } else if (tab == 4) {
      this.tabName = "tab-ph";
    } else if (tab == 5) {
      this.tabName = "tab-keton";
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-tabs-slider-wrapper {
  color: #41d8e6;
  height: 3px !important;
}

::v-deep .v-tabs-slider {
  border-radius: 3px 3px 0px 0px !important;
}

::v-deep .v-tab {
  color: rgba(0, 0, 0, 0.32) !important;
  font-weight: 500 !important;
  line-height: 40px;
  letter-spacing: -0.05em !important;
  text-transform: none !important;
  font-size: 14px !important;
}
::v-deep .v-tab--active {
  font-size: 14px !important;
  font-weight: bold !important;
  color: #000000 !important;
}

::v-deep .v-tabs-bar__content {
  background: transparent !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

::v-deep .theme--light.v-tabs > .v-tabs-bar {
  background-color: transparent !important;
}

::v-deep .v-toolbar__content {
  height: auto !important;
}

::v-deep .v-tab {
  min-width: 60px !important;
  background-color: transparent !important;
}

// tab ripple 제거
::v-deep .v-ripple__container {
  display: none !important;
}

::v-deep .v-input--selection-controls__ripple {
  display: none !important;
}

::v-deep .v-tab:before {
  display: none !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}


</style>