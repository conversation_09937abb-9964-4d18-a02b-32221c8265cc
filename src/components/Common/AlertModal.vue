<template>
  <div>
    <div class="bg-modal">
      <div class="alert-window">
        <div class="close-btn__wrapper">
          <img
            @click="closeBtnHandler"
            src="@/assets/images_assets/icons/close-btn-solid-ic.png"
            alt=""
            class="x-circle-ic"
          />
        </div>
        <div class="alert-window__content" v-html="error"></div>
        <div class="alert-window__btn" @click="nextHandler">{{ btnText }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    error: String,
    btnText: String,
  },
  methods: {
    closeBtnHandler() {
      this.$emit("closeBtnHandler", true);
    },
    nextHandler() {
      this.$emit("nextHandler", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  gap: 10px;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px;
}

.close-btn__wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  img {
    width: 22px;
  }
}

.alert-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-window__content {
  font-size: 18px;
  // padding-top: 15px;
  color: #323232;
}

.alert-window__btn {
  width: 100%;
  margin-top: 20px;
  text-align: right;
  font-size: 18px;
  font-weight: 700;
  color: #41d8e6;
}
</style>
