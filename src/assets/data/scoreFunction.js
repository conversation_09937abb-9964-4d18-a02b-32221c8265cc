/**
 * 🔥 en-score
 * [normal, warning, caution, danger, good]
 *
 * 🔥 ko-score
 * [적절, 주의, 경고, 위험, 좋음]
 *
 * 🔥 color-value
 * normal : #00BB00
 * good : #41D8E6
 * warning : #FFCC00
 * caution : #EE0000
 * danger : #646464
 */

const bloodLevel = {
  1: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 100 },
  2: { en: "warning", ko: "주의", color: "#FFCC00", cymScore: 50 },
  3: { en: "caution", ko: "경고", color: "#EE0000", cymScore: 25 },
  4: { en: "danger", ko: "위험", color: "#646464", cymScore: 0 },
};

const glucoseLevel = {
  1: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 100 },
  2: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 75 },
  3: { en: "warning", ko: "주의", color: "#FFCC00", cymScore: 50 },
  4: { en: "caution", ko: "경고", color: "#EE0000", cymScore: 25 },
  5: { en: "danger", ko: "위험", color: "#646464", cymScore: 0 },
  6: { en: "danger", ko: "위험", color: "#646464", cymScore: 0 },
};

const proteinLevel = {
  1: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 100 },
  2: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 75 },
  3: { en: "warning", ko: "주의", color: "#FFCC00", cymScore: 50 },
  4: { en: "caution", ko: "경고", color: "#EE0000", cymScore: 25 },
  5: { en: "danger", ko: "위험", color: "#646464", cymScore: 0 },
  6: { en: "danger", ko: "위험", color: "#646464", cymScore: 0 },
};

const phLevel = {
  1: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 100 },
  2: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 100 },
  3: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 100 },
  4: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 100 },
  5: { en: "warning", ko: "주의", color: "#FFCC00", cymScore: 0 },
};

const ketoneLevel = {
  1: { en: "normal", ko: "적절", color: "#00BB00", cymScore: 100 },
  2: { en: "caution_plus_minus", ko: "주의(+/-)", color: "#FFCC00", cymScore: 75 },
  3: { en: "caution_plus", ko: "주의(+)", color: "#FFCC00", cymScore: 50 },
  4: { en: "warning", ko: "경고", color: "#FF6600", cymScore: 25 },
  5: { en: "danger", ko: "위험", color: "#646464", cymScore: 100 },
};

export default {
  Blood(level) {
    return {
      cymScore() {
        return bloodLevel[`${level}`].cymScore;
      },
      korean() {
        return bloodLevel[`${level}`].ko;
      },
      english() {
        return bloodLevel[`${level}`].en;
      },
      color() {
        return bloodLevel[`${level}`].color;
      },
    };
  },

  Protein(level) {
    return {
      cymScore() {
        return proteinLevel[`${level}`].cymScore;
      },
      korean() {
        return proteinLevel[`${level}`].ko;
      },
      english() {
        return proteinLevel[`${level}`].en;
      },
      color() {
        return proteinLevel[`${level}`].color;
      },
    };
  },

  Glucose(level) {
    return {
      cymScore() {
        return glucoseLevel[`${level}`].cymScore;
      },
      korean() {
        return glucoseLevel[`${level}`].ko;
      },
      english() {
        return glucoseLevel[`${level}`].en;
      },
      color() {
        return glucoseLevel[`${level}`].color;
      },
    };
  },

  Ph(level) {
    return {
      cymScore() {
        return phLevel[`${level}`].cymScore;
      },
      korean() {
        return phLevel[`${level}`].ko;
      },
      english() {
        return phLevel[`${level}`].en;
      },
      color() {
        return phLevel[`${level}`].color;
      },
    };
  },

  Ketone(level) {
    return {
      cymScore() {
        return ketoneLevel[`${level}`].cymScore;
      },
      korean() {
        return ketoneLevel[`${level}`].ko;
      },
      english() {
        return ketoneLevel[`${level}`].en;
      },
      color() {
        return ketoneLevel[`${level}`].color;
      },
    };
  },
};
