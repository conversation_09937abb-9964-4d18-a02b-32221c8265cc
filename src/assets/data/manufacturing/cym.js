const makeLocaleTime = (createdAt) => {
  const unixTime = new Date(createdAt).getTime();
  const curLocalTime = new Date(unixTime);
  const year = curLocalTime.getFullYear();
  const month = String(curLocalTime.getMonth() + 1).padStart(2, "0");
  const day = String(curLocalTime.getDate()).padStart(2, "0");
  const hours = String(curLocalTime.getHours()).padStart(2, "0");
  const minutes = String(curLocalTime.getMinutes()).padStart(2, "0");
  const seconds = String(curLocalTime.getSeconds()).padStart(2, "0");

  const localeDate = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.000Z`;
  // console.log(localeDate);
  return localeDate;
};

const sortingUrineTest = (urineTestResultList) => {
  // console.log(urineTestResultList);
  let blood = [];
  let glucose = [];
  let protein = [];
  let ph = [];
  let ketone = [];
  let cym = [];

  if (urineTestResultList.length === 0) {
    return {
      blood,
      glucose,
      protein,
      ph,
      ketone,
      cym,
    };
  } else {
    urineTestResultList.forEach((item) => {
      let createdAt = makeLocaleTime(item.createdAt);
      blood.push({ level: item.blood, createdAt: createdAt });
      glucose.push({ level: item.glucose, createdAt: createdAt });
      protein.push({ level: item.protein, createdAt: createdAt });
      ph.push({ level: item.ph, createdAt: createdAt });
      ketone.push({ level: item.ketone, createdAt: createdAt });
      cym.push({ value: item.cymScore, createdAt: createdAt });
    });
    // cym = classifyCymLevel(urineTestResultList);
    return {
      blood,
      glucose,
      protein,
      ph,
      ketone,
      cym,
    };
  }
};

const sortingHistoryData = (historyArr, cymType) => {
  // console.log(historyArr, cymType);
  const sortedArr = [];
  historyArr.forEach((obj) => {
    if (cymType in obj) {
      const createdAt = makeLocaleTime(obj.createdAt);
      sortedArr.unshift({ level: obj[cymType], createdAt: createdAt });
    }
  });
  // console.log(sortedArr);
  return sortedArr;
};

const sortingCymScore = (cymScoreArr) => {
  const cymScore = [];
  cymScoreArr.map((i) => {
    const createdAt = makeLocaleTime(i.createdAt);
    cymScore.unshift({ createdAt: createdAt, value: i.cymScore });
  });
  return cymScore;
};

export default {
  FETCH_CYM_DATA(urineTestResult) {
    return sortingUrineTest(urineTestResult);
  },
  MAKE_HISTORY_DATA(historyData, cymType) {
    return sortingHistoryData(historyData, cymType);
  },
  MAKE_CYMSCORE_DATA(cymScore) {
    return sortingCymScore(cymScore);
  },
};
