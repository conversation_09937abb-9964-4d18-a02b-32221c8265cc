import lodash from "lodash";

/**
 * TODO: 1. 하루의 총량을 계산한다.
 * TODO: 2. scatter graph데이터를 만든다.
 * TODO: 3. 세부데이터를 하루의 데이터로 만든다.
 * TODO: 4. month 세부데이터를 각 달의 리스트로 만든다.
 * TODO: 5. 각 달의 평균값을 구한다.
 * TODO: 6. 평균값을 구한다.
 */

/**
 * 필요한 경우는 주별, 월별, 년별
 * 1. 각각의 일별로 데이터 리스트를 만든다.
 * 2. 각 날짜별 데이터를 리턴한다.
 * 3. 각 월별 데이터를 리턴한다.
 * 4. 각 주별 데이터를 리턴한다.
 */

const createdAtDataProcessing = (createdAt) => {
  const unixTime = new Date(createdAt).getTime();
  const curLocalTime = new Date(unixTime);
  const year = curLocalTime.getFullYear();
  let month = curLocalTime.getMonth() + 1; // month is zero-based, so add 1
  let day = curLocalTime.getDate();
  let hours = curLocalTime.getHours();
  let minutes = curLocalTime.getMinutes();
  const timeSet = hours >= 12 ? "PM" : "AM";
  hours = hours % 12 || 12;
  month = month < 10 ? `0${month}` : month;
  day = day < 10 ? `0${day}` : day;
  hours = hours < 10 ? `0${hours}` : hours;
  minutes = minutes < 10 ? `0${minutes}` : minutes;
  // console.log(month);
  return {
    year() {
      return Number(year);
    },
    month() {
      return Number(month);
    },
    date() {
      // return Number(D);
      return `${year}.${month}.${day} ${hours}:${minutes} ${timeSet}`;
    },
    day() {
      return Number(day);
    },
    hour() {
      return Number(hours);
    },
    Week() {
      // return Number(day);
      return `${month}.${day}`;
    },
    Month() {
      return `${year}.${month}.${day}`;
    },
    Year() {
      return `${year}.${month}`;
    },
  };
};

// 각 달의 데이터를 분류하는 함수
const eachMonthList = (monthDetailDataArr) => {
  const monthData = monthDetailDataArr.monthlyData.map((item) => {
    return {
      value: item.value,
      createdAt: item.date.replace(/-/g, "."),
    };
  });
  // console.log(monthData);
  return monthData;
};

// 주와 일의 수분기록 데이블 만들기
const makeHistoryListForWeek = (careDetailDataArr) => {
  const allValues = [];

  for (const record of careDetailDataArr.records) {
    allValues.push(...record.values);
  }
  const historyList = allValues.map((item) => {
    return {
      value: item.value,
      createdAt: createdAtDataProcessing(item.eventTime).date(),
    };
  });
  // console.log(historyList);
  return historyList;
};
const makeHistoryListForDay = (careDetailDataArr) => {
  // console.log(careDetailDataArr.records);
  const historyList = careDetailDataArr.records[0].values.map((item) => {
    // console.log(new Date(String(item.createdAt)));
    return {
      id: item.id,
      value: item.value,
      createdAt: createdAtDataProcessing(item.eventTime).date(),
    };
  });
  return historyList;
};

// scatter graph 데이터 만들기 return [[1, 2], [1, 3]] = [[날짜, 시간]]  = [[04.23, 11]]
const makeScatterGraphData = (weekCareDetail) => {
  const scatterData = weekCareDetail.records
    .flatMap((item) => item.values)
    .map((obj) => {
      const createdAt = new Date(obj.createdAt);
      return [createdAt.getDate(), createdAt.getHours()];
    });

  return scatterData;
};

const makeDateData = (dateDataObject) => {
  const dateData = dateDataObject.dailyData.map((item) => {
    return {
      value: item.value,
      createdAt: createdAtDataProcessing(item.date).Month(),
    };
  });
  return dateData;
};

const getMaxValue = (data) =>
  Math.max(...(data.dailyData || data.monthlyData).map((item) => item.value));

export default {
  GET_WEIGHT_CARE_DATA(data, count) {
    let graphData = [];
    let historyData = [];

    switch (count) {
      case "d": {
        historyData = makeHistoryListForDay(data);
        break;
      }
      case "w": {
        graphData = makeDateData(data).reverse();
        historyData = makeHistoryListForWeek(data);
        break;
      }
      case "m": {
        graphData = makeDateData(data).reverse();
        historyData = makeDateData(data);
        break;
      }
      case "y": {
        graphData = eachMonthList(data).reverse();
        historyData = eachMonthList(data);
        break;
      }
    }
    return {
      graphData,
      historyData,
    };
  },

  GET_WATER_CARE_DATA(data, count) {
    let graphData = [];
    let historyData = [];
    let dayAvgData = [];
    let maxValue = 0;

    switch (count) {
      case "d": {
        historyData = makeHistoryListForDay(data);
        break;
      }
      case "w": {
        graphData = makeScatterGraphData(data);
        historyData = makeHistoryListForWeek(data);
        dayAvgData = makeDateData(data).reverse();
        break;
      }
      case "m": {
        graphData = makeDateData(data).reverse();
        historyData = makeDateData(data);
        maxValue = getMaxValue(data);
        break;
      }
      case "y": {
        graphData = eachMonthList(data).reverse();
        historyData = eachMonthList(data);
        maxValue = getMaxValue(data);
      }
    }
    return {
      graphData,
      historyData,
      maxValue,
      dayAvgData,
    };
  },

  GET_PEE_CARE_DATA(data, count) {
    let graphData = [];
    let historyData = [];
    let dayAvgData = [];
    let maxValue = 0;

    switch (count) {
      case "d": {
        historyData = makeHistoryListForDay(data);
        break;
      }
      case "w": {
        graphData = makeScatterGraphData(data);
        historyData = makeHistoryListForWeek(data);
        dayAvgData = makeDateData(data).reverse();
        break;
      }
      case "m": {
        graphData = makeDateData(data).reverse();
        historyData = makeDateData(data);
        maxValue = getMaxValue(data);
        break;
      }
      case "y": {
        graphData = eachMonthList(data).reverse();
        historyData = eachMonthList(data);
        maxValue = getMaxValue(data);
      }
    }
    return {
      graphData,
      historyData,
      maxValue,
      dayAvgData,
    };
  },
};
