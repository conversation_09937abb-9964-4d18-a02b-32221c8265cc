import Vue from "vue";
import App from "./App.vue";
import vuetify from "./plugins/vuetify";
import router from "./router";
import store from "./store";
import webview from "./service/webview";
import native from "./service/native";
import VueScrollPicker from "vue-scroll-picker";
import CircularCountDownTimer from "vue-circular-count-down-timer";
import Navigation from "./components/Common/Navigation.vue";
import Background from "./components/Common/Background.vue";
import "vue-scroll-picker/dist/style.css";
import i18n from "./i18n";
import VueApexCharts from "vue-apexcharts";

const KAKAO_SDK_KEY = "41625f45c9ccefeffa035b8a0d7a0417";
const SERVICE_WORKER = "serviceWorker";

console.warn(
  "%cIf you turn on debugging mode in the Webview console and do something abnormal, you can be punished.",
  "font-size: 24px; color: red; font-weight: bold;",
);

Vue.config.productionTip = false;

Vue.use(CircularCountDownTimer);
Vue.use(VueScrollPicker);
window.Native = native;
window.Webview = webview;

Vue.use(VueApexCharts);

Vue.component("apexchart", VueApexCharts);
Vue.component("navigation", Navigation);
Vue.component("background", Background);

window.Kakao.init(KAKAO_SDK_KEY);

Vue.config.productionTip = false;
// Vue.prototype.isIos = navigator.userAgent.toLowerCase().indexOf("iphone") > -1;
Vue.prototype.isIos = navigator.platform.includes("Mac");

new Vue({
  vuetify,
  store,
  router,
  i18n,
  created() {},
  render: (h) => h(App),
}).$mount("#app");

if (SERVICE_WORKER in navigator) {
  window.addEventListener("load", () => {
    navigator.serviceWorker
      .register("/service-worker.js")
      .then((registration) => {
        console.log("ServiceWorker 등록 성공:", registration);
      })
      .catch((error) => {
        console.log("ServiceWorker 등록 실패:", error);
      });
  });
}
