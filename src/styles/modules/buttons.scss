@import "../mixins/mixins.scss";

// /* chart toggle btn css */
// /* 5회, 30회 button-toggle */
// .history-chart-btn {
//   width: calc(100% / 2);
//   padding: 4px 3px;
//   background-color: $disable;
//   border-radius: 5px;
//   color: $date-color;
//   @include flexBoxCenter(center);
// }

// .l--flex-center {
//   width: 100%;
//   padding: 0 3px;
//   @include flexBoxCenter(center);
// }

// .chart-btn__el {
//   border-radius: 4px;
//   width: calc(100% / 3);
//   font-size: 14px;
//   letter-spacing: -0.03em;
//   line-height: 19px;
// }

// .chart-btn__el--isActive {
//   width: calc(100% / 3);
//   padding: 2px 0px;
//   background-color: #fff;
//   color: $black;
//   box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
//   font-weight: 500;
// }

/* 일, 주, 월, 년 button-toggle */
.chart-controll-btn-wrapper {
  padding: 10px 0px 20px 0px;
}

.care-chart-btn {
  margin-top: 3px;
  width: 100%;
  max-width: 340px;
  height: 26px;
  background-color: $disable;
  border-radius: 5px;
  color: $date-color;
  @include flexBoxCenter(space-evenly);
}

.care-chart-btn__el {
  margin: 0 5px;
  width: 75%;
  height: 20px;
  border-radius: 4px;
  font-size: 12px;
  letter-spacing: -0.03em;
  line-height: 18px;
}

.care-chart-btn--isActive {
  background-color: #fff;
  color: $black;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}
