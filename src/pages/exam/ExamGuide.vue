<template>
  <div>
    <background>
      <HeaderNav page="guide1" />
      <ExamGuide />
      <FixedButton :title="title" :path="path" :color="color" :textColor="textColor" />
    </background>
  </div>
</template>

<script>
import HeaderNav from "@/components/Exam/HeaderNav.vue";
import FixedButton from "@/components/Exam/FixedButton.vue";

import ExamGuide from "@/components/Exam/ExamGuide.vue";

export default {
  components: {
    HeaderNav,
    FixedButton,
    ExamGuide,
  },

  data() {
    return {
      title: this.$i18n.t("btn_content_first"),
      path: "/exam/wait",
      color: "#41d8e6",
      textColor: "#fff",
      hideDelimiters: true,
      touchless: true,
      currentState: 2,
    };
  },

  methods: {
    changeCurrentState(idx) {
      this.currentState = idx;
    },
  },
};
</script>

<style></style>
