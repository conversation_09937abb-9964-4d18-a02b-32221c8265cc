<template>
  <div>
    <background>
      <HeaderNav page="intro" />
      <ExamIntro />
      <FixedButton :title="title" :path="path" :color="color" :textColor="textColor" />
    </background>
  </div>
</template>

<script>
import HeaderNav from "@/components/Exam/HeaderNav.vue";
import FixedButton from "@/components/Exam/FixedButton.vue";

import ExamIntro from "@/components/Exam/ExamIntro.vue";

export default {
  components: {
    HeaderNav,
    FixedButton,
    ExamIntro,
  },

  data() {
    return {
      title: this.$i18n.t("note_confirm_btn"),
      path: "/exam/guide",
      color: "#41d8e6",
      textColor: "#fff",
      hideDelimiters: true,
      touchless: true,
      currentState: 0,
    };
  },

  methods: {
    changeCurrentState(idx) {
      this.currentState = idx;
    },
  },
};
</script>

<style></style>
