<template>
  <div>
    <!-- <background> -->
    <!-- TODO: 🔖 header component -->
    <div>
      <div class="history-header">
        <div class="history-header__wrapper">
          <div class="history-header_nav">
            <router-link to="/login"><v-icon>$back_btn_bold</v-icon> </router-link>
          </div>
          <div class="history-header_title" :class="lang === 'ko' ? '' : 'en-title'">
            {{ $t("comment_findaccount_title") }}
          </div>
        </div>
      </div>
      <div class="tab__wrapper">
        <v-tabs
          v-model="tabName"
          color="#C9F4F8"
          class="history-tabs pa-0"
          center-active
          grow
          @change="changeTabs"
          mobile-breakpoint="xs"
          slider-color="#41D8E6"
          slider-size="3"
          height="40px"
        >
          <v-tab class="px-0 mx-0" href="#findId">{{ $t("comment_findaccount_tab_1") }}</v-tab>
          <v-tab class="px-0 mx-0" href="#findPw">{{ $t("comment_findaccount_tab_2") }}</v-tab>
        </v-tabs>
      </div>
    </div>

    <v-tabs-items v-model="tabName" color="transparent" :touchless="enableTouchless">
      <v-tab-item value="findId">
        <IdSearch />
      </v-tab-item>
      <v-tab-item value="findPw">
        <PasswordSearch />
      </v-tab-item>
    </v-tabs-items>
    <!-- </background> -->
  </div>
</template>

<script>
// import Background from "@/components/Common/Background.vue";
import IdSearch from "@/components/MyAccount/IdSearch.vue";
import PasswordSearch from "@/components/MyAccount/PasswordSearch.vue";
export default {
  components: {
    // Background,
    IdSearch,
    PasswordSearch,
  },
  data() {
    return {
      currentScrollValue: 0,
      tabName: this.$i18n.t("comment_findaccount_tab_1"),
      lang: "",
    };
  },
  mounted() {
    window.scrollTo(0, 0);
    this.lang = this.$i18n.locale.includes("ko") ? "ko" : "en";
  },
  computed: {
    enableTouchless() {
      if (this.$store.state.isEnableTouch) {
        return true;
      } else {
        return false;
      }
    },
  },
  methods: {
    changeTabs(tabIndex) {
      // console.log(tabIndex);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-tabs-slider-wrapper {
  color: #41d8e6;
  height: 3px !important;
  // padding: 0px 15px;
}
::v-deep .v-tabs-slider {
  border-radius: 3px 3px 0px 0px !important;
}
::v-deep .v-tab {
  color: rgba(0, 0, 0, 0.32) !important;
  font-weight: 500 !important;
  line-height: 40px;
  letter-spacing: -0.05em !important;
  text-transform: none !important;
  font-size: 16px !important;
}
::v-deep .v-tab--active {
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #000000 !important;
}
::v-deep .v-tabs-bar__content {
  background: transparent !important;
}
::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}
::v-deep .theme--light.v-tabs > .v-tabs-bar {
  background-color: #c9f4f8 !important;
}
::v-deep .v-toolbar__content {
  height: auto !important;
}
::v-deep .v-tab {
  font-size: 18px !important;
  min-width: 60px !important;
  background-color: transparent !important;
}
// tab ripple 제거
::v-deep .v-ripple__container {
  display: none !important;
}
::v-deep .v-input--selection-controls__ripple {
  display: none !important;
}
::v-deep .v-tab:before {
  display: none !important;
}
::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

.history-header {
  padding: 0px 30px;
  background-color: #c9f4f8;
}
.history-header_title {
  // font-family: "Noto Sans KR";
  font-size: 29px;
  text-align: left;
  font-weight: 700;
  padding-top: 5px;
  padding-bottom: 23px;
  color: #000000;
}

.tab__wrapper {
  padding: 0 30px;
  background-color: #c9f4f8;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-top: 60px;
}
</style>
