<template>
  <div>
    <background>
      <Header :type="page" :count="count" :avgScore="pee" :date="date" />
      <div class="white-background pt-45">
        <template v-if="count === 'd'">
          <PeeController
            v-if="loaded"
            :type="page"
            :volume="pee"
            @addWaterHistoryList="addWaterHistoryList"
            @saveBtnHandler="saveBtnHandler"
          />
        </template>

        <template v-else-if="count === 'w'">
          <ScatterChart
            v-if="loaded"
            :page="page"
            :count="count"
            :scatterData="graphData"
            :weekDayData="weekDayData"
          />
        </template>

        <template v-else>
          <LineChart
            v-if="loaded"
            :historyData="graphData"
            :page="page"
            :count="count"
            @showWeeksTrendGraph="showWeeksTrendGraph"
            @getMonth="getMonth"
            @getDate="getDate"
          />
          <WeekTrendModal
            v-if="count === 'm' && showWeekTrendModal"
            @closeWeekTrendModal="closeWeekTrendModal"
            :specificDate="specificDate"
            :page="page"
          />
        </template>
        <ChartBtn :count="count" @changeCount="changeCount" />
      </div>

      <div class="comment-txt__wrapper pt-21 pd-20"></div>

      <!-- history list -->
      <CareHistoryTable
        v-if="!noData"
        :history="historyData"
        :count="count"
        @reloadData="reloadData"
        type="urine"
      />
      <navigation :path="path"></navigation>
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";
import Header from "@/components/Care/Header.vue";

import PeeController from "@/components/DayCareControllers/PeeController.vue";
import LineChart from "@/components/Chart/LineChart.vue";
import ScatterChart from "@/components/Chart/ScatterChart.vue";
import WeekTrendModal from "@/components/Care/WeekTrendModal.vue";
import ChartBtn from "@/components/Care/CareChartBtns.vue";
import CareHistoryTable from "@/components/Care/CareHistoryTable.vue";

import API from "@/api/care/index.js";
import DataProcessing from "@/assets/data/manufacturing/care.js";

export default {
  components: {
    Background,
    Header,
    ChartBtn,
    CareHistoryTable,
    PeeController,
    ScatterChart,
    LineChart,
    WeekTrendModal,
  },
  data() {
    return {
      path: "/home",
      historyData: [],
      graphData: [],
      scatterData: [],
      monthData: {},
      count: "d",
      page: "pee",
      noData: true,
      pee: 0,
      date: "",
      showWeekTrendModal: false,
      loaded: false,
      specificDate: "",
      weekDayData: [],
    };
  },
  methods: {
    reloadData() {
      this.getHistoryData(this.count);
    },
    changeCount(count) {
      this.loaded = false;
      this.count = count;
      this.getHistoryData(count);
    },
    closeWeekTrendModal(fromChild) {
      this.showWeekTrendModal = fromChild;
    },
    showWeeksTrendGraph() {
      this.showWeekTrendModal = true;
    },
    getSubjectId() {
      if (JSON.parse(sessionStorage.getItem("subjects")) !== null) {
        const subjects = JSON.parse(sessionStorage.getItem("subjects"));
        const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
        const subjectId =
          JSON.parse(sessionStorage.getItem("selectUser")) !== null
            ? subjects[selectedId].id
            : JSON.parse(localStorage.getItem("lastSelectedUser")) !== null
            ? Number(localStorage.getItem("lastSelectedUser"))
            : Number(localStorage.getItem("subjectId"));
        return subjectId;
      } else {
        const subjectId =
          this.type === "kardio" &&
          JSON.parse(localStorage.getItem("lastSelectedUser")) !== null
            ? Number(localStorage.getItem("lastSelectedUser"))
            : Number(localStorage.getItem("subjectId"));
        return subjectId;
      }
    },
    async saveBtnHandler(type, value) {
      try {
        const subjectId = this.getSubjectId();
        const careValue = {
          value: 1,
        };
        const response = await API.FetchUpdateCareData(
          subjectId,
          type,
          careValue
        );
        // console.log(response);
        if (response.status === 201) {
          this.getHistoryData(this.count);
        }
      } catch (e) {
        console.log(e);
      }
    },
    formatDate(count) {
      const [year, month, day] = this.curDate(count);
      switch (count) {
        case "d":
          return [year, month, day];
        case "w":
          return [year, month, day];
        case "m":
          return [year, month, 1];
        case "y":
          return [year, 0, 1];
      }
    },

    curDate(count) {
      const curLocalTime = new Date();
      const start = new Date();
      start.setDate(start.getDate() - start.getDay() + 1);
      const year =
        count === "w" ? start.getFullYear() : curLocalTime.getFullYear();
      const month = count === "w" ? start.getMonth() : curLocalTime.getMonth();
      const day = count === "w" ? start.getDate() : curLocalTime.getDate();
      return [year, month, day];
    },

    getDayDate() {
      const localTime = new Date();
      const year = localTime.getFullYear();
      const month = localTime.getMonth();
      const day = localTime.getDate();
      return `${String(year).slice(2)}.${month + 1}.${day}`;
    },

    getWeekPeriod() {
      const now = new Date();
      const start = new Date(now);
      start.setDate(now.getDate() - now.getDay() + 1);

      const end = new Date(start);
      end.setDate(start.getDate() + 6);
      // 해당 주의 월요일, 일요일 날짜 반환

      const monYear = start.getFullYear();
      const monMonth = start.getMonth() + 1;
      const monDay = start.getDate();
      const sunYear = end.getFullYear();
      const sunMonth = end.getMonth() + 1;
      const sunDay = end.getDate();
      return `${String(monYear).slice(2)}.${monMonth}.${monDay} ~ ${String(
        sunYear
      ).slice(2)}.${sunMonth}.${sunDay}`;
    },

    getMonthPeriod() {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      // 해당 월의 시작일자와 말일 반환
      const year = start.getFullYear();
      const month = start.getMonth() + 1;
      const endDate = end.getDate();
      return `${String(year).slice(2)}.${month}.1 ~ ${String(year).slice(
        2
      )}.${month}.${endDate}`;
    },

    getYearPeriod() {
      const now = new Date();
      const yaer = now.getFullYear();
      return `${String(yaer).slice(2)}.01 ~ ${String(yaer).slice(2)}.12`;
    },

    getUtcOffset() {
      const now = new Date();
      const utcOffsetMinutes = now.getTimezoneOffset();
      const utcOffsetHours = -utcOffsetMinutes / 60;
      return utcOffsetHours;
    },

    async getHistoryData(count) {
      this.loaded = false;
      try {
        const [year, month, day] = this.formatDate(count);
        const utcStart = new Date(year, month, day, 0, 0, 0).toISOString();
        const [y, m, d] = this.curDate();
        const utcEnd = new Date(y, m, d + 1, 0, 0, 0).toISOString();
        const subjectId = this.getSubjectId();
        const utcOffset = this.getUtcOffset();
        const periodType = this.count === "y" ? `&periodType=year` : "";
        const { data, config } = await API.GetCareData(
          subjectId,
          "urine",
          utcStart,
          utcEnd,
          utcOffset,
          periodType
        );
        // console.log(count, data, config.url);
        this.makeData(data, count);
      } catch (error) {
        console.log(error);
      }
    },

    makeData(data, count) {
      // if (data.length === 0) {
      //   this.noData = true;
      //   this.pee = 0;
      //   this.historyData = [];
      //   this.loaded = true;
      // } else {
      //   this.noData = false;
      //   const careData = DataProcessing.GET_PEE_CARE_DATA(data, count);
      //   this.graphData = careData.graphData;
      //   this.historyData = careData.historyData;
      //   this.weekDayData = careData.dayAvgData;
      //   if (count === "d") {
      //     this.volume = careData.avgScore;
      //     this.pee = careData.avgScore;
      //     this.dateRange = careData.dateRange.startDate;
      //   } else {
      //     this.pee = Number(careData.avgScore);
      //     this.date = `${careData.dateRange.startDate} ~ ${careData.dateRange.endDate}`;
      //   }
      //   this.loaded = true;
      // }
      if (
        data?.records?.length === 0 ||
        data?.monthlyData?.length === 0 ||
        data?.dailyData?.length === 0
      ) {
        if (data.average === null) {
          switch (count) {
            case "d": {
              this.noData = false;
              this.date = this.getDayDate();
              break;
            }
            case "w": {
              this.noData = true;
              this.date = this.getWeekPeriod();
              break;
            }
            case "m": {
              this.noData = true;
              this.date = this.getMonthPeriod();
              break;
            }
            case "y": {
              this.noData = true;
              this.date = this.getYearPeriod();
              break;
            }
          }
          this.pee = 0;
          this.historyData = [];
          this.loaded = true;
        }
        this.loaded = true;
      } else {
        switch (count) {
          case "d": {
            this.noData = false;
            this.pee = data.dailyData[0].value;
            this.date = this.getDayDate();
            const careData = DataProcessing.GET_WATER_CARE_DATA(data, count);
            this.historyData = careData.historyData;
            break;
          }
          case "w":
            this.date = this.getWeekPeriod();
            break;
          case "m":
            this.date = this.getMonthPeriod();
            break;
          case "y":
            this.date = this.getYearPeriod();
            break;
        }
        const careData = DataProcessing.GET_WATER_CARE_DATA(data, count);
        // console.log(careData);
        this.historyData = careData.historyData;
        this.graphData = careData.graphData;
        this.weekDayData = careData.dayAvgData;
        // const maxValue = careData.maxValue;
        // this.water = data.average;
        this.pee = data.average;
        // this.targetWater = maxValue < data.subject?.targetWater ? data.subject?.targetWater : maxValue;
        // this.remaining = this.targetWater - this.volume;
        // this.toTargetValue = (data.subject?.targetWeight - this.weight).toFixed(1);
        this.noData = false;
        this.loaded = true;
      }
    },

    getWaterDetailDate(fromChild) {
      this.loaded = true;
    },
    getDate(fromChild) {
      // console.log(fromChild);
      this.loaded = true;
      this.specificDate = fromChild.createdAt;
    },
    getMonth(fromChild) {
      this.getWaterDetailDate(fromChild);
      this.graphData = this.monthData[`${fromChild}`].map((item) => {
        const itemDate = item.createdAt.split("T")[0];
        const itemTime = item.createdAt.split("T")[1];
        const [year, month, date] = itemDate.split("-");
        const [hour, minute] = itemTime.split(":");

        return {
          value: item.value,
          createdAt: `${year}.${month}.${date} ${hour}:${minute}PM`,
        };
      });
    },
    addWaterHistoryList(fromChild) {
      this.history = [...this.history, fromChild];
    },
  },
  mounted() {
    this.getHistoryData(this.count);
    // console.log(this.pee);
  },
};
</script>

<style lang="scss" scoped>
.white-background {
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.comment-txt__wrapper {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #646464;
  text-align: left;
  padding-left: 30px;
  span {
    color: #41d8e6;
  }
}
</style>
