<template>
  <div>
    <!-- <div class="bg-modal" v-show="showGuide">
      <div class="close-icon__wrapper" @click="cancelHandler">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div class="setting-icon__wrapper">
        <img src="@/assets/images/guide_img/care_settings_ic.png" />
      </div>
      <div class="arrow-icon__wrapper">
        <img src="@/assets/images/guide_img/arrow-up.png" />
      </div>
      <div class="guide-contents__wrapper">
        <div class="guide-title">나의 목표 체중이란?</div>
        <div class="guide-contents">
          앱 가입 시 설정한 목표체중까지<br />
          남은 몸무게를 알려드립니다.<br /><br />
          목표체중을 수정하고 싶으시면<br />
          오른쪽 위의 설정 버튼을 눌러주세요.
        </div>
        <div class="guide-desc__wrapper">
          <img src="@/assets/images/guide_img/weight_guide.png" />
        </div>
      </div>
    </div> -->

    <background>
      <Header
        :type="type"
        :avgScore="weight"
        :count="count"
        :noData="noData"
        :date="date"
        @openEditModalWindow="openEditModalWindow"
      />
      <div class="white-background pt-45">
        <template v-if="count === 'd' && loaded">
          <WeightController :weight="weight" @saveBtnHandler="saveBtnHandler" />
        </template>

        <template v-else>
          <LineChart
            v-if="loaded"
            :min="minWeight"
            :max="maxWeight"
            :target="targetWeight"
            :page="page"
            :count="count"
            :historyData="graphData"
            :noData="noData"
          />
          <!-- @getMonth="getMonth" -->
        </template>
        <ChartBtn :count="count" @changeCount="changeCount" />
      </div>

      <!-- <div v-if="count === 'd' && targetWeight !== 0" class="comment-txt__wrapper pt-21 pd-20">
        {{ $t("until_now") }} <span class="weight-data">{{ weightDecreaseValue }}kg</span> {{ $t("decrease") }} -->
      <!-- {{ $t("untile_target") }}
        <span class="weight-data">{{ toTargetValue }}kg</span> {{ $t("remain") }}
      </div>
      <div v-else class="comment-txt__wrapper pt-21 pd-20"></div> -->
      <div class="comment-txt__wrapper pt-21 pd-20"></div>

      <!-- history list -->
      <CareHistoryTable
        v-if="!noData"
        :history="historyData"
        :noData="noData"
        :count="count"
        @reloadData="reloadData"
        type="weight"
      />
      <navigation :path="path"></navigation>
      <EditWaterValueModal
        :page="page"
        :targetValue="targetWeight"
        v-if="showEditModalWindow"
        @closeEditModalWindow="closeEditModalWindow"
        @saveBtnHandler="targetSaveBtnHandler"
      />
      <div class="snackbar">
        <v-snackbar v-model="saveSuccess" timeout="2000">{{
          succesContent
        }}</v-snackbar>
        <v-snackbar v-model="saveFail" timeout="2000" color="#EE0000">{{
          failContent
        }}</v-snackbar>
      </div>
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";

// care componets
import Header from "@/components/Care/Header.vue";
import ChartBtn from "@/components/Care/CareChartBtns.vue";
import CareHistoryTable from "@/components/Care/CareHistoryTable.vue";
import EditWaterValueModal from "@/components/Care/EditWaterValueModal.vue";

// chart components
import LineChart from "@/components/Chart/LineChart.vue";

// dayCareController components
import WeightController from "@/components/DayCareControllers/WeightController.vue";

import API from "@/api/care/index.js";
import { updateSubjectInfo } from "@/api/user/index.js";
import DataProcessing from "@/assets/data/manufacturing/care.js";

export default {
  components: {
    Background,
    Header,
    ChartBtn,
    CareHistoryTable,
    WeightController,
    LineChart,
    EditWaterValueModal,
  },
  data() {
    return {
      path: "/home",
      historyData: [],
      graphData: [],
      count: "d",
      type: "weight",
      page: "weight",
      weight: 0,
      loaded: false,
      noData: false,
      saveSuccess: false,
      succesContent: this.$i18n.t("save_success"),
      saveFail: false,
      failContent: this.$i18n.t("save_fail"),
      date: "",
      weightDecreaseValue: 0,
      toTargetValue: 0,
      targetWeight: 0,
      maxWeight: 100,
      minWeight: 10,
      showEditModalWindow: false,
      isCalled: false,
      showGuide: false,
    };
  },
  methods: {
    reloadData() {
      this.getHistoryData(this.count);
    },
    changeCount(count) {
      // console.log(count);
      if (this.count !== count) {
        this.count = count;
        this.getHistoryData(count);
      } else {
        // console.log("already loaded");
      }
    },
    openEditModalWindow() {
      this.showEditModalWindow = true;
    },
    closeEditModalWindow() {
      this.showEditModalWindow = false;
    },
    async targetSaveBtnHandler(inputValue) {
      // console.log(inputValue);
      try {
        const subjectId = this.getSubjectId();
        const changedTargetWeight = {
          targetWeight: Number(inputValue),
        };
        const res = await updateSubjectInfo(subjectId, changedTargetWeight);
        // console.log(res);
        if (res.status === 200) {
          // console.log("success");
          this.saveSuccess = true;
          this.getHistoryData(this.count);
        }
      } catch (e) {
        this.saveFail = true;
        console.log(e);
      }
    },
    async saveBtnHandler(type, value) {
      try {
        const weightValue = {
          value: Number(value),
        };
        // console.log(weightValue);
        const subjectId = this.getSubjectId();
        const response = await API.FetchUpdateCareData(
          subjectId,
          type,
          weightValue
        );
        if (response.status === 201) {
          this.saveSuccess = true;
          this.getHistoryData(this.count);
        }
      } catch (error) {
        this.saveFail = true;
        console.log(error);
      }
    },
    getSubjectId() {
      if (JSON.parse(sessionStorage.getItem("subjects")) !== null) {
        const subjects = JSON.parse(sessionStorage.getItem("subjects"));
        const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
        const subjectId =
          JSON.parse(sessionStorage.getItem("selectUser")) !== null
            ? subjects[selectedId].id
            : JSON.parse(localStorage.getItem("lastSelectedUser")) !== null
            ? Number(localStorage.getItem("lastSelectedUser"))
            : Number(localStorage.getItem("subjectId"));
        return subjectId;
      } else {
        const subjectId =
          this.type === "kardio" &&
          JSON.parse(localStorage.getItem("lastSelectedUser")) !== null
            ? Number(localStorage.getItem("lastSelectedUser"))
            : Number(localStorage.getItem("subjectId"));
        return subjectId;
      }
    },

    formatDate(count) {
      const [year, month, day] = this.curDate(count);
      switch (count) {
        case "d":
          return [year, month, day];
        case "w":
          return [year, month, day];
        case "m":
          return [year, month, 1];
        case "y":
          return [year, 0, 1];
      }
    },

    curDate(count) {
      const curLocalTime = new Date();
      const start = new Date();
      start.setDate(start.getDate() - start.getDay() + 1);
      const year =
        count === "w" ? start.getFullYear() : curLocalTime.getFullYear();
      const month = count === "w" ? start.getMonth() : curLocalTime.getMonth();
      const day = count === "w" ? start.getDate() : curLocalTime.getDate();
      return [year, month, day];
    },

    getDayDate(date) {
      const localTime = new Date(date);
      const year = localTime.getFullYear();
      const month = localTime.getMonth();
      const day = localTime.getDate();
      return `${String(year).slice(2)}.${month + 1}.${day}`;
    },

    getWeekPeriod() {
      const now = new Date();
      const start = new Date(now);
      start.setDate(now.getDate() - now.getDay() + 1);

      const end = new Date(start);
      end.setDate(start.getDate() + 6);
      // 해당 주의 월요일, 일요일 날짜 반환

      const monYear = start.getFullYear();
      const monMonth = start.getMonth() + 1;
      const monDay = start.getDate();
      const sunYear = end.getFullYear();
      const sunMonth = end.getMonth() + 1;
      const sunDay = end.getDate();
      return `${String(monYear).slice(2)}.${monMonth}.${monDay} ~ ${String(
        sunYear
      ).slice(2)}.${sunMonth}.${sunDay}`;
    },

    getMonthPeriod() {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      // 해당 월의 시작일자와 말일 반환
      const year = start.getFullYear();
      const month = start.getMonth() + 1;
      const endDate = end.getDate();
      return `${String(year).slice(2)}.${month}.1 ~ ${String(year).slice(
        2
      )}.${month}.${endDate}`;
    },

    getYearPeriod() {
      const now = new Date();
      const yaer = now.getFullYear();
      return `${String(yaer).slice(2)}.01 ~ ${String(yaer).slice(2)}.12`;
    },

    getUtcOffset() {
      const now = new Date();
      const utcOffsetMinutes = now.getTimezoneOffset();
      const utcOffsetHours = -utcOffsetMinutes / 60;
      return utcOffsetHours;
    },

    async getHistoryData(count) {
      this.loaded = false;
      try {
        const [year, month, day] = this.formatDate(count);
        const utcStart = new Date(year, month, day, 0, 0, 0).toISOString();
        const [y, m, d] = this.curDate();
        const utcEnd = new Date(y, m, d + 1, 0, 0, 0).toISOString();
        const subjectId = this.getSubjectId();
        const utcOffset = this.getUtcOffset();
        const periodType = this.count === "y" ? `&periodType=year` : "";
        const { data, config } = await API.GetCareData(
          subjectId,
          this.type,
          utcStart,
          utcEnd,
          utcOffset,
          periodType
        );
        // console.log(count, data, config.url);
        this.makeData(data, count);
      } catch (error) {
        console.log(error);
      }
    },

    makeData(data, count) {
      if (
        data?.records?.length === 0 ||
        data?.monthlyData?.length === 0 ||
        data?.dailyData?.length === 0
      ) {
        if (data.average === null) {
          switch (count) {
            case "d": {
              this.noData = false;
              const recentWeight = data.recentWeight;
              const initialWeight = data.subject.initialWeight;
              if (recentWeight === null || recentWeight === undefined) {
                this.weight = initialWeight;
                this.date = this.getDayDate(data.subject.eventTime);
              } else {
                this.weight = recentWeight.value;
                this.date = this.getDayDate(recentWeight.eventTime);
              }
              break;
            }
            case "w": {
              this.noData = true;
              this.date = this.getWeekPeriod();
              break;
            }
            case "m": {
              this.noData = true;
              this.date = this.getMonthPeriod();
              break;
            }
            case "y": {
              this.noData = true;
              this.date = this.getYearPeriod();
              break;
            }
          }
          this.historyData = [];
          this.loaded = true;
        }
        this.loaded = true;
      } else {
        console.log(data);
        switch (count) {
          case "d": {
            this.noData = false;
            this.weight = data.records[0].values[0].value;
            this.date = this.getDayDate(data.records[0].values[0].eventTime);
            const careData = DataProcessing.GET_WEIGHT_CARE_DATA(data, count);
            this.historyData = careData.historyData;
            break;
          }
          case "w":
            this.date = this.getWeekPeriod();
            break;
          case "m":
            this.date = this.getMonthPeriod();
            break;
          case "y":
            this.date = this.getYearPeriod();
            break;
        }
        const careData = DataProcessing.GET_WEIGHT_CARE_DATA(data, count);
        console.log(careData);
        this.historyData = careData.historyData;
        this.graphData = careData.graphData;
        this.weight = data.average;
        this.targetWeight = data.subject?.targetWeight;
        this.toTargetValue = (data.subject?.targetWeight - this.weight).toFixed(
          1
        );
        this.maxWeight =
          count === "d"
            ? this.weight
            : careData.graphData.reduce(
                (max, cur) => Math.max(max, cur.value),
                0
              );
        this.minWeight =
          count === "d"
            ? this.targetWeight
            : careData.graphData.reduce(
                (min, cur) => Math.min(min, cur.value),
                100
              );
        this.noData = false;
        this.loaded = true;
      }
    },
    cancelHandler() {
      this.showGuide = false;
      localStorage.setItem("weightGuide", false);
    },
  },

  mounted() {
    this.getHistoryData(this.count);
    this.showGuide =
      !!JSON.parse(localStorage.getItem("weightGuide")) ||
      JSON.parse(localStorage.getItem("weightGuide")) === null
        ? true
        : false;
  },
};
</script>

<style lang="scss" scoped>
.white-background {
  background-color: #fff;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.8);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 65px;
  left: 30px;
  img {
    width: 26px;
  }
}
.setting-icon__wrapper {
  position: absolute;
  top: 151px;
  right: 21px;
  img {
    width: 36px;
  }
}

.arrow-icon__wrapper {
  position: absolute;
  top: 220px;
  right: 30px;
  img {
    width: 18px;
  }
}

.guide-contents__wrapper {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 200px;
}

.guide-title {
  color: #41d8e6;
  font-size: 20px;
  font-weight: 700;
}

.guide-contents {
  color: #ffffff;
  font-size: 16px;
  margin: 20px 0 30px;
}

.guide-desc__wrapper {
  // position: absolute;
  width: 100%;
  // left: 0;
  padding: 0 20px;
  img {
    width: 100%;
  }
}

.comment-txt__wrapper {
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #646464;
  text-align: left;
  padding: 20px 30px;
}

.weight-data {
  font-weight: 700;
  color: #41d8e6;
}

.noData-txt__wrapper {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #646464;
  text-align: left;
  text-indent: 30px;
}

// snackbar custom
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
</style>
