<template>
  <section>
    <div class="cym-total-info__box">
      <div class="cym-emblem__wrapper" @click="navigateToHistory">
        <CymEmblem />
        <div class="total-score__block">
          <span v-if="haveTotalScore" class="cym_score">{{
            setTotalScore
          }}</span>
          <span v-else class="cym_score_none" :class="isKo ? 'isKo' : 'isEn'">{{
            $t("try_test")
          }}</span>
        </div>
      </div>

      <div class="cym-chart__wrapper">
        <router-link :to="{ path: '/home/<USER>' }">
          <div class="history-link__block">
            <span class="detail-txt">
              {{
                username.length > 8 ? username.slice(0, 8) + "..." : username
              }}
            </span>
            <div class="icon__wrapper">
              <Arrow />
            </div>
          </div>
        </router-link>
        <div class="cym-chart__block">
          <HomeLineChart
            ref="homeChart"
            :chartData="setGraphData"
            @setTotalScore="setTotalScore"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import CymEmblem from "@/assets/svg/cym_emblem.svg";
import HomeLineChart from "@/components/Home/HomeLineChart.vue";
import Arrow from "@/assets/images_assets/icons/Arrow.svg";

export default {
  components: {
    CymEmblem,
    HomeLineChart,
    Arrow,
  },
  props: {
    username: String,
    graphData: Array,
    avgCymScore: Number,
  },
  data() {
    return {
      avgScore: this.avgCymScore,
      totalScore: 0,
      chartData: [],
      isKo: this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn") || this.$i18n.locale.includes("ja")
    };
  },

  watch: {
    graphData: {
      deep: true,
      immediate: true,
      handler(newData) {
        this.updateChartData(newData);
      },
    },
  },

  computed: {
    haveTotalScore() {
      if (this.graphData.length === 0) {
        return false;
      }
      return true;
    },
    setGraphData() {
      const graphData = this.graphData.map((item) => item.value);
      console.log(graphData);
      return graphData;
    },
    setTotalScore() {
      const activeIdx =
        this.graphData.length < 5
          ? this.$store.state.activeDropletIdx % 5
          : this.$store.state.activeDropletIdx;
      return this.haveTotalScore ? this.graphData[activeIdx % 5].value : 0;
    },
  },
  methods: {
    updateChartData(newData) {
      if (this.$refs.homeChart) {
        this.$refs.homeChart.updateSeries([
          {
            name: "chart",
            data: newData.map((item) => item.value),
          },
        ]);
      }
    },
    //   this.totalScore = this.graphData[dataPointIdx].value;
    navigateToHistory() {
      this.$router.push("/home/<USER>");
    },
    initialSet() {
      const totalScore = this.graphData[0].value;
      this.totalScore = totalScore;
      this.$store.commit("GET_TOTAL_SCORE", totalScore);
    },
  },
  mounted() {
    this.graphData.length !== 0 ? this.initialSet() : null;
    console.log(this.graphData);
  },
};
</script>

<style lang="scss" scoped>
.history-link__block {
  width: 100%;
  text-align: right;
  height: 25px;
  display: flex;
  align-items: end;
  position: absolute;
  top: 0;
  z-index: 1;
  left: 5.5px;
}

.detail-txt {
  width: 100%;
  line-height: 25px;
  font-size: 18px;
  font-weight: 500;
  padding-right: 4px;
}

@media screen and(max-width: 375px) {
  .detail-txt {
    font-size: 16px;
  }
  .icon__wrapper {
    svg {
      margin-top: 3px;
    }
  }
}
.icon__wrapper {
  height: 25px;
  display: flex;
  align-items: center;
  img {
    // width: 13px;
    width: 7px;
    height: 85%;
    object-fit: contain;
    padding-bottom: 2px;
  }
}

.cym-total-info__box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px 40px;
}

.cym-emblem__wrapper {
  position: relative;
}

.total-score__block {
  position: absolute;
  width: 100%;
  top: 48%;
  left: 55%;
  transform: translate(-50%, -50%);
}

.cym-chart__wrapper {
  position: relative;
  width: 100%;
}

.cym_score {
  font-family: GilroyBold !important;
  // font-weight: 800;
  font-size: 40px;
  line-height: 40px;
  text-align: center;
  color: #000000;
}

.cym_score_none {
  // font-family: Noto Sans KR;
  font-style: normal;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 44px;
  text-align: center;
  letter-spacing: -0.05em;
  color: #a7a7a7;
}

.user-name__block {
  // font-family: Noto Sans KR;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  text-align: right;
  letter-spacing: -0.05em;
  margin-right: 4px;
}

.isKo {
  font-family: Noto Sans KR !important;
}

.isEn {
  font-family: GilroyBold !important;
  font-size: 1.25rem;
}
</style>
