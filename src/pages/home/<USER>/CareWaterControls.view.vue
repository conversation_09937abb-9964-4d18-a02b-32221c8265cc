<template>
  <div>
    <v-card class="card elevation-0 clear:both;" outlined color="transparent">
      <!-- care link -->
      <div class="home-headline1">
        <router-link :to="{ path: '/home/<USER>/water' }">
          <span :class="isKo ? 'water-title' : 'water-title_en'">{{
            $t("water_title")
          }}</span>
          <span class="home-highlight1--en">
            Care <img src="@/assets/images/right-arrow.png"
          /></span>
        </router-link>
      </div>

      <!-- care controller-indicator -->
      <div class="controller-wrapper">
        <!-- ✋🏻 care controller-indicator : left -->
        <div class="controller-indicator-wrapper">
          <span class="home_figure">
            <input
              v-model="copyWater"
              placeholder="0"
              min="10000"
              class="home-text-field"
              name="water"
              type="number"
              inputmode="numeric"
            />
            <!-- <span class="home-text-field">{{ water }}</span> -->
          </span>
          <span class="home_measure">ml</span>
        </div>

        <!-- 🤚🏻 care controller-indicator : right -->
        <div class="container">
          <div class="water-controller">
            <div class="water-subtract__btn">
              <img
                @click="subtractWater"
                width="12"
                src="@/assets/images/minus_circle.png"
              />
            </div>
            <div class="glass-mask"></div>

            <div class="water-plus__btn">
              <img
                @click="addWater"
                width="12"
                src="@/assets/images/plus_circle.png"
              />
            </div>
          </div>
          <div class="water-glass__img">
            <img
              src="@/assets/images_assets/images/water_mask.png"
              alt="glass_mask"
            />
          </div>
          <div class="save-btn" v-if="showSaveBtn">
            <div class="save-btn__ic" @click="saveBtnHandler">
              <img src="@/assets/images_assets/icons/care-save-btn-ic.png" />
            </div>
          </div>
          <div class="circle">
            <div class="wave" ref="water"></div>
          </div>
        </div>
      </div>
    </v-card>
    <div class="snackbar">
      <v-snackbar v-model="snackbar" timeout="2000">{{
        succesContent
      }}</v-snackbar>
      <v-snackbar v-model="overTen" timeout="2000">{{
        failContent
      }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import API from "@/api/care/index.js";

export default {
  props: {
    water: Number,
  },

  data() {
    return {
      page: "water",
      path: "home",
      waterImage: require("@/assets/images/care_ani/water_0.png"),
      copyWater: 0,
      plusBtnCount: 0,
      minusBtnCount: 0,
      showSaveBtn: false,
      waveTopPosition: 80,
      succesContent: this.$i18n.t("save_success"),
      failContent: "연속 10회 입력 불가능",
      snackbar: false,
      overTen: false,
      isKo: false,
      count: 0,
    };
  },

  computed: {
    ToString() {
      return this.copyWater.toString();
    },
  },
  watch: {
    water(newVal) {
      this.$nextTick(() => {
        this.copyWater = newVal;
      });
    },
    copyWater(newVal) {
      if (newVal < 0) {
        this.copyWater = 0;
      } else {
        // console.log(newVal);
        this.$nextTick(() => {
          // this.copyWater = newVal;
          // this.$emit("changedWater", newVal);
          this.water !== newVal
            ? (this.showSaveBtn = true)
            : (this.showSaveBtn = false);
        });
      }
    },
    plusBtnCount(newVal) {
      // console.log("plusbtn", newVal);
      // console.log("wave top position", this.waveTopPosition);
      // let value;
      // if (newVal === 1) {
      //   value = 20;
      // } else {
      //   value = 90 / 20;
      // }
      // console.log("chk", this.waveTopPosition, value);
      // this.waveTopPosition = this.waveTopPosition -= value;
      // const wavePosition = this.waveTopPosition <= 0 ? 0 : this.waveTopPosition;
      // this.$refs.water.style.top = `${wavePosition}px`;
    },
    minusBtnCount(newVal) {
      // console.log("minusBtn", newVal);
      // console.log("top position", this.topPosition, "wave top position", this.waveTopPosition);
      // let value;
      // if (this.count >= 0) {
      // if (newVal === 1) {
      //   value = 20;
      // } else {
      //   value = 90 / 20;
      // }
      // }
      // console.log("chk", this.waveTopPosition, value);
      // this.waveTopPosition = this.waveTopPosition += value;
      // const wavePosition = this.waveTopPosition <= 0 ? 0 : this.waveTopPosition;
      // this.$refs.water.style.top = `${wavePosition}px`;
    },
  },
  methods: {
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null
          ? subjects[selectedId].id
          : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async saveBtn() {
      // console.log(this.page, this.copyWater);
      try {
        const subjectId = this.getSubjectId();
        const careValue = {
          water: this.count * 100,
        };
        const response = await API.FetchUpdateCareData(
          subjectId,
          this.page,
          careValue
        );
        // console.log(response);
        if (response.status === 201) {
          this.$emit("reloadHome", true);
          this.count = 0;
          this.snackbar = true;
        }
      } catch (e) {
        console.log(e);
      }
    },
    // async getHistoryData(count) {
    //   this.loaded = false;
    //   try {
    //     const response = await GetCareData(count, "water");
    //     console.log(response);
    //     if (response.data) {
    //       this.loaded = true;

    //       const careData = DataProcessing.GET_WATER_CARE_DATA(response.data, count);
    //       console.log(careData);
    //       this.graphData = careData.graphData;
    //       this.historyData = careData.historyData;
    //       this.weekDayData = careData.dayAvgData;
    //       if (count === "d") {
    //         this.water = 2000;
    //         this.volume = careData.avgScore;
    //         this.dateRange = careData.dateRange.startDate;
    //       } else {
    //         this.water = Number(careData.avgScore);
    //         this.dateRange = `${careData.dateRange.startDate} ~ ${careData.dateRange.endDate}`;
    //       }
    //     }
    //   } catch (e) {
    //     console.log(e);
    //   }
    // },
    addWater() {
      if (this.water !== this.copyWater) {
        this.showSaveBtn = true;
      }
      this.plusBtnCount = this.plusBtnCount += 1;
      if (this.count >= 0 && this.count < 10) {
        this.copyWater = this.copyWater + 100;
        this.count = this.count + 1;
        this.waveTopPosition = this.computedTopPosition(this.copyWater);
        this.$refs.water.style.top = `${this.waveTopPosition}px`;
      } else {
        this.showSaveBtn = false;
      }
      // console.log("count", this.count);
    },
    subtractWater() {
      if (this.water !== this.copyWater) {
        this.showSaveBtn = true;
      }
      this.minusBtnCount = this.minusBtnCount += 1;
      if (this.count > 0) {
        this.copyWater = this.copyWater - 100;
        this.count = this.count - 1;
        this.waveTopPosition = this.computedTopPosition(this.copyWater);
        this.$refs.water.style.top = `${this.waveTopPosition}px`;
      }
      // console.log("count", this.count);
    },
    saveBtnHandler() {
      this.saveBtn();
      this.showSaveBtn = false;
    },
    computedTopPosition(val) {
      const defaultPosition = 80;
      const count = val / 100;
      const decreseVal = count * 3;
      const topPosition = defaultPosition - decreseVal;
      // console.log(val, topPosition);
      if (topPosition < 0) {
        return 0;
      } else if (Number(val) === 100) {
        return 76;
      } else {
        return topPosition;
      }
    },
  },
  mounted() {
    this.copyWater = this.water;
    // console.log(this.water);
    this.waveTopPosition = this.computedTopPosition(this.water);
    this.$refs.water.style.top = `${this.waveTopPosition}px`;
    this.isKo = this.$i18n.locale.includes("ko");
  },
};
</script>

<style lang="scss" scoped>
.card {
  margin-top: 90px;
}

.controller-wrapper {
  display: flex;
  justify-content: space-between;
  height: 95px;
}

.controller-indicator-wrapper {
  width: 45%;
  height: 100%;
  text-align: right;
  position: relative;
  align-items: flex-end;
  padding: 0 20px 2px 0;
  justify-content: flex-end;
  display: flex;
}

.flex-end {
  align-items: flex-end;
}

.home-headline1 {
  margin-left: 29px;
  // font-weight: bold;
  font-size: 16px;
  line-height: 23px;
  text-align: left;
  color: #646464;
}

.water-title {
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
}

.water-title_en {
  font-size: 21.5px;
  line-height: 22px;
  font-family: GilroyBold;
}

.home-headline1 a {
  text-decoration: none !important;
  color: #646464 !important;
}

.border-rounded {
  border-radius: 20px 20px 0 0 !important;
}

.home-highlight1--en {
  text-decoration: none;
  font-family: GilroyBold;
  // font-weight: 800;
  font-size: 21.5px;
  line-height: 22px;
  img {
    width: 7px;
  }
}

.home-text-field {
  width: 95px;
  font-size: 38px;
  text-align: right;
  caret-color: #41d8e6;
  margin-right: 7px !important;
  font-family: GilroyMedium;
  outline: none;
}

.home_figure {
  font-family: GilroyMedium;
  font-size: 36px;
  line-height: 42px;
  text-align: right;
  position: relative;
}

.home_measure {
  font-family: GilroyMedium;
  font-size: 20px;
  line-height: 26px;
  text-align: center;
  color: #000000;
}

.container {
  width: 60%;
  max-width: 172px;
  position: relative;
  margin-right: 20px;
}

.water-controller {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
  position: relative;
}

.water-glass__img {
  position: absolute;
  z-index: 1;
  overflow: hidden;
  left: 50%;
  top: 0;
  // padding-top: 10px;
  transform: translateX(-50%);
  img {
    width: 90px;
  }
}

.water-subtract__btn {
  position: absolute;
  z-index: 999;
  left: 0px;
  top: 52px;
  img {
    width: 36px;
  }
}

.water-plus__btn {
  position: absolute;
  z-index: 99;
  top: 52px;
  right: 0px;
  img {
    width: 36px;
  }
}

.circle {
  background-color: #f8f8f8;
  position: absolute;
  width: 90px;
  max-width: 100%;
  height: 80px;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
  transition: all 1s;
}

.wave {
  position: absolute;
  width: 100px;
  height: 100px;
  background-color: #41d8e6;
  border-radius: 40%;

  left: 50%;
  transform: translateX(-50%);
  animation: wave 7s infinite linear;
  transition: all 1s;
  opacity: 0.7;
}

.value-txt {
  position: absolute;
  font-family: GilroyBold;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s;
  font-size: 25px;
  font-weight: bold;
  color: #41d8e6;
  z-index: 99;
}

@keyframes wave {
  0% {
    transform: translate(-50%) rotate(-90deg);
  }

  100% {
    transform: translate(-50%) rotate(360deg);
  }
}

.save-btn {
  position: absolute;
  z-index: 99;
  top: 65%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.save-btn__ic {
  // z-index: 999;
  img {
    width: 26px;
  }
}

// snackbar custom
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
</style>
