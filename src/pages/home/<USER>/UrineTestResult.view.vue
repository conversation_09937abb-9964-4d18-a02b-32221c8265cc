<template>
  <div>
    <div class="cym-main-score-box">
      <!-- <router-link to="/home/<USER>">
        <div @click="sessionStorageHandler" class="cym-main-score-title">
          <span>{{ $t("exam_result_title") }}</span>
          <img src="@/assets/images_assets/icons/arrow-bold-ic.png" alt="link" />
        </div>
      </router-link> -->
      <!-- 🔥 date==================== -->
      <div class="urineTest-table-row date">
        <div class="col-item date-col">
          <div class="skip-btn__wrapper">
            <!-- <v-icon :size="25" @click="prevHandler">$prev_icon</v-icon> -->
          </div>
        </div>
        <div
          class="col-item date-col"
          v-for="(item, idx) in dateArr"
          :key="idx"
          :class="
            idx === activeDropletIdx && !nonTestResult ? 'date-active' : ''
          "
        >
          {{ item.date }}
        </div>
        <div class="col-item btn__wrapper">
          <img :src="prevIcon" alt="link" @click="prevHandler" />
          <img :src="nextIcon" alt="link" @click="nextHandler" />
          <!-- <v-icon :size="25" @click="nextHandler">$right_arrow</v-icon> -->
        </div>
      </div>
      <div class="result-table__wrapper">
        <!-- 🔥 잠혈 =================== -->
        <TableRowItem
          type="blood"
          :urineTestItems="blood"
          :urineTestState="recentBloodState"
          :urineTestCompleted="showCompleteModal"
        />
        <!-- 🔥 Ph ===================== -->
        <TableRowItem
          type="ph"
          :urineTestItems="ph"
          :urineTestState="recentPhState"
          :urineTestCompleted="showCompleteModal"
        />
        <!-- 🔥 단백질 ================== -->
        <TableRowItem
          type="protein"
          :urineTestItems="protein"
          :urineTestState="recentProteinState"
          :urineTestCompleted="showCompleteModal"
        />
        <!-- 🔥 포도당 ================== -->
        <TableRowItem
          type="glucose"
          :urineTestItems="glucose"
          :urineTestState="recentGlucoseState"
          :urineTestCompleted="showCompleteModal"
        />
        <!-- 🔥 ketone ================= -->
        <TableRowItem
          type="ketone"
          :urineTestItems="ketone"
          :urineTestState="recentKetoneState"
          :urineTestCompleted="showCompleteModal"
        />
      </div>
    </div>
  </div>
</template>
<script>
import TableRowItem from "@/components/Home/UrineTestRowItem.vue";
import {
  bloodScoring,
  proteinAndGlucoseScoring,
  pHScoring,
  ketoneScoring,
} from "@/utils/ResultTableView.utils.js";

import { mapGetters } from "vuex";
import dataProcessing from "@/assets/data/manufacturing/cym.js";
import { createFiveLengthArray } from "@/utils/ResultTableView.utils.js";

export default {
  props: {
    historyData: Array,
    totalCount: Number,
  },
  components: {
    TableRowItem,
  },
  data() {
    return {
      nonTestResult: true,
      blood: [],
      glucose: [],
      protein: [],
      ph: [],
      ketone: [],
      dateArr: [],
      page: 1,
      totalPage: 1,
      isPrevData: false,
      isNextData: false,
    };
  },

  watch: {
    historyData(newVal) {
      // console.log(newVal);
      this.getUrineTestResults();
    },
    page(newVal) {
      // console.log(newVal);
      this.isNextData = newVal <= 1 ? false : true;
      this.isPrevData = this.totalPage > newVal ? true : false;
    },
  },
  computed: {
    prevIcon() {
      return this.isPrevData
        ? require("@/assets/images_assets/icons/prev_chevron.png")
        : require("@/assets/images_assets/icons/prev_btn.png");
    },
    nextIcon() {
      return this.isNextData
        ? require("@/assets/images_assets/icons/next_chevron.png")
        : require("@/assets/images_assets/icons/next_btn.png");
    },
    ...mapGetters(["activeDropletIdx", "showCompleteModal"]),
    setTestDateArray() {
      const datelist = this.blood.map((item) => {
        // console.log(item.createdAt);
        const YYMMDD = item.createdAt.split("T")[0].split("-");
        const MM = YYMMDD[1];
        const DD = YYMMDD[2];
        return `${MM}.${DD}`;
      });
      return datelist;
    },

    recentBloodState() {
      if (!this.blood[this.activeDropletIdx]) {
        return;
      }
      return bloodScoring(this.blood[this.activeDropletIdx].level);
    },
    recentProteinState() {
      if (!this.protein[this.activeDropletIdx]) {
        return;
      }
      return proteinAndGlucoseScoring(
        this.protein[this.activeDropletIdx].level
      );
    },
    recentGlucoseState() {
      if (!this.glucose[this.activeDropletIdx]) {
        return;
      }
      return proteinAndGlucoseScoring(
        this.glucose[this.activeDropletIdx].level
      );
    },
    recentPhState() {
      if (!this.ph[this.activeDropletIdx]) {
        return;
      }
      return pHScoring(this.ph[this.activeDropletIdx].level);
    },
    recentKetoneState() {
      if (!this.ketone[this.activeDropletIdx]) {
        return;
      }
      return ketoneScoring(this.ketone[this.activeDropletIdx].level);
    },
  },
  methods: {
    setPrevData() {
      this.isPrevData = Math.floor(this.totalCount / 5) > 0 ? true : false;
    },
    prevHandler() {
      console.log(this.page, this.totalPage);
      if (this.page > 0 && this.totalPage > this.page) {
        this.page += 1;
        this.$emit("pageHandler", this.page);
      }
    },
    nextHandler() {
      console.log(this.page, this.totalPage);
      if (this.page > 1) {
        this.page -= 1;
        this.$emit("pageHandler", this.page);
      }
    },
    sessionStorageHandler() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedBookmarkDataPage");
    },
    getUrineTestResults() {
      const urineTestResult = dataProcessing.FETCH_CYM_DATA(this.historyData);
      // console.log("urineTestResult:", urineTestResult);
      // console.log(urineTestResult.blood);
      this.blood = urineTestResult.blood;
      this.glucose = urineTestResult.glucose;
      this.protein = urineTestResult.protein;
      this.ph = urineTestResult.ph;
      this.ketone = urineTestResult.ketone;
      this.dateArr = createFiveLengthArray("blood", this.blood);
      // console.log("urineTestResult arr:", this.dateArr);
      this.haveUrineTestReulst();
    },
    haveUrineTestReulst() {
      if (this.blood.length > 0) {
        this.nonTestResult = false;
      } else {
        this.nonTestResult = true;
      }
    },
  },
  mounted() {
    this.totalPage = Math.floor(this.totalCount / 5);
    console.log("dfdcsercs" + this.totalPage, this.page);
    if (this.totalPage >= 1) this.isPrevData = true;
    this.getUrineTestResults();
  },
};
</script>

<style lang="scss" scoped>
.urineTest-table-row {
  display: flex;
  align-items: center;
}

.col-item {
  font-family: GilroyMedium !important;
  flex: 1;
  text-align: center;
  padding: 0.313rem 0.5rem 0 0.313rem;
  display: flex;
  justify-content: center;
  img {
    width: 12px;
  }
}

.btn__wrapper {
  width: 100%;
  display: flex;
  gap: 25px;
  padding: 0 0.2rem 0 0 !important;
  margin-left: 0.3rem;
}

.cym-main-score-box {
  padding: 25px 20px;
}

.skip-btn__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.cym-main-score-title {
  font-size: 18px;
  font-weight: 700;
  text-align: left;
  color: #000;
  margin-bottom: 8px;
  img {
    margin-left: 3px;
    width: 8px;
  }
}

.date {
  font-size: 12px;
  color: #ededed;
}

.date-active {
  font-size: 15px;
  font-weight: 500;
  color: #646464;
  letter-spacing: -0.05em;
  // padding-right: 1.2rem;
}

.result-table__wrapper {
  margin-top: 15px;
}
</style>
