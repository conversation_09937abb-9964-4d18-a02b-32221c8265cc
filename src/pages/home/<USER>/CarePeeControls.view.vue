<template>
  <div>
    <v-card class="card pb-120 elevation-0" outlined color="transparent">
      <!-- care link -->
      <div class="home-headline1">
        <router-link :to="{ path: '/home/<USER>/pee' }">
          <span :class="isKo ? 'pee-title' : 'pee-title_en'">{{
            $t("pee_title")
          }}</span>
          <span class="home-highlight1--en">
            Care <img src="@/assets/images/right-arrow.png"
          /></span>
        </router-link>
      </div>

      <!-- care controller-indicator -->
      <div class="d-flex justify-space-between controller-wrapper">
        <!-- ✋🏻 care controller-indicator : left -->
        <div class="controller-indicator-wrapper">
          <span class="home_figure">
            <!-- <span class="home-text-field">{{ pee }}</span> -->
            <input
              v-model="copyPee"
              placeholder="0"
              class="home-text-field"
              name="pee"
              type="number"
              inputmode="numeric"
            />
          </span>
          <span class="uri_measure" v-if="isKo">{{ $t("times") }}</span>
          <span class="uri_measure" v-else>X</span>
        </div>

        <!-- 🤚🏻 care controller-indicator : right -->
        <div class="container">
          <div class="water-controller">
            <div class="water-subtract__btn">
              <img
                @click="subtractPee"
                src="@/assets/images/minus_circle.png"
              />
            </div>
            <div class="water-plus__btn">
              <img @click="addPee" src="@/assets/images/plus_circle.png" />
            </div>
          </div>
          <div class="waterdrop__img">
            <img
              src="@/assets/images_assets/images/pee_mask.png"
              alt="glass_mask"
            />
          </div>
          <div class="save-btn" v-if="showSaveBtn">
            <div class="save-btn__ic" @click="saveBtnHandler">
              <img src="@/assets/images_assets/icons/care-save-btn-ic.png" />
            </div>
          </div>
          <div class="main-page__circle">
            <div class="wave" ref="pee"></div>
          </div>
        </div>
      </div>
    </v-card>
    <div class="snackbar">
      <v-snackbar v-model="snackbar" timeout="2000">{{
        successContent
      }}</v-snackbar>
      <v-snackbar v-model="overOnce" timeout="2000" color="#EE0000">{{
        failContent
      }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import API from "@/api/care/index.js";

export default {
  props: {
    pee: Number,
  },
  data() {
    return {
      peeImage: require("@/assets/images/care_ani/urine_0.png"),
      copyPee: this.pee,
      showSaveBtn: false,
      plusBtnCount: 0,
      waveTopPosition: 70,
      successContent: this.$i18n.t("save_success"),
      failContent: this.$i18n.t("pee_error"),
      snackbar: false,
      overOnce: false,
      isKo: false,
      count: 0,
      totalValue: 8,
    };
  },
  computed: {
    setPeeValue() {
      return this.computedTopPosition(this.pee);
    },
  },
  watch: {
    // pee(newVal) {
    //   this.$nextTick(() => {
    //     this.copyPee = newVal;
    //   });
    // },
    // copyPee(newVal, oldVal) {
    //   if (newVal < 0) {
    //     console.log("0이하의 숫자가 감지되었습니다.");
    //     this.copyPee = 0;
    //   }
    // },
    plusBtnCount(newVal) {
      // let value;
      // if (newVal === 1) {
      //   value = 20;
      // } else {
      //   value = 90 / 10;
      // }
      // this.waveTopPosition = this.waveTopPosition -= value;
      // const wavePosition = this.waveTopPosition <= 0 ? 0 : this.waveTopPosition;
      // this.$refs.pee.style.top = `${wavePosition}px`;
    },
    count(newVal) {
      // console.log(newVal);
      newVal === 0 ? (this.showSaveBtn = false) : (this.showSaveBtn = true);
    },
    waveTopPosition(val) {
      // console.log(val);
    },
  },
  methods: {
    addPee() {
      if (this.count === 0) {
        this.count = this.count += 1;
        this.showSaveBtn = true;
        this.plusBtnCount = this.plusBtnCount += 1;
        this.copyPee = this.copyPee + 1;
        this.waveTopPosition = this.computedTopPosition(this.copyPee);
        this.$refs.pee.style.top = `${this.waveTopPosition}px`;
      } else if (this.count > 0) {
        this.overOnce = true;
      } else {
        this.overOnce = true;
        this.showSaveBtn = false;
      }
    },
    subtractPee() {
      if (this.copyPee < 0) {
        // console.log("0이하의 숫자가 감지되었습니다.");
        this.copyPee = 0;
      }
      if (this.count === 1) {
        this.showSaveBtn = true;
        this.count = this.count -= 1;
        this.plusBtnCount = this.plusBtnCount -= 1;
        this.copyPee = this.copyPee - 1;
        this.waveTopPosition = this.computedTopPosition(this.copyPee);
        this.$refs.pee.style.top = `${this.waveTopPosition}px`;
      } else {
        this.showSaveBtn = false;
      }
    },
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null
          ? subjects[selectedId].id
          : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async saveBtnHandler() {
      try {
        const subjectId = this.getSubjectId();
        const careValue = {
          value: 1,
        };
        const response = await API.FetchUpdateCareData(
          subjectId,
          "urine",
          careValue
        );
        // console.log(response);
        if (response.status === 201) {
          this.count = 0;
          this.$emit("reloadHome", true);
          this.showSaveBtn = false;
          this.snackbar = true;
        }
      } catch (e) {
        console.log(e.response.data);
      }
    },
    computedTopPosition(val) {
      //4. 2000 / 100 => 8번을 클릭해야 물이 꽉찬다.
      const totalAmount = this.totalValue;

      //5. 그럼 어느정도 감소해야하는지? 200 / 8 =? 12.5
      const increaseValue = 100 / 8;

      //6. 그럼 어느정도 감소해야하는지? 18
      const currentVolume = val;
      const calculateVolume = currentVolume * increaseValue;
      const topPosition = 90 - calculateVolume;

      if (topPosition < 0) {
        return 0;
      } else if (val === 1) {
        return 70;
      } else {
        return topPosition;
      }
    },
  },
  mounted() {
    this.isKo = this.$i18n.locale.includes("ko");
    this.topPosition = this.computedTopPosition(this.copyPee);
    this.$refs.pee.style.top = `${this.topPosition}px`;
  },
};
</script>

<style lang="scss" scoped>
// .controller-wrapper {
//   padding: 0 25px 0 0;
// }

.card {
  margin-top: 70px;
}

.controller-indicator-wrapper {
  width: 45%;
  height: 100%;
  text-align: right;
  position: relative;
  align-items: flex-end;
  padding: 0 18.5px 8px 0;
  justify-content: flex-end;
  display: flex;
}

.flex-end {
  align-items: flex-end;
}

.controller-wrapper {
  display: flex;
  justify-content: space-between;
  height: 95px;
}

.home-headline1 {
  margin-left: 29px;
  // font-weight: bold;
  font-size: 16px;
  line-height: 23px;
  text-align: left;
  color: #646464;
}

.pee-title {
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
}

.pee-title_en {
  font-size: 21.5px;
  line-height: 22px;
  font-family: GilroyBold;
}

.home-headline1 a {
  text-decoration: none !important;
  color: #646464 !important;
}

.border-rounded {
  border-radius: 20px 20px 0 0 !important;
}

.home-highlight1--en {
  text-decoration: none;
  font-family: GilroyBold;
  // font-weight: 800;
  font-size: 21.5px;
  line-height: 24px;
  img {
    width: 7px;
  }
}

.home_figure {
  font-family: GilroyMedium;
  font-size: 36px;
  line-height: 42px;
  text-align: right;
  position: relative;
  /* height: 57px; */
}

.home_measure {
  font-family: GilroyMedium;
  font-size: 20px;
  line-height: 26px;
  text-align: center;
  color: #000000;
}

.container {
  width: 60%;
  max-width: 172px;
  position: relative;
  margin-right: 20px;
}

.water-controller {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
  position: relative;
}

.waterdrop__img {
  position: absolute;
  z-index: 1;
  overflow: hidden;
  left: 50%;
  top: 0px;
  // padding-top: 10px;
  transform: translateX(-50%);
  img {
    height: 91px;
    object-fit: contain;
  }
}

.water-subtract__btn {
  position: absolute;
  z-index: 999;
  left: 0px;
  top: 50px;
  img {
    width: 36px;
  }
}

.water-plus__btn {
  position: absolute;
  z-index: 99;
  top: 50px;
  right: 0px;
  img {
    width: 36px;
  }
}

.main-page__circle {
  background-color: #f8f8f8;
  position: absolute;
  width: 80px;
  max-width: 100%;
  height: 80px;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  overflow: hidden;
  transition: all 1s;
}

.wave {
  position: absolute;
  width: 100px;
  height: 100px;
  background-color: #ffcc00;
  border-radius: 40%;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  animation: wave 7s infinite linear;
  transition: all 1s;
  opacity: 0.7;
}

.value-txt {
  position: absolute;
  font-family: GilroyBold;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s;
  font-size: 25px;
  font-weight: bold;
  color: #41d8e6;
  z-index: 99;
}

.uri_measure {
  min-width: 30px;
  text-align: left;
  font-size: 24px;
}

@keyframes wave {
  0% {
    transform: translate(-50%) rotate(-90deg);
  }

  100% {
    transform: translate(-50%) rotate(360deg);
  }
}
.save-btn {
  position: absolute;
  z-index: 99;
  top: 65%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.save-btn__ic {
  img {
    width: 26px;
  }
}

.home-text-field {
  width: 95px;
  font-size: 38px;
  text-align: right;
  caret-color: #41d8e6;
  margin-right: 12px !important;
  font-family: GilroyMedium;
  outline: none;
}

// snackbar custom
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
</style>
