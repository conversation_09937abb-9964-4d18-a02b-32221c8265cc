<template>
  <div class="guide-back">
    <!-- guide tutorial header -->
    <div :class="{ 'ios-area-top': isIos === true }"></div>
    <div class="guide-close-btn-area">
      <div class="close-btn-area" @click="closeTutorial">
        <v-icon>$x_circle_wht</v-icon>
      </div>
    </div>

    <!-- guide tutorial body -->
    <v-carousel
      v-model="carouselIndex"
      :cycle="false"
      height="100%"
      hide-delimiter-background
      class="control"
      :show-arrows="false"
      :continuous="false"
    >
      <!-- =================================================================== -->
      <!-- 🚨 First Tutorial -->
      <v-carousel-item class="first-tutor" key="1">
        <div class="guide_first_img">
          <img src="@/assets/images/guide_img/care_yourmoment.png" />
        </div>
        <div class="guide-first-text">
          <h2 class="guide-text-title">{{ $t("first_tutorial_title") }}</h2>
          <h3 class="guide-text-content mx-auto">
            {{ $t("first_tutorial_content") }}
          </h3>
        </div>
      </v-carousel-item>

      <!-- =================================================================== -->
      <!-- 🚨 Second Tutorial -->
      <v-carousel-item class="second-tutor" key="2">
        <div class="carousel-item-flex">
          <div class="guide-second-text">
            <h2 class="guide-text-title">{{ $t("second_tutorial_title") }}</h2>
            <h3 class="guide-text-content mx-auto">
              {{ $t("second_tutorial_content") }}
            </h3>
          </div>
          <div class="guide_second_img">
            <img src="@/assets/images/guide_img/exam_result.png" alt="" />
          </div>
        </div>
      </v-carousel-item>

      <!-- =================================================================== -->
      <!-- 🚨 Third Tutorial -->
      <v-carousel-item key="3" class="third-tutor">
        <div class="carousel-item-flex">
          <div class="guide-second-text">
            <h2 class="guide-text-title">{{ $t("third_tutorial_title") }}</h2>
            <h3 class="guide-text-content mx-auto">
              {{ $t("third_tutorial_content") }}
            </h3>
          </div>
          <div class="guide_gif">
            <img src="@/assets/images/guide_img/dropdown.gif" />
          </div>
          <div class="guide_third_img">
            <div class="guide_care_icon">
              <img src="@/assets/images/guide_img/guide_weight.png" />
            </div>
            <div class="guide_care_icon">
              <img src="@/assets/images/guide_img/guide_water.png" />
            </div>
            <div class="guide_care_icon">
              <img src="@/assets/images/guide_img/guide_pee.png" />
            </div>
          </div>
        </div>
      </v-carousel-item>
    </v-carousel>
  </div>
</template>

<script>
export default {
  name: "Guide",
  props: {
    guidePage: String,
  },
  data: () => ({
    carouselIndex: 0,
  }),
  methods: {
    closeTutorial() {
      if (this.guidePage === "guide") {
        this.$router.push({ path: "/mypage/guide" });
      }
      this.$emit("showTutorial", false);
    },
  },
};
</script>

<style lang="scss">
</style>