<template>
  <div>
    <header class="result_header">
      <span class="variable_date">{{ urineDate }}</span>
      <div class="col-item btn__wrapper">
        <img
          :src="prevIcon"
          alt="prev_button"
          loading="lazy"
          @click="handlePrevDateClick"
        />
        <img
          :src="nextIcon"
          alt="next_button"
          loading="lazy"
          @click="handleNextDateClick"
        />
      </div>
    </header>
    <section class="urine-section">
      <UrineTestRowItem
        v-for="item in formatHistoryData"
        :key="item.type"
        :type="item.type"
        :level="item.level"
        :urineImage="item.urineImage"
        :isComplete="isComplete()"
      />
    </section>
  </div>
</template>

<script>
import UrineTestRowItem from "@/components/Home/UrineTestRowItem10.vue";

export default {
  props: {
    historyData: Array,
    count: Number,
  },
  components: {
    UrineTestRowItem,
  },
  data() {
    return {
      isPrevData: false,
      isNextData: true,
      urineDate: "",
      currentDate: new Date(),
      formatHistoryData: [],
      urineTypes: [
        "blood",
        "leukocytes",
        "ph",
        "bilirubin",
        "protein",
        "urobilinogen",
        "glucose",
        "nitrite",
        "ketone",
        "sg",
      ],
      page: 1,
    };
  },
  watch: {
    page(value) {
      this.isNextData = value <= 1 ? false : true;
    },
    historyData: {
      immediate: true,
      handler(newData) {
        const historyDataLength = newData.length;

        if (historyDataLength) {
          this.formatHistoryData = this.formatData(newData);
          return;
        }

        const result = this.urineTypes.map((type) => {
          return {
            type: type,
            level: null,
            urineImage: this.formatImage(type, null),
          };
        });

        this.formatHistoryData = result;
      },
    },
    "$store.state.activeDropletIdx"(newValue) {
      // Vuex 상태 변경 시 formatData 실행
      this.formatHistoryData = this.formatData(this.historyData);
    },
  },
  computed: {
    prevIcon() {
      return this.isPrevData
        ? require("@/assets/images_assets/icons/prev_chevron.png")
        : require("@/assets/images_assets/icons/prev_btn.png");
    },
    nextIcon() {
      return this.isNextData
        ? require("@/assets/images_assets/icons/next_chevron.png")
        : require("@/assets/images_assets/icons/next_btn.png");
    },
    setUrineDate() {
      return this.formatDate(new Date());
    },
  },
  methods: {
    isComplete() {
      return this.$store.state.showCompleteModal;
    },
    getActiveIndex() {
      return this.$store.state.activeDropletIdx;
    },
    formatDate(date = new Date()) {
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");

      if (isNaN(month) || isNaN(day)) {
        return "";
      }

      return `${month}.${day}`;
    },

    getLevel(type, level) {
      const scores = ["good", "warning", "caution", "danger"];

      if (level === null) {
        return "empty";
      }

      switch (type) {
        case "blood":
          return scores[level - 1];
        case "ketone": {
          if (level === 3) {
            return "warning";
          }

          if (level >= 4) {
            return "danger";
          }

          return scores[level - 1];
        }
        case "protein": {
          if (level === 2) {
            return "good";
          } else if (level > 4) {
            return "danger";
          }

          return scores[level - 1];
        }
        case "glucose": {
          if (level === 2) {
            return "good";
          } else if (level >= 5) {
            return "danger";
          }

          return scores[level - 1];
        }
        case "ph": {
          if (level === 5) {
            return "warning";
          }
          return "good";
        }
        case "leukocytes":
          return scores[level - 1];
        case "bilirubin":
          return scores[level - 1];
        case "urobilinogen": {
          if (level <= 2) {
            return "good";
          }

          return scores[level - 2];
        }
        case "nitrite":
          if (level === 1) {
            return "good";
          }

          return "danger";
        case "sg":
          if (level <= 2) {
            return "good";
          }
          if (level === 3) {
            return "warning";
          }

          if (level === 4 || level === 5) {
            return "caution";
          }

          return "danger";
        default:
          return "empty";
      }
    },

    formatImage(type, level) {
      const imageByLevel = {
        good: require("@/assets/images/droplets/result_normal.png"),
        warning: require("@/assets/images/droplets/result_caution.png"),
        caution: require("@/assets/images/droplets/result_warning.png"),
        danger: require("@/assets/images/droplets/result_danger.png"),
        empty: require("@/assets/images/droplets/result_blank.png"),
      };
      const imageKey = this.getLevel(type, level);

      return (
        imageByLevel[imageKey] ??
        require("@/assets/images/droplets/result_blank.png")
      );
    },

    formatData(data) {
      const activeIndex = this.getActiveIndex();
      console.log(activeIndex);
      const currentData = { ...data[activeIndex % 5] };

      console.log(currentData);

      const formatList = [];

      Object.freeze(currentData);

      for (const [key, value] of Object.entries(currentData)) {
        if (this.urineTypes.includes(key)) {
          formatList.push({
            type: key,
            level: value,
            urineImage: this.formatImage(key, value),
          });
        }
      }

      this.urineDate = this.formatDate(new Date(currentData?.createdAt));

      return formatList.sort(
        (a, b) =>
          this.urineTypes.indexOf(a.type) - this.urineTypes.indexOf(b.type)
      );
    },

    handlePrevDateClick() {
      if (!this.isPrevData) return;

      const activeIndex = this.getActiveIndex() + 1;

      if (activeIndex >= this.count) {
        this.isPrevData = false;
        return;
      }

      if (activeIndex % 5 === 0 && activeIndex !== 0) {
        this.page += 1;
        this.$emit("pageHandler", this.page);
      }
      this.$store.commit("GET_ACTIVE_DROPLET_IDX", activeIndex);

      this.isNextData = true;
    },

    handleNextDateClick() {
      if (!this.isNextData) return;

      const activeIndex = this.getActiveIndex() - 1;

      if (activeIndex < 0) {
        this.isNextData = false;
        return;
      }

      if (activeIndex < 0 && this.page > 1) {
        this.page -= 1;
        this.$emit("pageHandler", this.page);
      }
      this.$store.commit("GET_ACTIVE_DROPLET_IDX", activeIndex);

      this.isPrevData = true;
    },
  },
  created() {
    this.isPrevData = this.historyData.length > 1;
    this.isNextData = false;
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.col-item {
  font-family: GilroyMedium !important;
  padding: 0.313rem 0.5rem 0 0.313rem;
  display: flex;
  align-items: center;
}

.btn__wrapper {
  min-width: 55px;
  max-width: 120px;
  height: 20px;
  display: flex;
  justify-content: space-between;
  padding: 0 0.2rem 0 0 !important;
  margin: 0 1.125rem 0 0;

  > img {
    width: 10px;
    height: 16px;
    object-fit: contain; // 이미지 왜곡 방지
    display: block; // 이미지 주변 여백 제거
    -webkit-user-drag: none; // iOS에서 드래그 방지
  }
}

.result_header {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding-top: 25px;
}

.variable_date {
  color: #646464;
  font-family: GilroyMedium !important;
  font-size: 13px;
  font-weight: 500;
  line-height: 15.3px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  margin-top: 5px;
  margin-right: 1rem;
}

.urine-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  padding: 0 1.25rem 1.875rem 1.25rem;
  gap: 10px;
  margin-top: 0.406rem;
}

.urine-section > * {
  flex: 0 0 calc(50% - 10px); // 2개의 항목이 한 줄에 나타나도록 설정
  box-sizing: border-box;
  display: flex;
}
</style>
