<template>
  <div>
    <v-card class="elevation-0 pt-21" outlined color="transparent">
      <!-- care link -->
      <div class="home-headline1">
        <router-link :to="{ path: '/home/<USER>/weight' }">
          <span :class="isKo ? 'weight-title' : 'weight-title_en'">{{
            $t("weight_title")
          }}</span>
          <span class="home-highlight1--en">
            Care
            <img src="@/assets/images/right-arrow.png" />
          </span>
        </router-link>
      </div>

      <!-- care controller-indicator -->
      <div class="controller-wrapper">
        <div class="mt-38 controller-indicator-wrapper">
          <AlertSign :position="position" v-if="showCompleteAlert" />
          <span class="home_figure">
            <input
              v-model="copyWeight"
              placeholder="0"
              class="home-text-field"
              name="weight"
              type="number"
              inputmode="numeric"
            />
          </span>
          <span class="home_measure">Kg</span>
        </div>

        <!-- 🤚🏻 care controller-indicator : right -->
        <div class="weight-controller__wrapper">
          <div class="weight-controller__btn">
            <img
              @click="subtractWeight"
              src="@/assets/images/minus_circle.png"
            />
          </div>
          <div class="weight-controller__item float-weight-container">
            <img
              class="weight-img"
              :src="weightImage"
              @click="saveWeightData"
            />
            <div class="float-weight">
              <span class="weight-text">{{ copyWeight }}</span>
            </div>
          </div>
          <div class="weight-controller__btn">
            <img @click="addWeight" src="@/assets/images/plus_circle.png" />
          </div>
        </div>
      </div>
    </v-card>
    <div class="snackbar">
      <v-snackbar v-model="snackbar" timeout="2000">{{
        succesContent
      }}</v-snackbar>
      <v-snackbar v-model="overTen" timeout="2000">{{
        failContent
      }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import AlertSign from "@/components/Common/AlertSign.vue";
import API from "@/api/care/index.js";

export default {
  props: {
    weight: Number,
  },
  components: {
    AlertSign,
  },
  data() {
    return {
      copyWeight: this.weight,
      isSaveAvailable: false,
      showSaveBtn: false,
      showCompleteAlert: false,
      position: "right-bottom",
      succesContent: this.$i18n.t("save_success"),
      failContent: "연속 10회 입력 불가능",
      snackbar: false,
      overTen: false,
      isKo: false,
      count: 0,
    };
  },

  mounted() {
    this.isKo = this.$i18n.locale.includes("ko");
    // console.log(this.weight);
  },

  watch: {
    copyWeight(newVal) {
      // console.log(newVal);
      this.$nextTick(function() {
        this.copyWeight = newVal;
        this.weight !== newVal
          ? ((this.showSaveBtn = true), (this.isSaveAvailable = true))
          : ((this.showSaveBtn = false), (this.isSaveAvailable = false));
      });
    },
  },
  computed: {
    weightImage() {
      if (this.showSaveBtn) {
        return require("@/assets/images/main_scales_on.png");
      } else {
        return require("@/assets/images/main_scales.png");
      }
    },
  },
  methods: {
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null
          ? subjects[selectedId].id
          : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    /** save weight */
    async saveWeight() {
      try {
        const subjectId = this.getSubjectId();
        const value = { value: Number(this.copyWeight) };
        const response = await API.FetchUpdateCareData(
          subjectId,
          "weight",
          value
        );
        // console.log(response);
        if (response.status === 201) {
          this.$emit("reloadHome", true);
          this.snackbar = true;
        }
      } catch (e) {
        this.failContent = this.$i18n.t("save_fail");
        this.$emit("reloadHome", true);
        this.overTen = true;
        console.log(e);
      }
    },

    saveWeightData() {
      if (this.isSaveAvailable) {
        this.saveWeight();
        this.showSaveBtn = false;
      }
    },
    addWeight() {
      this.showSaveBtn = true;
      this.copyWeight = +(parseFloat(this.copyWeight) + 0.1).toFixed(1);
    },
    subtractWeight() {
      this.showSaveBtn = true;
      this.copyWeight = +(parseFloat(this.copyWeight) - 0.1).toFixed(1);
    },
  },
};
</script>

<style lang="scss" scoped>
.controller-wrapper {
  padding: 0 20px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.controller-indicator-wrapper {
  width: 45%;
  text-align: right;
  position: relative;
  align-items: flex-end;
  padding-right: 10px;
  justify-content: flex-end;
  display: flex;
}

.flex-end {
  align-items: flex-end;
}

.home-text-field {
  width: 95px;
  font-size: 38px;
  text-align: right;
  caret-color: #41d8e6;
  margin-right: 7px !important;
  font-family: GilroyMedium;
  outline: none;
}

.weight-controller__wrapper {
  display: flex;
  align-items: flex-end;
  // z-index: 9999;
}

.float-weight-container {
  position: relative;
}

.weight-text {
  display: inline-block;
  font-family: GilroyMedium;
  font-size: 14px;
  line-height: 14px;
  align-items: center;
  text-align: center;
  letter-spacing: 0.05em;
  color: #a7a7a7;
  position: absolute;
  left: 32px;
  top: 17px;
  width: 37px;
}

.weight-img {
  width: 90px;
  margin: 0 5px;
}

.home-headline1 {
  margin-left: 29px;
  text-align: left;
  color: #646464;
}

.weight-title {
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
}

.weight-title_en {
  font-size: 21.5px;
  line-height: 22px;
  font-family: GilroyBold;
}

.home-headline1 a {
  text-decoration: none !important;
  color: #646464 !important;
}

.border-rounded {
  border-radius: 20px 20px 0 0 !important;
}

.home-highlight1--en {
  text-decoration: none;
  font-family: GilroyBold;
  // font-weight: 800;
  font-size: 21.5px;
  line-height: 22px;
  img {
    width: 7px;
  }
}

.home_measure {
  min-width: 30px;
  font-family: GilroyMedium;
  font-size: 24px;
  line-height: 38px;
  color: #000000;
  margin-bottom: 5px;
}

.weight-controller__btn {
  img {
    width: 36px;
  }
}
// snackbar custom
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
</style>
