<template>
  <div class="ios-snsBtns__wrapper">
    <div v-for="(item, idx) in iosSnsBtns" :key="idx" class="ios-btn__container" @click="snsLoginRequest(item.type)">
      <img :src="item.img" alt="item.type" />
      <!-- <img :src="recentIcon" alt="icon" id="recent-icon" /> -->
      <div class="ballon" v-if="recentLogin === item.type">{{ $t("recent_login") }}</div>
    </div>
  </div>
</template>

<script>
import API from "@/api/auth/index.js";

export default {
  props: {
    recentLogin: String,
  },
  data() {
    return {
      recentIcon: require("@/assets/images_assets/icons/recent_tooltip-ic.png"),
      iosSnsBtns: [
        {
          type: "apple",
          img: require("@/assets/images_assets/sns-icon/apple-small.png"),
        },
        {
          type: "google",
          img: require("@/assets/images_assets/sns-icon/google-small.png"),
        },
        {
          type: "kakao",
          img: require("@/assets/images_assets/sns-icon/kakao-small.png"),
        },
      ],
    };
  },
  methods: {
    listener() {
      // ios
      window.addEventListener("message", this.handleMessage);
    },

    handleMessage(e) {
      const { payload } = JSON.parse(e.data);
      this.loginActionHandler(payload);
    },

    loginActionHandler(payload) {
      switch (payload.action) {
        case "REQ-KAKAO-LOGIN":
          return this.kakaoLogin(payload.result.accessToken);
        case "REQ-GOOGLE-LOGIN":
          return this.googleLogin(payload.result.accessToken);
        case "REQ-APPLE-LOGIN":
          return this.appleLogin(payload.result.identityToken);
        default:
          break;
      }
    },

    async kakaoLogin(token) {
      try {
        const body = { accessToken: token };
        const { data, status } = await API.postOauthValid("kakao", body);
        // console.log(data, status, Object.ha`sOwnProperty.call(data, "token"));
        if (Object.hasOwnProperty.call(data, "accessToken")) {
          localStorage.setItem("subjectId", data.subjectId);
          localStorage.setItem("surveyStatus", data.status);
          localStorage.setItem("snsType", "kakao");
          localStorage.setItem("loginError", false);
          Webview.loginSuccess();
          this.$store.commit("SNS_LOGIN", data.accessToken);
          this.$router.push({ path: "/home" });
        } else {
          this.$router.push({
            name: "Join",
            params: {
              id: data.kakao.id,
              snsType: "kakao",
              email: data.kakao.kakao_account.email || "",
              profileImg: data.kakao.kakao_account.profile_image,
              name: data.kakao.kakao_account.nickname,
            },
          });
        }
      } catch (e) {
        this.$store.commit("setLoginError", true);
        console.log(e);
      }
    },
    async googleLogin(token) {
      try {
        const body = { accessToken: token };
        const { data, status } = await API.postOauthValid("google", body);
        // console.log(data, status);
        if (Object.hasOwnProperty.call(data, "accessToken")) {
          localStorage.setItem("subjectId", data.subjectId);
          localStorage.setItem("surveyStatus", data.status);
          localStorage.setItem("snsType", "google");
          localStorage.setItem("loginError", false);
          Webview.loginSuccess();
          this.$store.commit("SNS_LOGIN", data.accessToken);
          this.$router.push({ path: "/home" });
        } else {
          this.$router.push({
            name: "Join",
            params: {
              id: String(data.google.id),
              snsType: "google",
              email: data.google.email || "",
              profileImg: data.google.picture || "",
              name: data.google.given_name || "",
            },
          });
        }
      } catch (e) {
        this.$store.commit("setLoginError", true);
        console.log(e);
      }
    },
    async appleLogin(token) {
      try {
        // 201 token
        // 200 data
        const body = { idToken: token };
        const { data, status } = await API.postOauthValid("apple", body);
        // console.log(data, status);
        if (Object.hasOwnProperty.call(data, "accessToken")) {
          localStorage.setItem("subjectId", data.subjectId);
          localStorage.setItem("surveyStatus", data.status);
          localStorage.setItem("snsType", "apple");
          localStorage.setItem("loginError", false);
          Webview.loginSuccess();
          this.$store.commit("SNS_LOGIN", data.accessToken);
          this.$router.push({ path: "/home" });
        } else {
          this.$router.push({
            name: "Join",
            params: {
              id: data.apple.id,
              snsType: "apple",
              email: data.apple.email || "",
            },
          });
        }
      } catch (e) {
        this.$store.commit("setLoginError", true);
        console.log(e);
      }
    },

    snsLoginRequest(type) {
      if (type === "kakao") {
        /*global Webview*/
        /*eslint no-undef: "error"*/
        Webview.kakaoLogin();
        this.$nextTick(() => this.listener());
      } else if (type === "google") {
        Webview.googleLogin();
        this.$nextTick(() => this.listener());
      } else if (type === "apple") {
        Webview.appleLogin();
        this.$nextTick(() => this.listener());
      }
    },
  },

  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
  },
};
</script>

<style lang="scss" scoped>
.ios-snsBtns__wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
}

.ios-btn__container {
  position: relative;
  img {
    width: 80px;
    height: 80px;
  }
}

#recent-icon {
  position: absolute;
  width: 32px;
  height: 35px;
  top: -17%;
  left: 45%;
  transform: translateX(-50%);
}

.ballon {
  position: absolute;
  width: 45px;
  height: 40px;
  top: -28%;
  left: 45%;
  transform: translateX(-50%);
  background: #41d8e6;
  color: white;
  font-weight: 500;
  border-radius: 100%;
  padding: 8px 0;
  letter-spacing: -0.03em;
  font-size: 14px;
}

.ballon:after {
  border-top: 10px solid #41d8e6;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  left: 39%;
  bottom: -5px;
}
</style>
