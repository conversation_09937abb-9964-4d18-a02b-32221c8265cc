<template>
  <div>
    <!-- <div class="pt-40">
      <IOSBtns :recentLogin="recentLogin" />
    </div> -->
    <!-- iphone -->
    <div v-if="isIos" class="pt-40">
      <IOSBtns :recentLogin="recentLogin" />
    </div>

    <!-- android -->
    <!-- <div> -->
    <div v-else>
      <AOSBtns :recentLogin="recentLogin" />
    </div>
  </div>
</template>

<script>
import AOSBtns from "./AOSBtns.vue";
import IOSBtns from "./IOSBtns.vue";

export default {
  components: {
    AOSBtns,
    IOSBtns,
  },
  data() {
    return {
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      recentLogin: "",
    };
  },
  mounted() {
    const snsType = localStorage.getItem("snsType");
    console.log(snsType);
    this.recentLogin = snsType;
  },
};
</script>

<style lang="scss" scoped></style>
