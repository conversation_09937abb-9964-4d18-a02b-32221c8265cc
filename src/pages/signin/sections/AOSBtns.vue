<template>
  <div class="aos-btn__wrapper">
    <div class="aos-btn__container">
      <div class="kakao-btn" @click="kakaoLoginHandler">
        <img
          src="@/assets/images_assets/sns-icon/kakao.png"
          alt="kakao login"
        />
        <div class="login-text">{{ $t("kakao_login") }}</div>
        <div class="ballon" v-if="recentLogin === 'kakao'">
          {{ $t("recent_login") }}
        </div>
      </div>
      <div class="google-btn" @click="googleLoginHandler">
        <img
          src="@/assets/images_assets/sns-icon/google.png"
          alt="google login"
        />
        <div class="login-text">{{ $t("google_login") }}</div>
        <div class="ballon_google" v-if="recentLogin === 'google'">
          {{ $t("recent_login") }}
        </div>
      </div>
      <!-- <div @click="logout">google logout</div> -->
    </div>
    <!-- <div v-for="(item, idx) in aosSnsBtns" :key="idx" class="aos-btn__container" @click="snsLoginRequest(item.type)">
      <div class="sns-btn"><img :src="item.img" alt="item.type" /></div>
    </div> -->
  </div>
</template>

<script>
import API from "@/api/auth/index.js";

export default {
  props: {
    recentLogin: String,
  },
  data() {
    return {
      recentIcon: require("@/assets/images_assets/icons/recent_tooltip-ic.png"),
      aosSnsBtns: [
        {
          type: "kakao",
          img: require("@/assets/images_assets/sns-icon/kakao.png"),
        },
        {
          type: "google",
          img: require("@/assets/images_assets/sns-icon/google.png"),
        },
      ],
    };
  },
  methods: {
    kakaoLoginHandler(type) {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      Webview.kakaoLogin();
      this.$nextTick(() => this.listener());
    },
    logout() {
      Webview.googleLogout();
      this.$nextTick(() => this.listener());
    },
    googleLoginHandler() {
      Webview.googleLogin();
      this.$nextTick(() => this.listener());
    },

    listener() {
      document.addEventListener("message", this.handleMessage);
    },

    handleMessage(e) {
      const { payload } = JSON.parse(e.data);
      // alert(payload.action)
      this.loginActionHandler(payload);
    },
    loginActionHandler(payload) {
      switch (payload.action) {
        case "REQ-KAKAO-LOGIN":
          return this.kakaoLogin(payload.result.accessToken);
        case "REQ-GOOGLE-LOGIN":
          return this.googleLogin(payload.result.accessToken);
        default:
          break;
      }
    },

    async kakaoLogin(token) {
      try {
        const body = { accessToken: token };
        const { data, status } = await API.postOauthValid("kakao", body);
        // console.log(data, status, Object.hasOwnProperty.call(data, "token"));
        if (Object.hasOwnProperty.call(data, "accessToken")) {
          localStorage.setItem("subjectId", data.subjectId);
          localStorage.setItem("surveyStatus", data.status);
          localStorage.setItem("snsType", "kakao");
          Webview.loginSuccess();
          localStorage.setItem("loginError", false);
          this.$store.commit("SNS_LOGIN", data.accessToken);
          this.$router.push({ path: "/home" });
        } else {
          this.$router.push({
            name: "Join",
            params: {
              id: data.kakao.id,
              snsType: "kakao",
              email: data.kakao.kakao_account.email || "",
              profileImg: data.kakao.kakao_account.profile_image,
              name: data.kakao.kakao_account.nickname,
            },
          });
        }
      } catch (e) {
        this.$store.commit("setLoginError", true);
        console.log(e);
      }
    },

    async googleLogin(token) {
      // alert(token);
      try {
        const body = { accessToken: token };
        const { data, status } = await API.postOauthValid("google", body);
        // console.log(data, status);
        if (Object.hasOwnProperty.call(data, "accessToken")) {
          localStorage.setItem("subjectId", data.subjectId);
          localStorage.setItem("surveyStatus", data.status);
          localStorage.setItem("snsType", "google");
          localStorage.setItem("loginError", false);
          Webview.loginSuccess();
          this.$store.commit("SNS_LOGIN", data.accessToken);
          this.$router.push({ path: "/home" });
        } else {
          this.$router.push({
            name: "Join",
            params: {
              id: String(data.google.id),
              snsType: "google",
              email: data.google.email || "",
              profileImg: data.google.picture || "",
              name: data.google.given_name || "",
            },
          });
        }
      } catch (e) {
        this.$store.commit("setLoginError", true);
        // alert(e);
        console.log(e.response.data);
      }
    },
  },

  mounted() {
    this.logout();
  },

  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
    document.removeEventListener("message", this.handleMessage);
    console.log("snsType" + this.recentType);
  },
};
</script>

<style lang="scss" scoped>
.aos-btn__wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 40px;
}

.aos-btn__container {
  position: relative;
  height: 70px;
  img {
    width: 100%;
  }
}

.google-btn {
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  border: 1px solid #ededed;
  border-radius: 10px;
  img {
    width: 28px;
    height: 28px;
    line-height: 60px;
  }
}

.kakao-btn {
  width: 100%;
  height: 50px;
  display: flex;
  margin-bottom: 20px;
  justify-content: center;
  align-items: center;
  background-color: #fae300;
  border-radius: 10px;
  img {
    width: 28px;
    height: 26px;
    line-height: 60px;
  }
}
.login-text {
  width: 70%;
  font-weight: 500;
  font-size: 18px;
}

.ballon {
  position: absolute;
  width: 45px;
  height: 40px;
  right: 7%;
  bottom: 62px;
  background: #41d8e6;
  color: white;
  font-weight: 500;
  border-radius: 100%;
  padding: 8px 0;
  letter-spacing: -0.03em;
  font-size: 14px;
}

.ballon:after {
  border-top: 10px solid #41d8e6;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  left: 39%;
  bottom: -5px;
}
.ballon_google {
  position: absolute;
  width: 45px;
  height: 40px;
  right: 7%;
  bottom: -15px;
  background: #41d8e6;
  color: white;
  font-weight: 500;
  border-radius: 100%;
  padding: 8px 0;
  letter-spacing: -0.03em;
  font-size: 14px;
}

.ballon_kakao:after {
  border-top: 10px solid #41d8e6;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 0px solid transparent;
  content: "";
  position: absolute;
  left: 39%;
  bottom: -5px;
}
</style>
