<template>
  <div>
    <background>
      <CommonModal
        :content="waitText"
        :confirmText="waitConfirmText"
        v-show="showWaitModal"
        @handleConfirmClick="handleConfirmClick"
      />
      <WithdrawalModal v-show="isWithdrawal" />
      <ErrorModal v-show="showErrorModal" :error="error" @isClicked="isClicked" />
      <!-- 로그인페이지 헤더, 슬로건이미지 영역 -->
      <div :class="isIos ? 'header__wrapper' : 'header__wrapper-android'">
        <div class="text-center main-text" justify="center">
          <img class="slogan" src="@/assets/images/cym702_slogan.png" />
        </div>
        <strong>Pet</strong>
        <!-- 개발 용-->
        <strong v-if="isDev">-dev</strong>
      </div>

      <!-- 로그인 폼 -->
      <div class="login-form__wrapper">
        <v-form>
          <v-text-field
            v-model="userId"
            :label="this.$i18n.t('input_id')"
            color="#41D8E6"
            type="text"
            required
          >
          </v-text-field>
          <v-text-field
            v-model="password"
            :label="this.$i18n.t('input_pwd')"
            color="#41D8E6"
            :append-icon="showPassword ? '$eye_show' : '$eye_off'"
            :type="showPassword ? 'text' : 'password'"
            @click:append="showPassword = !showPassword"
            required
          ></v-text-field>

          <v-btn class="main-large-btn" elevation="0" color="#41D8E6" @click="generalLogin">
            {{ $t("login") }}
          </v-btn>
          <div class="btn__wrapper pt-21">
            <router-link :to="'/join'">
              <div class="btn-item txt-blackColor">
                <div>{{ $t("join_btn") }}</div>
              </div>
            </router-link>
            <div class="btn-divider">|</div>
            <router-link :to="'/find'">
              <div class="btn-item">{{ $t("find_account_btn") }}</div>
            </router-link>
          </div>
        </v-form>
        <!-- SNS 버튼 section -->
        <SnsButtons />
      </div>
      <Loading v-if="loading" />
    </background>
  </div>
</template>

<script>
import Background from "@/components/Common/Background.vue";
import SnsButtons from "./sections/SnsButtons.view.vue";
import WithdrawalModal from "@/components/Home/DeletedModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import Loading from "@/components/Common/Loading.vue";
import CommonModal from "@/components/Common/modal/modal.ui";

export default {
  components: {
    Background,
    SnsButtons,
    WithdrawalModal,
    ErrorModal,
    Loading,
    CommonModal,
  },
  data() {
    return {
      userId: "",
      password: "",
      loading: false,
      showPassword: false,
      isWithdrawal: false,
      isDev: process.env.NODE_ENV === "development",
      // showErrorModal: false,
      error: this.$i18n.t("login_error_msg"),
      waitText: this.$i18n.t("wait_text"),
      waitConfirmText: this.$i18n.t("wait_confirm_text"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  computed: {
    showErrorModal() {
      return this.$store.state.loginError;
    },
    showWaitModal() {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

      if (timezone.includes("Seoul") && this.$i18n.locale.includes("ko")) {
        return false;
      }

      return true;
    },
  },
  mounted() {
    this.isWithdrawal = JSON.parse(localStorage.getItem("isWithdrawal"));
    sessionStorage.clear();
    /*global Webview*/
    /*eslint no-undef: "error"*/
    if (window.ReactNativeWebView) {
      Webview.cacheClear();
    }
  },
  methods: {
    async generalLogin() {
      this.loading = true;
      try {
        // this.showErrorModal = false;
        const userData = {
          account: this.userId,
          password: this.password,
        };
        this.$store.dispatch("LOGIN", userData);
        this.loading = false;
      } catch (e) {
        console.log(e.response.data);
      }
      // setTimeout(() => {
      // this.showErrorModal = localStorage.getItem("loginError");
      // }, 5000);
    },
    isClicked() {
      // this.showErrorModal = false;
      // localStorage.setItem("loginError", false);
      this.$store.commit("setLoginError", false);
      this.$router.push("/login");
    },
    handleConfirmClick() {
      const lang = (this.$i18n && this.$i18n.locale) || "ko";

      if (lang === "ko") {
        return;
      }

      const message =
        lang === "ja"
          ? "https://yellosis.notion.site/6-27-216edea689bc8071841fd8d7c5bcdff3"
          : "https://www.indiegogo.com/projects/cym702-pet-easy-at-home-pet-health-test-kits#/updates/all";

      console.log(message);

      Webview.openUrl(message);
    },
  },
};
</script>

<style lang="scss" scoped>
.header__wrapper {
  padding: 190px 0 90px;
}

.header__wrapper-android {
  padding: 160px 0 80px;
}

.main-text {
  width: 100%;
  padding: 0 1.84rem;

  img {
    width: 100%;
  }
}

.login-form__wrapper {
  width: 100%;
  padding: 0 1.84rem;
}

.v-text-field.v-text-field--solo .v-input__control input {
  caret-color: #41d8e6 !important;
  padding: 0px 0 10px !important;
}

.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .v-input {
  font-family: GilroyMedium !important;
  font-size: 20px;
}

::v-deep .v-text-field input:invalid {
  padding: 8px 0 4px 0;
  // padding: 0;
}
::v-deep .v-text-field {
  label {
    padding: 0;
    font-size: 20px !important;
    color: #a7a7a7;
  }
}

::v-deep .v-text-field .v-label {
  top: 0 !important;
  padding: 0;
  // font-size: 12px;
  // color: #41d8e6;
}

.btn__wrapper {
  display: flex;
  justify-content: center;
}

.btn-item {
  margin: 0px 10px;
  font-size: 16px;
  color: #858585;
  font-weight: 500;
  letter-spacing: -0.03em;
}

.txt-blackColor {
  color: #000;
}

.btn-divider {
  font-size: 14px;
}
</style>
