<template>
  <div>
    <background>
      <div class="container">
        <v-carousel>
          <v-carousel-item cover v-for="(slide, index) in slides" :key="index">
            <div class="intro-title" :class="lang === 'ko' ? '' : 'en-title'" v-html="slide.title"></div>
            <div class="intro-subtitle" v-html="slide.subtitle"></div>
            <div class="intro-img__wrapper">
              <img :src="slide.img" alt="" />
            </div>
          </v-carousel-item>
        </v-carousel>
      </div>
      <LastButton :title="title" :path="path" :color="color" @setLocalStorage="setLocalStorage" />
    </background>
  </div>
</template>

<script>
import LastButton from "@/components/Common/LastButton.vue";

export default {
  components: { LastButton },
  data() {
    return {
      title: this.$i18n.t("confirm"),
      path: "/login",
      color: "#41D8E6",
      showButton: false,
      lang: "ko",
      slides: [
        {
          id: 0,
          title: this.$i18n.t("intro_title_1"),
          subtitle: this.$i18n.t("intro_mid_1"),
          img: "",
        },
        {
          id: 1,
          title: this.$i18n.t("intro_title_2"),
          subtitle: this.$i18n.t("intro_mid_2"),
          img: "",
        },
        // {
        //   id: 2,
        //   title: this.$i18n.t("intro_title_3"),
        //   subtitle: this.$i18n.t("intro_mid_3"),
        //   img: "",
        // },
      ],
      visibleSlide: 0,
    };
  },
  computed: {
    slidesLen() {
      return this.slides.length;
    },
    active() {
      return this.visibleSlide === this.slides.id ? "active" : false;
    },
  },
  mounted() {
    this.lang = this.$i18n.locale.includes("ko") ? "ko" : "en";
    this.slides.map((slide, idx) => {
      slide.img = require(`@/assets/images/intro/intro_${this.lang}${idx + 1}.png`);
    });
  },
  methods: {
    next() {
      if (this.visibleSlide === 1) this.showButton = true;
      if (this.visibleSlide >= this.slides.length - 1) {
        this.visibleSlide = 2;
      } else {
        this.visibleSlide++;
      }
    },
    setLocalStorage() {
      // console.log("intro checked!");
      localStorage.setItem("introChecked", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  // position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // overflow-y: auto;
  // padding: calc(100vh / 15) 10vw 13vh 10vw;
}

// @media (max-width: 380px) {
//   .container {
//     padding: 0 10vw;
//     height: 95vh;
//   }
//   .intro-img__wrapper {
//     img {
//       width: 70% !important;
//     }
//   }
//   .intro-title {
//     font-size: 24px !important;
//   }
//   .intro-subtitle {
//     font-size: 16px !important;
//   }
// }

.intro-title {
  // font-family: "Noto Sans KR";
  font-style: normal;
  font-weight: 700;
  font-size: 26px;
  line-height: 30px;
  text-align: center;
  letter-spacing: -0.03em;
  /* Yellosis gray/5 */
  color: #323232;
}

.intro-subtitle {
  // font-family: "Noto Sans KR";
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 22px;
  /* or 143% */
  text-align: center;
  letter-spacing: -0.03em;
  color: #000000;
  margin: 2vh 0 1.5vh 0;
}

.intro-img__wrapper {
  width: 100%;
  // height: 100%;
  img {
    width: 80%;
  }
}

@media screen and(max-width: 375px) {
  .intro-img__wrapper {
    img {
      width: 75%;
    }
  }
}

::v-deep .v-item-group {
  height: 100vh !important;
  z-index: 0;
}

::v-deep .v-responsive {
  height: 100% !important;
  padding: calc(100vh / 7) 10vw 13vh 10vw;
}

::v-deep .VueCarousel-pagination {
  position: fixed;
  left: 0;
  top: 0;
}

::v-deep .v-carousel__controls {
  top: 55%;
  background: transparent;
}

::v-deep .v-carousel__controls__item .v-icon {
  opacity: 1;
}
</style>
