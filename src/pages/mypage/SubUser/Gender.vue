<template>
  <div class="gender-input__wrapper">
    <div class="gender-input__title">{{ $t("profile_sex") }}</div>
    <div class="gender-checkbox__items">
      <div class="gender-checkbox__item">
        <v-checkbox
          color="#41d8e6"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          v-model="male"
          @change="maleCheckBoxHandler"
        >
          <template v-slot:label>
            <div>{{ $t("male") }}</div>
          </template>
        </v-checkbox>
      </div>
      <div class="gender-checkbox__item">
        <v-checkbox
          color="#41d8e6"
          off-icon="$check_box"
          on-icon="$check_box_inactive"
          v-model="female"
          @change="femaleCheckBoxHandler"
        >
          <template v-slot:label>
            <div>{{ $t("female") }}</div>
          </template>
        </v-checkbox>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      male: false,
      female: false,
      gender: null,
    };
  },
  methods: {
    maleCheckBoxHandler: function() {
      if (this.male) {
        this.female = false;
      }
      this.$emit("maleCheckBoxHandler", this.male);
    },
    femaleCheckBoxHandler: function() {
      if (this.female) {
        this.male = false;
      }
      this.$emit("femaleCheckBoxHandler", this.female);
    },
  },
};
</script>

<style lang="scss" scoped>
.gender-input__wrapper {
  margin-top: 20px;
  width: 100%;
  height: 80px;
}

.gender-checkbox__items {
  display: flex;
  width: 100%;
  color: #646464;
}

.gender-checkbox__item {
  width: 100%;
}
.gender-input__title {
  width: 100%;
  text-align: left;
  margin: 7px 0 10px 0;
  font-size: 16px;
  font-weight: 500;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
