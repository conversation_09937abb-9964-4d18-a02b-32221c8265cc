<template>
  <div class="add-user__container">
    <CompleteAlert
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-show="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
    <HeaderNav class="empty-background" :pageName="pageName" />
    <div class="content__wrapper">
      <div class="sub-title">{{ $t("add_user_description") }}</div>
      <div class="form__wrapper">
        <PetTypeForm @petTypeHandler="petTypeHandler" />
      </div>
      <div class="form__wrapper">
        <PetSearchForm :typeList="typeList" @breedHandler="breedHandler" />
      </div>
      <GenderForm @genderInputHandler="genderInputHandler" />
      <div class="form__wrapper">
        <NeuteredForm
          :initialNeutered="neutered"
          @neuteredHandler="neuteredHandler"
        />
      </div>
      <div class="form__wrapper">
        <NameForm @nameInputHandler="nameInputHandler" />
      </div>
      <div class="form__wrapper">
        <WeightForm @weightInputHandler="weightInputHandler" />
      </div>
      <div class="form__wrapper">
        <WeightGoalForm @weightGoalInputHandler="weightGoalInputHandler" />
      </div>
      <div class="form__wrapper">
        <BirthForm @birthInputHandler="birthHandler" />
      </div>
      <div class="form__wrapper">
        <AdoptionDateForm
          :initialBirth="initialBirth"
          @adoptionDateHandler="adoptionDateHandler"
          @sameBirthHandler="sameBirthHandler"
        />
      </div>
      <div class="form__wrapper">
        <RegistrationNumberForm
          @registrationNumberHandler="registrationNumberHandler"
        />
      </div>

      <div class="btn__wrapper">
        <v-btn
          class="save-btn"
          :disabled="!isAvailableSave"
          elevation="0"
          color="#41D8E6"
          type="submit"
          @click="saveBtnHandler"
          >{{ $t("save") }}</v-btn
        >
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import PetTypeForm from "@/components/Forms/PetTypeForm.vue";
import PetSearchForm from "@/components/Forms/PetSearchForm.vue";
import NeuteredForm from "@/components/Forms/NeuteredForm.vue";
import NameForm from "@/components/Forms/NameForm.vue";
import GenderForm from "@/components/Forms/GenderForm.vue";
import BirthForm from "@/components/Forms/BirthForm.vue";
import WeightForm from "@/components/Forms/WeightForm.vue";
import WeightGoalForm from "@/components/Forms/WeightGoalForm.vue";
import AdoptionDateForm from "@/components/Forms/AdoptionDateForm.vue";
import RegistrationNumberForm from "@/components/Forms/RegistrationNumberForm.vue";

import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { postSubUser } from "@/api/user/index";
import API from "@/api/auth/index.js";

export default {
  name: "Adduser",
  components: {
    PetTypeForm,
    PetSearchForm,
    NeuteredForm,
    HeaderNav,
    GenderForm,
    NameForm,
    WeightForm,
    WeightGoalForm,
    BirthForm,
    AdoptionDateForm,
    RegistrationNumberForm,
    CompleteAlert,
    ErrorModal,
  },
  data() {
    return {
      loading: false,
      profile_img: "",
      pageName: this.$i18n.t("add_pet"),
      typeList: [],
      subjectTypeId: null,
      name: "",
      validName: false,
      neutered: null,
      gender: null,
      validGender: false,
      initialBirth: null,
      birth: null,
      validBirth: false,
      adoptionDate: null,
      adoptionDateValid: false,
      registrationNumber: null,
      registrationNumberValid: false,
      height: 0,
      validHeight: false,
      weight: 0,
      validWeight: false,
      goalWeight: 0,
      validGoalWeight: false,
      showCompleteAlert: false,
      showErrModal: false,
      // isAvailableSave: false,
      content: this.$i18n.t("success_add_user"),
      error: this.$i18n.t("error_add_user"),
    };
  },
  methods: {
    petTypeHandler(type) {
      // console.log(type);
      this.getTypeList(type);
    },
    breedHandler(breedObj) {
      // console.log(breedObj);
      this.subjectTypeId = Number(breedObj.id);
    },
    genderInputHandler(gender) {
      // console.log(gender);
      this.gender = gender;
    },
    neuteredHandler(neutered) {
      // console.log(neutered);
      this.neutered = neutered;
    },
    nameInputHandler(inputName) {
      // console.log("name input", inputName);
      this.name = inputName.name;
      this.validName = inputName.valid;
    },
    birthHandler(fromChild) {
      // console.log(fromChild);
      this.birth = fromChild.birth;
      this.validBirth = fromChild.valid;
    },
    adoptionDateHandler(adoptionDateObj) {
      // console.log(adoptionDateObj);
      this.adoptionDate = adoptionDateObj.adoptionDate;
      this.adoptionDateValid = adoptionDateObj.valid;
    },
    sameBirthHandler(isSame) {
      this.initialBirth = isSame ? this.birth : null;
    },
    registrationNumberHandler(registrationNumberObj) {
      // console.log(registrationNumberObj);
      this.registrationNumber = registrationNumberObj.registrationNumber;
      this.registrationNumberValid = registrationNumberObj.valid;
    },
    weightInputHandler(inputWeight) {
      // console.log(inputWeight);
      this.weight = inputWeight.weight;
      this.validWeight = inputWeight.valid;
    },
    weightGoalInputHandler(inputGoalWeight) {
      // console.log(inputGoalWeight);
      // console.log(inputGoalWeight.goal);
      this.goalWeight = inputGoalWeight.goal;
      this.validGoalWeight = inputGoalWeight.valid;
    },
    saveBtnHandler() {
      this.postAddPet();
    },
    inputValidation(name) {
      if (/^[ㄱ-ㅎ|가-힣|a-z|A-Z]{1,10}$/.test(name)) return true;
      else return false;
    },
    isConfirmed() {
      this.$router.go(-1);
    },
    isClicked() {
      this.showErrModal = false;
    },

    // api
    async postAddPet() {
      try {
        const petDate = {
          subjectTypeId: Number(this.subjectTypeId),
          sex: this.gender,
          neutered: this.neutered,
          nickname: this.name,
          initialWeight: Number(this.weight),
          birth: this.birth,
        };
        // console.log(this.goalWeight);
        this.goalWeight === 0 ||
        this.goalWeight === undefined ||
        this.goalWeight === null
          ? null
          : (petDate.targetWeight = Number(this.goalWeight));
        this.adoptionDate === null
          ? null
          : (petDate.adoptionDate = this.adoptionDate);
        this.registrationNumber === null
          ? null
          : (petDate.registrationNumber = this.registrationNumber);
        // console.log(petDate);
        const res = await postSubUser(petDate);
        // console.log(res);
        if (res.status === 201) {
          this.showCompleteAlert = true;
          this.$store.commit("setNoSubjects", false);
        } else {
          this.showErrModal = true;
        }
      } catch (err) {
        console.log(err);
        this.showErrModal = true;
      }
    },

    async getTypeList(type) {
      try {
        const { data, status } = await API.getTypeList(type);
        // console.log(data, status);
        if (status == 200) {
          let petList = data.subjectTypes;

          if (!this.isKo) {
            petList.sort((a, b) => {
              return a.english.localeCompare(b.english);
            });

            petList = petList.map((item) => {
              return {
                ...item,
                english: `${item.english
                  .charAt(0)
                  .toUpperCase()}${item.english.slice(1)}`,
              };
            });
          }

          this.typeList = petList;
        }
      } catch (e) {
        console.error(e);
      }
    },
  },
  computed: {
    isAvailableSave() {
      const registrationNumberCondition =
        this.registrationNumber === null ||
        this.registrationNumber === "" ||
        this.registrationNumber.length === 15;

      // return true;
      return (
        (this.gender !== null &&
          this.validName &&
          this.validBirth &&
          this.validWeight &&
          registrationNumberCondition) ||
        this.adoptionDateValid ||
        this.targetWeightValid
      );
    },
  },
  watch: {
    name(newVal) {
      const nameRegex = /^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,10}$/;
      /^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,10}$/.test(newVal)
        ? (this.validName = true)
        : (this.validName = false);
      // console.log("validName", this.validName);
    },
    gender(newVal) {
      this.gender === ""
        ? (this.validGender = false)
        : (this.validGender = true);
      // console.log("validGender", this.validGender);
    },
  },
};
</script>

<style lang="scss" scoped>
.add-user__container {
  width: 100%;
  height: 100%;
}

.content__wrapper {
  width: 100%;
  padding: 40px 30px 80px;
  overflow: auto;
}

.sub-title {
  font-size: 18px;
  font-weight: 500;
  color: #646464;
  text-align: left;
  letter-spacing: -0.03em;
  padding-bottom: 30px;
}
.profile__wrapper {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  // align-items: center;
  margin-top: 20px;
}
.profile-img__box {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: #fff;
  position: relative;
  border: 5px solid #41d8e6;
}

.profile-img__wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 35%;
  }
}

.check-box {
  // display: flex;
  color: #646464;
  margin-left: 20px;
  width: 65%;
  // display: flex;
}

.form__wrapper {
  // padding: 20px 0;
  margin-bottom: 30px;
}

.name-form__wrapper {
  margin: 30px 0;
}

.input__wrapper {
  margin-bottom: 30px;
}

.input-title {
  width: 100%;
  text-align: left;
  color: #646464;
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 10px;
}

.input-label {
  color: #646464;
  font-size: 18px;
  line-height: 26px;
  margin-left: 10px;
}

.btn__wrapper {
  width: 100%;
  position: fixed;
  bottom: 5vh;
  left: 0;
  padding: 0px 30px;
}

.save-btn {
  letter-spacing: -0.03em;
  height: 50px !important;
  position: relative;
  z-index: 2;
  max-width: 340px;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 20px !important;
  font-weight: bold !important;
  color: #ffffff !important;
  margin: 0 auto;
}

::v-deep .v-input--selection-controls {
  margin: 0;
  padding: 0;
}

::v-deep .v-input__slot {
  margin: 0;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 5px;
}

::v-deep .v-messages {
  min-height: 10px;
}

::v-deep .v-input--selection-controls__input {
  margin: 0;
}

::v-deep .v-text-field__slot {
  font-size: 12px;
}
.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

// input[type="file"] {
//   position: absolute;
//   width: 1px;
//   height: 1px;
//   padding: 0;
//   margin: -1px;
//   overflow: hidden;
//   clip: rect(0, 0, 0, 0);
//   border: 0;
// }
</style>
