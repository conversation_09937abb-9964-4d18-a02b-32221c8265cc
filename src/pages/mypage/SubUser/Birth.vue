<template>
  <div class="container">
    <div class="age-input__title">{{ $t("profile_birth") }}</div>
    <div class="userdetail-text-field__items">
      <div class="name-input__item">
        <v-text-field
          type="number"
          inputmode="numeric"
          v-model="year"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_year_placeholder')"
          :error-messages="yearError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
          @change="birthHandler"
        >
        </v-text-field>
      </div>
      <div class="name-input__item age">
        <v-text-field
          type="number"
          inputmode="numeric"
          v-model="month"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_month_placeholder')"
          :error-messages="monthError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
          @change="birthHandler"
        >
        </v-text-field>
      </div>
      <div class="name-input__item">
        <v-text-field
          type="number"
          inputmode="numeric"
          v-model="day"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_day_placeholder')"
          :error-messages="dayError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
          @change="birthHandler"
        >
        </v-text-field>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      year: null,
      month: null,
      day: null,
      max: 4,
      yearError: "",
      monthError: "",
      dayError: "",
      isAvailableYear: false,
    };
  },
  methods: {
    birthHandler() {
      const birth = `${this.year}-${this.month}-${this.day}`;
      if (this.year !== null && this.month !== null && this.day !== null) {
        this.$emit("birthHandler", birth);
      }
    },
    focusInHandler() {
      const ageInputTitle = document.querySelector(".age-input__title");
      ageInputTitle.style.color = "#41D8E6";
    },
    focusOutHandler() {
      const ageInputTitle = document.querySelector(".age-input__title");
      ageInputTitle.style.color = "rgba(0, 0, 0, 0.6)";
    },
    yearValidation(year) {
      const today = new Date();
      const yearNow = today.getFullYear();
      if (1900 > year || year > yearNow) return false;
      else return true;
    },
    monthValidation(month) {
      if (month < 1 || month > 12 || month.length === 1) return false;
      else return true;
    },
    dayValidation(day) {
      if (day < 1 || day > 31 || day.length === 1) return false;
      else return true;
    },
  },
  watch: {
    year(newVal) {
      // console.log(this.yearValidation(newVal));
      if (this.year.length === 0 || this.yearValidation(newVal)) this.yearError = "";
      else this.yearError = this.$i18n.t("invalid");
    },
    month(newVal) {
      // console.log(this.monthValidation(newVal));
      if (this.month.length === 0 || this.monthValidation(newVal)) this.monthError = "";
      else this.monthError = this.$i18n.t("invalid");
    },
    day(newVal) {
      // console.log(this.dayValidation(newVal));
      if (this.day.length === 0 || this.dayValidation(newVal)) this.dayError = "";
      else this.dayError = this.$i18n.t("invalid");
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  margin: 20px 0;
}
.userdetail-text-field__items {
  display: flex;
}
.age-input__subtitle {
  font-size: 12px;
  text-align: left;
  color: #a7a7a7;
}

.age-input__items {
  display: flex;
  padding-top: 15px;
}

.age {
  padding: 0 12px;
}
::v-deep .v-text-field {
  padding: 0;
}

:v-deep .theme--light.v-text-field > .v-input__control > .v-input__slot:before {
  border-color: #a7a7a7 !important;
}

.age-input__title {
  text-align: left;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
