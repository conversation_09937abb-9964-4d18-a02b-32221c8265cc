<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="container">
      <router-link to="/mypage/guide/cym702">
        <div class="menu-card">
          <div class="menu-card-header">
            <div class="menu-card-title">
              Cym<sup>702</sup> Pet {{ $t("guide_category_app") }}
            </div>
            <div class="icon__wrapper">
              <img src="@/assets/images/bottom-arrow.png" alt="right arrow" />
            </div>
          </div>
        </div>
      </router-link>
      <!-- <router-link to="/mypage/guide/test">
        <div class="menu-card">
          <div class="menu-card-header">
            <div class="menu-card-title">
              Cym<sup>702</sup> {{ $t("guide_category_exam") }}
            </div>
            <div class="icon__wrapper">
              <img src="@/assets/images/bottom-arrow.png" alt="right arrow" />
            </div>
          </div>
        </div>
      </router-link> -->
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";

export default {
  name: "UseageGuide",
  components: { HeaderNav },
  data: () => ({
    pageName: "",
  }),
  mounted() {
    this.pageName = this.$t("useage_guide");
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 40px 30px;
}
.menu-card {
  width: 100%;
}

.menu-card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.menu-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
}

.icon__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 14px;
    transform: rotate(-90deg);
  }
}
</style>
