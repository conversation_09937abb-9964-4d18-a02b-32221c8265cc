<template>
  <div class="guide-background">
    <div class="background-dim">
      <img :src="bgImg" alt="guide image" />
    </div>
    <div class="container">
      <div class="close-btn">
        <img src="@/assets/images/guide_img/close_btn.png" alt="close btn" @click="closeTutorial" />
      </div>
      <div class="stepper-wrapper" :class="{ 'slide2-active': visibleSlide === 2 }">
        <Slot
          v-for="item in slides"
          v-bind="item"
          :key="item.id"
          :value="visibleSlide"
          @input="visibleSlide = $event"
        />
      </div>
      <div class="carousel">
        <div class="slide1" v-show="this.visibleSlide === 0">
          <div class="intro-img__wrapper">
            <img :src="slide1Img" loading="lazy" alt="img" />
          </div>
          <div class="arrow-img__wrapper">
            <img src="@/assets/images/guide_img/arrow-up.png" loading="lazy" alt="img" />
          </div>
          <div class="slide-title" :class="lang === 'ko' ? '' : 'en-title'">
            {{ $t("tutorial_one_title") }}
          </div>
          <div class="slide-content" v-html="this.$i18n.t('tutorial_one_text')"></div>
        </div>
        <div class="slide2" v-show="this.visibleSlide === 1">
          <div class="slide-title" :class="lang === 'ko' ? '' : 'en-title'">
            {{ $t("tutorial_two_title") }}
          </div>
          <div class="slide-content" v-html="this.$i18n.t('tutorial_two_text')"></div>
          <div class="arrow-img__wrapper">
            <img src="@/assets/images/guide_img/arrow-up.png" loading="lazy" />
          </div>
          <div class="result-img__wrapper">
            <img :src="slide2Img" loading="lazy" />
          </div>
        </div>
        <div class="slide3" v-show="this.visibleSlide === 2">
          <div class="pet-history-guide__wrapper">
            <img :src="slide3Img" loading="lazy" alt="img" />
          </div>
          <div class="arrow-img__wrapper">
            <img src="@/assets/images/guide_img/arrow-up.png" loading="lazy" alt="img" />
          </div>
          <div class="slide-title" :class="lang === 'ko' ? '' : 'en-title'">
            {{ $t("tutorial_two_title") }}
          </div>
          <div class="slide-content" v-html="this.$i18n.t('tutorial_two_description')"></div>
        </div>
        <div class="slide3" v-show="this.visibleSlide === 3">
          <div class="slide-title" :class="lang === 'ko' ? '' : 'en-title'">
            {{ $t("tutorial_last_title") }}
          </div>
          <div class="slide-content" v-html="this.$i18n.t('tutorial_last_text')"></div>
          <div class="hand-img__wrapper">
            <img src="@/assets/images/guide_img/dropdown.gif" loop="infinite" loading="lazy" />
          </div>
          <div class="guide_img__wrapper">
            <img src="@/assets/images/guide_img/guide_weight.png" loading="lazy" />
            <!-- <img src="@/assets/images/guide_img/guide_water.png" /> -->
            <img src="@/assets/images/guide_img/guide_pee.png" loading="lazy" />
          </div>
        </div>
      </div>
      <div class="btn__wrapper">
        <button class="next-btn" @click="next">{{ btnText }}</button>
      </div>
      <!-- <LastButton :title="title" :path="path" :color="color" v-if="showButton" /> -->
    </div>
  </div>
</template>

<script>
import Slot from "@/components/Common/Slot.vue";

export default {
  components: { Slot },
  data() {
    return {
      title: this.$i18n.t("confirm_btn"),
      path: "/mypage/guide",
      color: "#41D8E6",
      showButton: false,
      slides: [{ id: 0 }, { id: 1 }, { id: 2 }, { id: 3 }],
      visibleSlide: 0,
      lang: "ko",
      bgImg: "",
      slide1Img: "",
      slide2Img: "",
      slide3Img: "",
      isUrineDetailView: false,
    };
  },
  computed: {
    slidesLen() {
      return this.slides.length;
    },
    btnText() {
      return this.visibleSlide === 3 ? this.$i18n.t("confirm_btn") : this.$i18n.t("next_btn");
    },
  },
  methods: {
    next() {
      if (this.visibleSlide === 3) {
        this.$router.push({ path: "/mypage/guide" });
        return;
      }

      if (this.visibleSlide === 1) {
        this.isUrineDetailView = true;
        this.bgImg = require(`@/assets/images/guide_img/pet-urine-detail_${this.lang}.png`);
      } else if (this.isUrineDetailView) {
        this.isUrineDetailView = false;
        this.bgImg = require(`@/assets/images/guide_img/pet-guide-bg_${this.lang}.png`);
      }

      this.visibleSlide += 1;
    },
    closeTutorial() {
      this.$router.push({ path: "/mypage/guide" });
    },
  },
  mounted() {
    // console.log(this.$i18n.locale);
    const lang = this.$i18n.locale.includes("ko")
      ? "ko"
      : this.$i18n.locale.includes("ja")
      ? "ja"
      : "en";

    // const lang = this.$i18n.locale.includes("ko") ? "ko" : "en" ?? "en";
    this.lang = lang;
    this.bgImg = require(`@/assets/images/guide_img/pet-guide-bg_${lang}.png`);
    this.slide1Img = require(`@/assets/images/guide_img/cym_${lang === "ko" ? "ko" : "en"}.png`);
    this.slide2Img = require(`@/assets/images/guide_img/pet-test-result_${lang}.png`);
    this.slide3Img = require(`@/assets/images/guide_img/history-graph-guide_${lang}.png`);
  },
};
</script>

<style lang="scss" scoped>
.guide-background {
  position: relative;
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
  }
}
.background-dim:after {
  width: 100%;
  background: rgba(0, 0, 0, 0.8);
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  overflow-y: auto;
  padding: 40px 10px;
}

.close-btn {
  width: 100%;
  height: 24px;
  left: 0;
  text-align: left;
  padding-left: 15px;
  img {
    width: 24px;
  }
}

.stepper-wrapper {
  display: flex;
  margin: 5vh 0;
}

.stepper-wrapper.slide2-active {
  margin-bottom: 2vh;
}

.carousel {
  width: 100%;
  height: 100%;
}

.slide1 {
  width: 100%;
  height: 100%;
}
.intro-img__wrapper {
  width: 100%;
  position: relative;
  img {
    width: 100%;
    object-fit: contain;
    border-radius: 10px;
  }
}

.slide2 {
  .arrow-img__wrapper {
    transform: rotate(180deg);
    margin: 3vh 0;
  }
}

.arrow-img__wrapper {
  width: 100%;
  position: relative;
  margin: 3vh 0 1vh 0;
  img {
    width: 20px;
    object-fit: contain;
  }
}

.result-btns-container {
  width: 100%;
  height: 100%;
  display: flex;
  margin-top: 14vh;
  gap: 20px;
}

.arrow__wrapper {
  width: 80%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 9% 2% 0;
  gap: 5%;
}

.arrow-right__wrapper {
  transform: rotate(90deg);
  img {
    width: 20px;
    object-fit: contain;
  }
}

.result-btn-img__wrapper {
  width: 20%;
  display: flex;
  justify-content: end;
  img {
    width: 95%;
  }
}

.result-img__wrapper {
  img {
    border-radius: 10px;
  }
}

.slide3 {
  width: 100%;
}

.slide3 {
  .arrow-img__wrapper {
    transform: rotate(0deg);
    margin: 3vh 0;
  }
}

.hand-img__wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  margin: 3vh 0 3vh 0;
  img {
    width: 100px;
  }
}

.guide_img__wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 12px;
  img {
    height: 90px;
    object-fit: contain;
  }
}

.slide-title {
  color: #41d8e6;
  font-weight: 700;
  font-size: 20px;
}
.slide-content {
  color: #ffffff;
  font-size: 18px;
  margin-top: 1vh;
}

.btn__wrapper {
  width: 100%;
  position: fixed;
  bottom: 5vh;
  left: 0;
  padding: 0px 1.875rem;
}

.next-btn {
  height: 50px;
  position: relative;
  max-width: 340px;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 20px;
  font-weight: bold;
  background-color: #41d8e6;
  color: #ffffff;
  margin: 0 auto;
}

.pet-history-guide__wrapper {
}
</style>
