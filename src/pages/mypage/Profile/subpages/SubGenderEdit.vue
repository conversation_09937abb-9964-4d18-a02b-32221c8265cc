<template>
  <div class="bg-modal">
    <CompleteAlert
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-show="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
    <div class="name-edit__wrapper">
      <div class="close-icon__wrapper" @click="exitHandler">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div class="form-container">
        <div class="edit-form">
          <div class="edit-title">성별 선택</div>
          <div @click="maleClicked" :class="[maleActive, 'gender-list']">남성</div>
          <div @click="femaleClicked" :class="[femaleActive, 'gender-list']">여성</div>
        </div>
        <div>
          <v-btn
            class="main-large-btn"
            elevation="0"
            color="#41D8E6"
            type="submit"
            @click="saveHandler"
            :disabled="!clicked"
            >저장</v-btn
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { updateSubUserData } from "@/api/user/index";

export default {
  props: { subName: String },
  components: {
    CompleteAlert,
    ErrorModal,
  },
  data() {
    return {
      gender: "",
      clicked: false,
      maleBtn: false,
      femaleBtn: false,
      showCompleteAlert: false,
      showErrModal: false,
      content: "성별이 성공적으로 <br/> 변경되었습니다!",
      error: "변경에 실패했습니다. <br/> 다시 시도해주세요.",
    };
  },
  computed: {
    maleActive() {
      return this.maleBtn ? "active-btn" : false;
    },
    femaleActive() {
      return this.femaleBtn ? "female-active " : false;
    },
  },
  mounted() {
    // console.log("===!====", this.$route.params.id);
    // console.log("sub user is", this.$store.state.subUserGender);
  },
  methods: {
    maleClicked() {
      if (this.maleBtn === true) {
        this.maleBtn = false;
        this.clicked = false;
      } else if (this.maleBtn === false && this.femaleBtn === true) {
        this.maleBtn = true;
        this.femaleBtn = false;
        this.clicked = true;
      } else {
        this.maleBtn = true;
        this.clicked = true;
      }
    },
    femaleClicked() {
      if (this.femaleBtn === true) {
        this.femaleBtn = false;
        this.clicked = false;
      } else if (this.femaleBtn === false && this.maleBtn === true) {
        this.femaleBtn = true;
        this.maleBtn = false;
        this.clicked = true;
      } else {
        this.femaleBtn = true;
        this.clicked = true;
      }
    },
    saveHandler() {
      this.updateUserGender();
    },
    async updateUserGender() {
      const clickedGender = this.maleBtn ? "m" : "f";
      // console.log(clickedGender);
      if (this.$store.state.subUserGender !== clickedGender && this.clicked) {
        try {
          const gender = { gender: clickedGender };
          const id = this.$route.params.id;
          const res = await updateSubUserData(id, gender);
          if (res.status === 200) {
            this.showCompleteAlert = true;
          }
        } catch (e) {
          console.log(e);
          this.showErrModal = true;
        }
      } else {
        this.showErrModal = true;
      }
    },
    exitHandler() {
      this.$emit("genderClose", false);
    },
    isConfirmed(clicked) {
      // console.log(clicked);
      this.showCompleteAlert = false;
      this.$emit("genderConfirmed", true);
    },
    isClicked(boolean) {
      this.showErrModal = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  width: 100%;
  height: 100%;
}

.name-edit__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 55px;
  img {
    width: 25px;
  }
}

.form-container {
  width: 100%;
}
.edit-form {
  background-color: #ededed;
  border-radius: 10px;
  margin-bottom: 10px;
}

.edit-title {
  font-size: 14px;
  color: #646464;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 10px 0;
}

.gender-list {
  color: #000000;
  font-size: 20px;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 15px 0;
}
.gender-list:last-child {
  padding-bottom: 2vh;
  border: none;
}
.active-btn {
  background-color: #c8c8c8;
}
.female-active {
  background-color: #c8c8c8;
  border-radius: 0 0 10px 10px;
}

// .gender-list:hover {
//   background-color: #c8c8c8;
// }
// .gender-list:last-child:hover {
//   border-radius: 0 0 10px 10px;
// }
.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #c9f4f8 !important;
}

::v-deep .v-text-field .v-label {
  font-size: 12px;
  color: #41d8e6;
}

::v-deep .v-text-field {
  margin-top: 0;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 16px !important;
    letter-spacing: -0.03em;
    line-height: 15px !important;
  }
}
::v-deep .v-input input {
  font-size: 16px !important;
  line-height: 23px;
  text-align: center !important;
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
