<template>
  <div>
    <div class="background-dim">
      <CompleteAlert
        :content="content"
        :btnText="this.$i18n.t('confirm_btn')"
        v-show="showCompleteAlert"
        @isConfirmed="isConfirmed"
      />
      <ErrorModal v-show="showErrModal" :error="error" @isClicked="btnClicked" />
      <div class="name-edit__wrapper">
        <div class="close-icon__wrapper" @click="exitHandler">
          <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
        </div>
        <div class="form-wrapper">
          <div class="reasons">
            <div class="resons-title">{{ $t("relationships") }}</div>
            <div class="reasons-list" v-for="(reason, idx) in reasons" :key="idx" @click="unClickHandler(idx)">
              {{ reason.value }}
            </div>
            <div class="reasons-list" @click="clickHandler">
              <div>{{ $t("etc") }}</div>
              <v-textarea
                v-model="input"
                v-show="isClicked"
                class="textarea__wrapper"
                :placeholder="this.$i18n.t('input_nickname')"
                outlined
                name="input-7-4"
              ></v-textarea>
            </div>
          </div>
        </div>
        <div>
          <v-btn
            @click="submitHandler"
            class="main-large-btn"
            elevation="0"
            color="#41D8E6"
            type="submit"
            :disabled="!clicked"
            >{{ $t("confirm_btn") }}</v-btn
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { updateSubUserData } from "@/api/user/index";

export default {
  components: { CompleteAlert, ErrorModal },
  data() {
    return {
      isClicked: false,
      showCompleteAlert: false,
      showErrModal: false,
      clicked: false,
      input: "",
      reasons: [
        { type: "parents", value: this.$i18n.t("parents") },
        { type: "spouse", value: this.$i18n.t("spouse") },
        { type: "child", value: this.$i18n.t("child") },
        { type: "friend", value: this.$i18n.t("friend") },
      ],
      selected: "",
      content: this.$i18n.t("change_relationship_success"),
      error: this.$i18n.t("change_birth_error"),
    };
  },
  methods: {
    clickHandler() {
      this.selected = this.$i18n.t("etc");
      // console.log(this.selected);
      this.isClicked = true;
    },
    unClickHandler(idx) {
      this.selected = this.reasons[idx].type;
      // console.log(this.selected);
      this.isClicked = false;
      this.clicked = true;
    },
    submitHandler() {
      this.updateSubUserType();
    },
    exitHandler() {
      this.$emit("typeClose", false);
    },
    isConfirmed(clicked) {
      this.showCompleteAlert = false;
      this.$emit("typeConfirmed", true);
    },
    btnClicked() {
      this.showErrModal = false;
    },
    async updateSubUserType() {
      if (this.$store.state.subUserType !== this.selected) {
        try {
          const select = this.selected.toLowerCase();
          const type = { user_type: select };
          const id = this.$route.params.id;
          const res = await updateSubUserData(id, type);
          if (res.status === 200) {
            this.showCompleteAlert = true;
          }
        } catch (e) {
          console.log(e);
          this.showErrModal = true;
        }
      } else {
        this.showErrModal = true;
      }
    },
    typeValidation(type) {
      if (/^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,10}$/.test(type)) return true;
      else return false;
    },
  },
  watch: {
    input(newVal) {
      // console.log("validation:", this.typeValidation(newVal));
      if (this.typeValidation(newVal)) {
        this.selected = newVal;
      }
      this.typeValidation(newVal) ? (this.clicked = true) : (this.clicked = false);
    },
  },
};
</script>

<style lang="scss" scoped>
.background-dim {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  width: 100%;
  height: 100%;
}
.name-edit__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 55px;
  img {
    width: 25px;
  }
}

.form-container {
  width: 100%;
}

.form-wrapper {
  width: 100%;
}

.reasons {
  background-color: #ededed;
  border-radius: 10px;
  margin-bottom: 10px;
}

.resons-title {
  font-size: 14px;
  color: #646464;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 10px 0;
}

.reasons-list {
  color: #000000;
  font-size: 18px;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 15px 0;
}

.reasons-list:last-child {
  border-radius: 0 0 10px 10px;
}

.reasons-list:last-child {
  padding-bottom: 2vh;
  border: none;
}
.reasons-list:last-child:hover {
  border-radius: 0 0 10px 10px;
}

.reasons-list:hover {
  background-color: #c8c8c8;
}
.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}
.textarea__wrapper {
  margin: 0 10px;
}
::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  color: #c9f4f8 !important;
  opacity: 1;
}

::v-deep .v-text-field .v-label {
  font-size: 12px;
  color: #41d8e6;
}

::v-deep .v-text-field {
  margin-top: 10px;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 16px !important;
    letter-spacing: -0.03em;
    line-height: 15px !important;
  }
}
::v-deep .v-input input {
  font-size: 16px !important;
  line-height: 23px;
  text-align: center !important;
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

::v-deep
  .theme--light.v-text-field--outlined:not(.v-input--is-focused):not(.v-input--has-state)
  > .v-input__control
  > .v-input__slot
  fieldset {
  background-color: #fff !important;
}

::v-deep .v-application--is-ltr .v-text-field--outlined fieldset {
  padding-left: 0 !important;
  background-color: #fff !important;
}
::v-deep .v-text-field__details {
  display: none;
}

::v-deep .v-application .pa-3 {
  padding: 15px 10px 0 10px !important;
}

::v-deep .v-input__slot {
  margin: 0 !important;
}

::v-deep .v-text-field.v-text-field--enclosed {
  background-color: #fff;
}
</style>
