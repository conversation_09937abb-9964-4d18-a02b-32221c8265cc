<template>
  <div class="subname__wrapper">
    <CompleteAlert
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-show="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
    <div class="name-edit__wrapper">
      <div class="close-icon__wrapper" @click="exitHandler">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div class="form-container">
        <div class="edit-form">
          <div class="edit-title">{{ $t("profile_name") }} {{ $t("edit") }}</div>
          <div class="input__wrapper">
            <v-text-field
              v-model="name"
              @change="nameInputHandler"
              color="#41d8e6"
              :placeholder="subName"
              :error-messages="nameError"
              :persistent-placeholder="true"
            ></v-text-field>
          </div>
        </div>
        <div>
          <v-btn
            class="main-large-btn"
            elevation="0"
            color="#41D8E6"
            type="submit"
            @click="saveHandler"
            :disabled="valid"
            >저장</v-btn
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";
import { updateSubUserData } from "@/api/user/index";

export default {
  props: { subName: String },
  components: {
    CompleteAlert,
    ErrorModal,
  },
  data() {
    return {
      name: "",
      nameError: "",
      valid: true,
      showCompleteAlert: false,
      showErrModal: false,
      content: "✏️ 이름이 성공적으로 <br/> 변경되었습니다!",
      error: "변경에 실패했습니다. <br/> 이름을 확인해주세요.",
    };
  },
  watch: {
    name(newVal) {
      // console.log("name validation:", this.nameValidation(newVal));
      if (this.name.length > 1 || this.nameValidation(newVal)) {
        this.nameError = "";
        this.valid = false;
      } else {
        this.nameError = "10글자 이내의 한글이나 영문, 숫자로 정해주세요.";
        this.valid = true;
      }
    },
  },
  methods: {
    nameValidation(name) {
      if (/^[ㄱ-ㅎ|가-힣|a-z|A-Z|0-9|]{2,20}$/.test(name)) return true;
      else return false;
    },
    nameInputHandler() {
      // console.log(this.name);
    },
    saveHandler() {
      this.updateUsername();
    },
    async updateUsername() {
      if (this.$store.state.subUser !== this.name) {
        try {
          const name = { nickname: this.name };
          const id = this.$route.params.id;
          const res = await updateSubUserData(id, name);
          // console.log(res);
          if (res.status === 200) {
            this.showCompleteAlert = true;
          }
        } catch (e) {
          console.log(e);
          this.showErrModal = true;
        }
      } else {
        this.showErrModal = true;
      }
    },
    exitHandler() {
      this.$emit("closeBtn", false);
    },
    isConfirmed(clicked) {
      // console.log(clicked);
      this.showCompleteAlert = false;
      this.$emit("isConfirmed", true);
    },
    isClicked() {
      this.showErrModal = false;
    },
  },

  mounted() {
    // console.log(this.subName);
    this.name = this.subName;
  },
};
</script>

<style lang="scss" scoped>
.subname__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  width: 100%;
  height: 100%;
}

.name-edit__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;
  top: 55px;
  img {
    width: 25px;
  }
}

.form-container {
  width: 100%;
}
.edit-form {
  background-color: #ededed;
  border-radius: 10px;
  margin-bottom: 10px;
}

.edit-title {
  font-size: 14px;
  color: #646464;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 10px 0;
}

.input__wrapper {
  color: #000000;
  font-size: 18px;
  background-color: #fff;
  padding: 0 15px;
  border-radius: 0 0 10px 10px;
  display: flex;
}

.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  color: #c9f4f8 !important;
  opacity: 1;
}

::v-deep .v-text-field .v-label {
  // top: -10px !important;
  font-size: 12px;
  color: #41d8e6;
}

::v-deep .v-text-field {
  margin-top: 0;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 16px !important;
    letter-spacing: -0.03em;
    line-height: 15px !important;
  }
}
::v-deep .v-input input {
  font-size: 16px !important;
  line-height: 23px;
  text-align: center !important;
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}

// ::v-deep .v-text-field__slot {
//   font-size: 12px;
// }
.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
