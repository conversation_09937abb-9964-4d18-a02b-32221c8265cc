<template>
  <div>
    <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
    <div v-show="deleteConfirm" class="bg-modal">
      <div class="close-icon__wrapper" @click="exitHandler">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div class="alert-window">
        <div class="alert-window__content" v-html="content"></div>
        <div class="btn__wrapper">
          <div class="left-btn" @click="nextHandler">{{ $t("yes") }}</div>
          <div class="right-btn" @click="cancelHandler">{{ $t("no") }}</div>
        </div>
      </div>
    </div>
    <div v-show="confirmed" class="bg-modal">
      <div class="alert-window">
        <div class="alert-window__content" v-html="confirmContent"></div>
        <div class="confirm__wrapper">
          <div class="alert-window__btn" @click="closeAlertHandler">{{ $t("confirm_btn") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { deleteSubUser } from "@/api/user/index";
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  props: { subjectId: Number },
  components: { ErrorModal },
  data() {
    return {
      content: this.$i18n.t("delete_sub_modal"),
      confirmContent: this.$i18n.t("success_delete_sub_modal"),
      error: this.$i18n.t("error_delete_sub_modal"),
      showErrModal: false,
      deleteConfirm: true,
      confirmed: false,
    };
  },
  methods: {
    nextHandler() {
      this.deleteConfirm = false;
      this.deleteSubUser();
    },
    cancelHandler() {
      this.$emit("cancel", false);
    },
    exitHandler() {
      this.$emit("closeDeleteForm", false);
    },
    closeAlertHandler() {
      this.$router.go(-1);
    },
    isClicked() {
      this.showErrModal = false;
    },
    async deleteSubUser() {
      try {
        // const id = this.$route.params.id;
        const res = await deleteSubUser(this.subjectId);
        // console.log(res);
        if (res.status === 204) {
          this.confirmed = true;
          sessionStorage.setItem("selectUser", 0);
          this.$store.commit("setSelectUser", 0);
        }
      } catch (e) {
        console.log(e);
        this.showErrModal = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.close-icon__wrapper {
  position: absolute;
  left: 30px;
  top: 55px;
  img {
    width: 25px;
  }
}
.alert-window {
  background-color: #fff;
  width: 100%;
  height: 180px;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  padding: 30px 15px;
}

.alert-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alert-window__content {
  display: flex;
  font-size: 18px;
  color: #323232;
}

.btn__wrapper {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  height: 70px;
}
.confirm__wrapper {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  height: 60px;
}

.left-btn {
  width: 45%;
  height: 50px;
  background: #c8c8c8;
  border-radius: 5px;
  margin: 30px 8px 0 8px;
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
  color: #fff;
}
.right-btn {
  width: 45%;
  height: 50px;
  background: #41d8e6;
  border-radius: 5px;
  margin: 30px 8px 0 8px;
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
  color: #fff;
}
.alert-window__btn {
  width: 100%;
  padding: 0px 20px;
  padding-top: 10px;
  text-align: right;
  font-size: 16px;
  font-weight: 700;
  color: #41d8e6;
}
</style>
