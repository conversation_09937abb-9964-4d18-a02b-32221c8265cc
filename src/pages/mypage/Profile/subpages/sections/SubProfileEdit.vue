<template>
  <div>
    <div v-if="loaded" class="container">
      <div class="contents__wrapper">
        <GenderForm :initialGender="initialGender" @genderInputHandler="genderInputHandler" />
        <!-- <div class="name-form__wrapper">
          <NameForm :name="name" />
        </div> -->
        <div class="form__wrapper">
          <BirthForm :initialBirth="initialBirth" @birthInputHandler="birthInputHandler" />
        </div>
        <div class="form__wrapper">
          <HeightForm :initialHeight="initialHeight" @heightInputHandler="heightInputHandler" />
        </div>
        <!-- <div class="form__wrapper">
          <WeightForm :weight="weight" />
        </div> -->
        <div class="form__wrapper">
          <WeightGoalForm :initialWeight="initialWeight" @weightGoalInputHandler="weightGoalInputHandler" />
        </div>
        <div class="form__wrapper">
          <WaterGoalFormVue :initialWater="initialWater" @waterGoalInputHandler="waterGoalInputHandler" />
        </div>
        <div class="form__wrapper">
          <SubTypeEdit :initialWater="initialWater" @userTypeHandler="userTypeHandler" />
        </div>
      </div>
      <div class="save-btn__wrapper">
        <v-btn
          elevation="0"
          color="#41D8E6"
          type="submit"
          :disabled="!isValid"
          @click="submitHandler"
          class="save-btn"
          >{{ $t("save") }}</v-btn
        >
      </div>
    </div>
    <Loading v-if="loading" />
    <div class="snackbar">
      <v-snackbar v-model="saveSuccess" timeout="2000">{{ succesContent }}</v-snackbar>
      <v-snackbar v-model="saveFailed" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import GenderForm from "@/components/Forms/GenderForm.vue";
// import NameForm from "@/components/Forms/NameForm.vue";
import BirthForm from "@/components/Forms/BirthForm.vue";
import HeightForm from "@/components/Forms/HeightForm.vue";
// import WeightForm from "@/components/Forms/WeightForm.vue";
import WeightGoalForm from "@/components/Forms/WeightGoalForm.vue";
import WaterGoalFormVue from "@/components/Forms/WaterGoalForm.vue";
import SubTypeEdit from "@/components/Forms/SubTypeForm.vue";

import { fetchGetSubjectInfo, updateSubjectInfo } from "@/api/user/index";
import Loading from "@/components/Common/Loading.vue";

export default {
  components: {
    GenderForm,
    // NameForm,
    BirthForm,
    HeightForm,
    // WeightForm,
    WeightGoalForm,
    WaterGoalFormVue,
    SubTypeEdit,
    Loading,
  },
  data() {
    return {
      loading: true,
      loaded: false,
      saveSuccess: false,
      saveFailed: false,
      succesContent: this.$i18n.t("save_success"),
      failContent: this.$i18n.t("save_fail"),
      initialGender: "",
      gender: "",
      initialBirth: "",
      birth: "",
      initialHeight: 0,
      height: 0,
      initialWeight: 0,
      goalWeight: 0,
      initialWater: 0,
      goalWater: 0,
    };
  },
  methods: {
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId = subjects !== null ? subjects[selectedId].id : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async getBasicData() {
      this.loading = true;
      try {
        const subjectId = this.getSubjectId();
        // console.log(selectUser > 0);
        const { data } = await fetchGetSubjectInfo(subjectId);
        // console.log(data.subject);
        const userData = data.subject;
        this.initialGender = userData.sex;
        this.initialHeight = userData.metadata.height || 0;
        this.initialWeight = userData.targetWeight || 0;
        this.initialWater = userData.targetWater;
        this.initialBirth = userData.birth;
        // console.log(this.initialBirth);
        this.loading = false;
        this.loaded = true;
      } catch (e) {
        console.log(e);
        this.loading = false;
        this.loaded = true;
      }
    },
    genderInputHandler(gender) {
      // console.log(gender);
      this.gender = gender;
    },
    birthInputHandler(inputBirth) {
      // console.log(inputBirth);
      this.birth = inputBirth.birth;
    },
    heightInputHandler(inputHeight) {
      // console.log(inputHeight);
      this.height = inputHeight.height;
    },
    weightGoalInputHandler(inputGoalWeight) {
      // console.log(inputGoalWeight);
      // console.log(inputGoalWeight.goal);
      this.goalWeight = inputGoalWeight.goal;
    },
    waterGoalInputHandler(goalWater) {
      // console.log(goalWater);
      this.goalWater = goalWater;
    },
    userTypeHandler(type) {
      // console.log(type);
    },
    submitHandler() {
      // console.log("submit");
      const userData = {};
      this.gender !== this.initialGender ? (userData.sex = this.gender) : null;
      this.birth !== this.initialBirth ? (userData.birth = this.birth) : null;
      this.height !== this.initialHeight ? (userData.height = Number(this.height)) : null;
      this.goalWeight !== this.initialWeight ? (userData.targetWeight = Number(this.goalWeight)) : null;
      this.goalWater !== this.initialWater ? (userData.targetWater = Number(this.goalWater)) : null;
      // console.log(userData);
      this.updateUserData(userData);
    },
    async updateUserData(userData) {
      try {
        const subjectId = this.getSubjectId();
        const { data, status } = await updateSubjectInfo(subjectId, userData);
        // console.log(data, status);
        if (status === 200) {
          this.saveSuccess = true;
          this.getBasicData();
        }
      } catch (e) {
        this.saveFailed = true;
        console.log(e);
      }
    },
  },
  computed: {
    isValid() {
      return (
        this.gender !== this.initialGender ||
        this.birth !== this.initialBirth ||
        this.height !== this.initialHeight ||
        this.goalWeight !== this.initialWeight ||
        this.goalWater !== this.initialWater
      );
    },
  },
  mounted() {
    this.getBasicData();
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 40px 0;
  display: flex;
}

.contents__wrapper {
  height: 70vh;
  overflow: auto;
  padding: 0 30px 30px;
}

.form__wrapper {
  margin-bottom: 40px;
}
.name-form__wrapper {
  margin-top: 30px;
}

.save-btn__wrapper {
  margin: 0 auto;
  width: 100%;
  max-width: 640px;
  min-width: 320px;
  display: flex;
  justify-content: center;
  position: absolute;
  padding: 0 30px;
  bottom: 30px;
}

.save-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

::v-deep .v-text-field {
  margin: 0;
  padding: 0;
}
::v-deep .v-text-field input {
  padding: 5px 0 5px;
}

// snackbar custom
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
</style>
