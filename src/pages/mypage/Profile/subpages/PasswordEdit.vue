<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <FormSubTitle :currentStateIdx="currentStateIdx" />
    <CurrentPasswordForm v-if="!passNextState" @nextStateHandler="nextStateHandler" />
    <PasswordForm v-else />
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import FormSubTitle from "@/components/Mypage/Common/FormSubTitle.vue";
import CurrentPasswordForm from "@/components/Forms/CurrentPasswordForm.vue";
import PasswordForm from "@/components/Forms/PasswordForm.vue";

export default {
  components: {
    HeaderNav,
    FormSubTitle,
    CurrentPasswordForm,
    PasswordForm,
  },
  data() {
    return {
      pageName: this.$i18n.t("profile_pwd_title"),
      currentStateIdx: 2,
      passNextState: false,
    };
  },
  methods: {
    nextStateHandler() {
      this.passNextState = true;
    },
  },
};
</script>

<style lang="scss" scoped></style>
