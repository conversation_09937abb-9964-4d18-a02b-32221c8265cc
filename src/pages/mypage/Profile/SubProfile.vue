<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div v-if="loaded" class="profile-main-container">
      <NameForm v-show="showNameForm" :subName="userName" @closeBtn="closeBtn" @isConfirmed="isConfirmed" />
      <!-- <GenderForm v-show="showGenderForm" @genderConfirmed="genderConfirmed" @genderClose="genderClose" />
      <BirthForm v-show="showBirthForm" @birthConfirmed="birthConfirmed" @birthClose="birthClose" />
      <TypeForm v-show="showTypeForm" @typeConfirmed="typeConfirmed" @typeClose="typeClose" /> -->
      <SubUserImageUpload :profileImg="profile_img" />
      <SubUserCategories
        :username="userName"
        @nameClicked="nameClicked"
        @genderClicked="genderClicked"
        @birthClicked="birthClicked"
        @typeClicked="typeClicked"
        @deleteSubUser="deleteSubUser"
        :gender="gender"
        :birth="birth"
        :type="type"
      />
      <DeleteForm v-show="showDeleteForm" @closeDeleteForm="closeDeleteForm" @cancel="cancel" />
    </div>
    <Loading v-if="loading" />
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import SubUserImageUpload from "@/components/Mypage/ProfileView/SubUserImageUpload.vue";
import SubUserCategories from "@/components/Mypage/ProfileView/SubUserCategories.vue";
import NameForm from "./subpages/SubNameEdit.vue";
// import GenderForm from "./subpages/SubGenderEdit.vue";
// import BirthForm from "./subpages/SubBirthEdit.vue";
// import TypeForm from "./subpages/SubTypeEdit.vue";
import DeleteForm from "./subpages/DeleteSubUser.vue";
import { fetchGetSubjectInfo } from "@/api/user/index";
import Loading from "@/components/Common/Loading.vue";

export default {
  name: "Myprofile",
  components: {
    HeaderNav,
    SubUserCategories,
    SubUserImageUpload,
    NameForm,
    // GenderForm,
    // BirthForm,
    // TypeForm,
    DeleteForm,
    Loading,
  },
  data() {
    return {
      loading: true,
      loaded: false,
      profile_img: "",
      pageName: this.$i18n.t("profile_title"),
      showNameForm: false,
      showGenderForm: false,
      showBirthForm: false,
      showTypeForm: false,
      showDeleteForm: false,
      userName: "",
      gender: "",
      birth: "",
      type: "",
    };
  },
  computed: {},
  mounted() {
    this.getSubUser();
    this.pageName = this.$i18n.t("profile_title");
    // console.log(this.$route.params);
  },

  methods: {
getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId = subjects !== null ? subjects[selectedId].id : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async getSubUser() {
      try {
        const subjectId = this.getSubjectId();
        const { data } = await fetchGetSubjectInfo(subjectId);
        // console.log(data);

        if (data) {
          const userData = data.subject;
          this.profile_img = userData.image || require("@/assets/images/mypage-icon/profile.png");
          this.userName = userData.nickname;

          this.$store.commit("getUsername", userData.nickname);
          // console.log(this.$store.state.username);
          // this.gender = userData.gender === "f" ? this.$i18n.t("female") : this.$i18n.t("male");
          // this.birth = userData.birth
          //   .split("-")
          //   .toString()
          //   .replace(/,/g, ".");

          // this.type = this.typeLocalization(data.subUserType.type);
          // this.$store.commit("setSubUserType", data.subUserType.type);
        }
        this.loading = false;
        this.loaded = true;
      } catch (error) {
        this.loading = false;
        this.loaded = true;
        console.log(error);
      }
    },
    // typeLocalization(type) {
    //   switch (type) {
    //     case "parents":
    //       return this.$i18n.t("parents");
    //     case "spouse":
    //       return this.$i18n.t("spouse");
    //     case "child":
    //       return this.$i18n.t("child");
    //     case "friend":
    //       return this.$i18n.t("friend");
    //   }
    // },

    nameClicked() {
      this.showNameForm = true;
    },
    genderClicked() {
      this.showGenderForm = true;
    },
    birthClicked() {
      this.showBirthForm = true;
    },
    typeClicked() {
      this.showTypeForm = true;
    },
    deleteSubUser() {
      // console.log("clicked");
      this.showDeleteForm = true;
    },
    closeBtn() {
      this.showNameForm = false;
    },
    genderClose() {
      this.showGenderForm = false;
    },
    birthClose() {
      this.showBirthForm = false;
    },
    typeClose() {
      this.showTypeForm = false;
    },
    closeDeleteForm() {
      this.showDeleteForm = false;
    },
    cancel() {
      this.showDeleteForm = false;
    },
    isConfirmed() {
      this.showNameForm = false;
      this.getSubUser();
    },
    genderConfirmed() {
      this.showGenderForm = false;
      this.getSubUser();
    },
    birthConfirmed() {
      this.showBirthForm = false;
      this.getSubUser();
    },
    typeConfirmed() {
      this.showTypeForm = false;
      this.getSubUser();
    },
  },
};
</script>

<style scoped>
.profile-main-container {
  height: 100%;
}
</style>
