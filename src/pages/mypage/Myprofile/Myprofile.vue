<template>
  <div :class="{ 'main ios-area-top': isIos === true, main: isIos === false }">
    <div class="xs-mobile mypage-bg">
      <!-- =============================================== -->
      <!-- 📍 header area -->
      <HeaderComponent :pageName="pageName" />
      <!-- =============================================== -->
      <!-- 📍 content area -->
      <!-- image upload function -->
      <div class="profile_box">
        <div class="profile-img">
          <img
            :src="profile_img"
            class="profile-img-tg"
            @error="replaceImage"
          />
          <div class="profile-uploadBtn-wrapper">
            <label for="upload-btn">
              <v-icon v-if="!clicked">$exam_off</v-icon>
              <v-icon v-else>$exam_on</v-icon>
            </label>
            <input
              id="upload-btn"
              type="file"
              accept="image/png, image/jpeg, image/jpg"
              @change="imgFileHandler"
              @click="clickHandler"
            />
          </div>
        </div>
      </div>
      <ProfileComponent
        :username="username"
        :usermail="usermail"
        :userphone="userphone"
        :isSns="isSns"
      />
    </div>
    <Loader :loading="loading" v-if="loading" />
  </div>
</template>

<script>
import HeaderComponent from "@/components/Mypage/Common/HeaderComponent";
import ProfileComponent from "@/components/Mypage/Myprofile/ProfileComponent";
import Loader from "@/components/_common/Loader";
import { fetchGetUserInfo } from "@/api/user/index";
import axios from "axios";
import { cym702 } from "@/api";

export default {
  name: "Myprofile",
  components: {
    HeaderComponent,

    ProfileComponent,
    Loader,
  },
  data() {
    return {
      pageName: this.$i18n.t("profile_title"),
      loading: false,
      profile_img: "",
      username: "",
      usermail: "",
      userphone: "",
      mymenu: false,
      clicked: false,
      isSns: false,
    };
  },
  computed: {
    allCheck() {
      return this.confirmPouch && this.confirmCapture && this.confirmBright;
    },
  },

  methods: {
    imgFileHandler(e) {
      this.profile_img = URL.createObjectURL(e.target.files[0]);

      // console.log(e.target.files[0]);
      this.fetchImageUpload(e.target.files[0]);
    },
    clickHandler() {
      this.clicked = true;
    },
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null
          ? subjects[selectedId].id
          : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async fetchImageUpload(image) {
      this.loading = true;
      try {
        const subjectId = this.getSubjectId();
        let formData = new FormData();
        formData.append("image", image);
        // console.log(formData);

        const { data } = await cym702.patch(
          `/subjects/${subjectId}/image`,
          formData,
          {
            headers: {
              "content-type": "multipart/form-data",
            },
          }
        );
        this.loading = false;

        // console.log(data);
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
    async loadData() {
      try {
        this.loading = true;
        const res = await fetchGetUserInfo();
        this.loading = false;
        if (res.data.data) {
          const userData = res.data.data[0];
          this.profile_img = userData.img_url;
          this.username = userData.username;
          this.usermail = userData.email;
          this.userphone = userData.userphone;
          this.isSns = userData.sns === "false" ? false : true;

          // 전화번호 형식 변경(기본 11자리 기준)
          this.userphone =
            this.userphone.substring(0, 3) +
            "-" +
            this.userphone.substring(3, 5) +
            "**-" +
            this.userphone.substring(7, 9) +
            "**";
        }
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },

    gobackHome() {
      this.$router.push({ path: "/home" });
    },

    checkPouch() {
      if (this.confirmPouch) {
        this.confirmPouch = false;
      } else {
        this.confirmPouch = true;
      }
    },
    checkCapture() {
      if (this.confirmCapture) {
        this.confirmCapture = false;
      } else {
        this.confirmCapture = true;
      }
    },
    checkBright() {
      if (this.confirmBright) {
        this.confirmBright = false;
      } else {
        this.confirmBright = true;
      }
    },
  },
  replaceImage(e) {
    e.target.src = require("@/assets/images/mypage-icon/profile.png");
  },
  mounted() {
    this.loadData();
    this.pageName = this.$i18n.t("profile_title");
  },
};
</script>

<style scoped></style>
