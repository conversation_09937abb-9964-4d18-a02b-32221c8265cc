<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="container">
      <!-- <div class="title">⏱ 시간 설정</div> -->

      <div class="time-section">
        <div class="time-section__btns">
          <v-btn-toggle v-model="timeSelectToggle">
            <v-btn> {{ $t("am") }} </v-btn>
            <v-btn> {{ $t("pm") }} </v-btn>
          </v-btn-toggle>
        </div>
        <div class="time-section__inputs">
          <v-dialog ref="dialog" v-model="modal2" :return-value.sync="time" persistent width="290px">
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="time"
                label="시간선택"
                prepend-icon="mdi-clock-time-four-outline"
                readonly
                v-bind="attrs"
                v-on="on"
              ></v-text-field>
            </template>
            <v-time-picker v-if="modal2" v-model="time" color="#41D8E6" full-width>
              <v-spacer></v-spacer>
              <v-btn text color="#41D8E6" @click="modal2 = false">
                Cancel
              </v-btn>
              <v-btn text color="#41D8E6" @click="$refs.dialog.save(time)">
                OK
              </v-btn>
            </v-time-picker>
          </v-dialog>
        </div>
      </div>

      <div class="title">반복설정</div>

      <div class="div-flex">
        <div>요일 반복</div>
        <div>
          <v-switch inset v-model="dayCycleMode" @click="setDayCycleMode"></v-switch>
        </div>
      </div>
      <v-btn-toggle v-model="daySelectToggle" multiple>
        <v-btn> 월 </v-btn>
        <v-btn> 화 </v-btn>
        <v-btn> 수 </v-btn>
        <v-btn> 목 </v-btn>
        <v-btn> 금 </v-btn>
        <v-btn> 토 </v-btn>
        <v-btn> 일 </v-btn>
      </v-btn-toggle>
      <div class="div-flex">
        <div>매월 반복</div>
        <div>
          <v-switch inset v-model="monthCycleMode" @click="setMonthCycleMode"></v-switch>
        </div>
      </div>
      <v-select v-model="day" :items="items" outlined></v-select>
    </div>
    <v-btn @click="saveUrineTestTimeAlert">저장하기</v-btn>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
export default {
  name: "ExamAlertSettings",
  components: {
    HeaderNav,
  },
  data() {
    return {
      pageName: this.$i18n.t("test_alert_settings"),
      time: null,
      menu2: false,
      modal2: false,
      monthCycleMode: false,
      dayCycleMode: false,
      day: 1,
      timeSelectToggle: 0,
      daySelectToggle: 0,
      items: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
    };
  },

  methods: {
    setDayCycleMode() {
      if (this.monthCycleMode) {
        this.monthCycleMode = false;
      }
    },
    setMonthCycleMode() {
      if (this.dayCycleMode) {
        this.dayCycleMode = false;
      }
    },

    returnDayKoreanName(daySelectToggleNumber) {
      if (daySelectToggleNumber === 0) return "월";
      if (daySelectToggleNumber === 1) return "화";
      if (daySelectToggleNumber === 2) return "수";
      if (daySelectToggleNumber === 3) return "목";
      if (daySelectToggleNumber === 4) return "금";
      if (daySelectToggleNumber === 5) return "토";
      if (daySelectToggleNumber === 6) return "일";
    },

    saveUrineTestTimeAlert() {
      /*global Webview*/
      /*eslint no-undef: "error"*/

      if (this.dayCycleMode || this.monthCycleMode) {
        let message = {
          action: "setUrineTestTimeAlert",
          cycle: this.monthCycleMode ? "month" : "day",
          days: this.monthCycleMode ? this.day : this.returnDayKoreanName(this.daySelectToggle),
          hour: this.time.slice(0, 2),
          min: this.time.slice(3, 5),
        };

        // console.log(message);

        Webview.setUrineTestAlarm(message);
      }
    },
  },
};
</script>

<style scoped>
.container {
  padding: 40px 30px;
}
a {
  color: black;
}
.div-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-section {
  display: flex;
  justify-content: space-evenly;
}

.title {
  text-align: left;
  padding: 10px 0px;
}

::v-deep .v-input {
  max-width: 164px;
}

::v-deep .theme--light.v-btn-toggle:not(.v-btn-toggle--group) .v-btn.v-btn {
  border-color: rgba(0, 0, 0, 0) !important;
  background-color: #ededed;
  border-radius: 5px;
}

::v-deep .v-btn-toggle .v-btn.v-btn.v-size--default {
  min-width: 35px;
  max-width: 35px;
  width: 35px;
}

::v-depp .v-btn-toggle:not(.v-btn-toggle--dense) .v-btn.v-btn.v-size--default {
  height: 35px !important;
}

::v-deep .v-btn-toggle {
  border-radius: 5px;
}

::v-deep .v-btn__content {
  color: #a7a7a7;
  font-size: 20px;
  font-weight: 500;
}

::v-deep .v-btn:before {
  border: 2px solid #41d8e6 !important;
  background-color: #fff;
}

::v-deep .theme--light.v-btn--active:hover::before,
.theme--light.v-btn--active::before {
  opacity: 1;
}

::v-deep [data-v-330bd01f] .theme--light.v-btn-toggle:not(.v-btn-toggle--group) .v-btn.v-btn {
  background-color: #fff;
}

::v-deep .v-btn-toggle:not(.v-btn-toggle--dense) .v-btn.v-btn.v-size--default {
  width: 10vw;
  height: 10vw;
  /* margin: 0 5px; */
}

::v-deep .v-item-group {
  width: 100%;
  height: 100%;
  justify-content: space-between;
}
</style>
