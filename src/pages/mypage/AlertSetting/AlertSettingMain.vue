<template>
  <div>
    <Loading v-if="loading" />
    <ConfirmModal
      v-if="toggleClicked"
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      :createdAt="createdAt"
      @isConfirmed="isConfirmed"
    />
    <CheckModal
      v-if="showCheckModal"
      :content="checkContent"
      :leftBtnTxt="this.$i18n.t('no')"
      :rightBtnTxt="this.$i18n.t('yes')"
      @cancelHandler="cancelHandler"
      @confirmHandler="confirmHandler"
    />
    <HeaderNav :pageName="pageName" />
    <div class="settings__wrapper">
      <div class="pushAlert__container">
        <div class="alert-header">{{ pushAlert }}</div>
        <div class="icon__wrapper">
          <v-switch
            color="#41d8e6"
            height="20px"
            v-model="isPushAlertSettingMode"
            inset
            @click="setPushAlertSetting"
          ></v-switch>
        </div>
      </div>
      <!-- <p>{{ $t("push_description") }}</p> -->
      <div class="underline"></div>

      <div class="pushAlert__container">
        <div class="alert-header">{{ notificationAlert }}</div>
        <div class="icon__wrapper">
          <v-switch
            color="#41D8E6"
            v-model="isNotificationAlertMode"
            inset
            @click="setNotificationAlert"
            :disabled="!isPushAlertSettingMode"
          ></v-switch>
        </div>
      </div>
      <div class="pushAlert__container">
        <div class="alert-header">{{ marketingAlertMode }}</div>
        <div class="icon__wrapper">
          <v-switch
            color="#41D8E6"
            v-model="isMarketingAlertMode"
            inset
            @click="setMarketingAlert"
            :disabled="!isPushAlertSettingMode"
          ></v-switch>
        </div>
      </div>
      <p
        class="alert-description"
        v-html="this.$i18n.t('alert_description')"
      ></p>
    </div>
  </div>
</template>

<script>
import Loading from "@/components/Common/Loading.vue";
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import ConfirmModal from "@/components/Common/ConfirmModal.vue";
import CheckModal from "@/components/Common/CheckModal.vue";

export default {
  components: {
    HeaderNav,
    ConfirmModal,
    CheckModal,
    Loading,
  },
  data() {
    return {
      loaded: false,
      loading: false,
      isKo: true,
      pageName: this.$i18n.t("setting_notification"),
      isPushAlertSettingMode: false,
      pushAlert: this.$i18n.t("setting_push"),
      isNotificationAlertMode: false,
      notificationAlert: this.$i18n.t("setting_noti"),
      isMarketingAlertMode: false,
      marketingAlertMode: this.$i18n.t("setting_marketing"),
      content: "",
      createdAt: "",
      toggleClicked: false,
      showCheckModal: false,
      checkContent: "",
      topic: "",
    };
  },

  methods: {
    setPushAlertSetting() {
      // 2. (앱 알림 on) isPushAlertSettingMode === true 일 때, fcm권한 없으면 권한 요청 webview 함수 실행
      this.loading = true;
      if (this.isPushAlertSettingMode) {
        if (this.fcmPermission) {
          Webview.registerFcm();
          this.topic === "all";
        } else Webview.requestPermissionFcm();
      } else {
        Webview.unRegisterFcm();
        this.topic === "all";
      }
    },
    setNotificationAlert() {
      if (this.isPushAlertSettingMode) {
        if (this.isNotificationAlertMode) {
          this.topic = this.$i18n.t("topic_notice");
          Webview.subscribeTopic(`notice-${this.$i18n.locale}`);
        } else {
          this.topic = this.$i18n.t("topic_notice");
          this.notiHandler();
        }
      }
    },
    setMarketingAlert() {
      if (this.isPushAlertSettingMode) {
        if (this.isMarketingAlertMode) {
          // 구독
          this.topic = this.$i18n.t("topic_marketing");
          Webview.subscribeTopic(`marketing-${this.$i18n.locale}`);
        } else {
          // 해제
          this.topic = this.$i18n.t("topic_marketing");
          this.notiHandler();
        }
      }
    },
    notiHandler() {
      this.checkContent = this.isKo
        ? `${this.topic} ${this.$i18n.t("confirm_disagree_modal")}`
        : `${this.$i18n.t("confirm_disagree_modal")} ${this.topic}?`;
      this.showCheckModal = true;
    },
    cancelHandler() {
      this.showCheckModal = false;
      this.loadNotiStatus();
    },
    confirmHandler() {
      this.loading = true;
      if (this.topic === this.$i18n.t("topic_notice")) {
        Webview.unsubscribeTopic(`notice-${this.$i18n.locale}`);
      } else if (this.topic === this.$i18n.t("topic_marketing")) {
        Webview.unsubscribeTopic(`marketing-${this.$i18n.locale}`);
      }
    },
    allSubscribeHandler() {
      this.loading = false;
      if (this.topic === this.$i18n.t("topic_notice")) {
        localStorage.setItem("notice", true);
        this.showCheckModal = false;
        this.toggleClicked = true;
        this.content = this.isKo
          ? `${this.$i18n.t("notice_agree_modal")}`
          : `${this.$i18n.t("notice_agree_modal")} ${this.topic}.`;
        this.getCurTime();
      } else if (this.topic === this.$i18n.t("topic_marketing")) {
        localStorage.setItem("marketing", true);
        this.showCheckModal = false;
        this.toggleClicked = true;
        this.content = this.isKo
          ? `${this.$i18n.t("marketing_agree_modal")}`
          : `${this.$i18n.t("marketing_agree_modal")} ${this.topic}.`;
        this.getCurTime();
      } else {
        this.isNotificationAlertMode = true;
        this.isMarketingAlertMode = true;
        localStorage.setItem("push", true);
        localStorage.setItem("marketing", true);
        localStorage.setItem("notice", true);
        this.content = this.$i18n.t("notice_marketing_agree_modal");
        this.getCurTime();
        this.toggleClicked = true;
      }
    },
    allUnsubscribeHandler() {
      this.loading = false;
      if (this.topic === this.$i18n.t("topic_notice")) {
        localStorage.setItem("notice", false);
        this.showCheckModal = false;
        this.toggleClicked = true;
        this.content = this.isKo
          ? `${this.topic} ${this.$i18n.t("notice_disagree_modal")}`
          : `${this.$i18n.t("notice_disagree_modal")} ${this.topic}.`;
        this.getCurTime();
      } else if (this.topic === this.$i18n.t("topic_marketing")) {
        localStorage.setItem("marketing", false);
        this.showCheckModal = false;
        this.toggleClicked = true;
        this.content = this.isKo
          ? `${this.topic} ${this.$i18n.t("notice_disagree_modal")}`
          : `${this.$i18n.t("notice_disagree_modal")} ${this.topic}.`;
        this.getCurTime();
      } else {
        this.isNotificationAlertMode = false;
        this.isMarketingAlertMode = false;
        localStorage.setItem("push", false);
        localStorage.setItem("marketing", false);
        localStorage.setItem("notice", false);
        this.content = this.$i18n.t("notice_marketing_disagree_modal");
        this.getCurTime();
        this.toggleClicked = true;
      }
    },
    isConfirmed() {
      this.toggleClicked = false;
      this.loading = true;
      this.loadNotiStatus();
    },
    getCurTime() {
      const unixTime = new Date().getTime();
      const curLocalTime = new Date(unixTime);
      const year = curLocalTime.getFullYear();
      const month = curLocalTime.getMonth();
      const day = curLocalTime.getDate();
      let hh = curLocalTime.getHours();
      let mm = curLocalTime.getMinutes();
      let isAm = hh < 12 ? true : false;
      hh = hh < 10 ? `0${hh}` : hh;
      mm = mm < 10 ? `0${mm}` : mm;
      this.createdAt = isAm
        ? `${year}.${month + 1}.${day} ${hh}:${mm} AM`
        : `${year}.${month + 1}.${day} 0${hh - 12}:${mm} PM`;
    },
    loadNotiStatus() {
      this.isPushAlertSettingMode = JSON.parse(localStorage.getItem("push"));
      this.isNotificationAlertMode = JSON.parse(localStorage.getItem("notice"));
      this.isMarketingAlertMode = JSON.parse(localStorage.getItem("marketing"));
      this.loading = false;
    },

    resultHandler(res) {
      // alert(res.action);
      this.loading = true;
      if (res.action === "REQ-IS-AUTHORIZED-FCM") {
        // 1. mount 시 fcm 권한 조회 및 값 저장
        res.result === false
          ? (this.fcmPermission = false)
          : (this.fcmPermission = true);
      } else if (res.action === "REQ-PREMISSION-FCM") {
        if (res.result === "SUCCESS") {
          this.content === this.$i18n.t("alert_setting_fail_message");
          this.toggleClicked = true;
        } else {
          this.setPushAlertSetting();
        }
      } else if (res.action === "REQ-REGISTER-DEVICE-FCM") {
        if (res.result === "SUCCESS") {
          // 3. topic 등록 webview 함수 실행: message = "topicName"
          Webview.subscribeTopic(`marketing-${this.$i18n.locale}`);
          Webview.subscribeTopic(`notice-${this.$i18n.locale}`);
        } else {
          this.content === this.$i18n.t("alert_setting_fail_message");
          this.toggleClicked = true;
        }
      } else if (res.action === "REQ-UNREGISTER-DEVICE-FCM") {
        if (res.result === "SUCCESS") {
          Webview.unsubscribeTopic(`marketing-${this.$i18n.locale}`);
          Webview.unsubscribeTopic(`notice-${this.$i18n.locale}`);
        } else {
          this.content === this.$i18n.t("alert_setting_fail_message");
          this.toggleClicked = true;
        }
      } else if (res.action === "REQ-SUBSCRIBE-TOPIC-FCM") {
        if (res.result === "SUCCESS") this.allSubscribeHandler();
      } else if (res.action === "REQ-UNSUBSCRIBE-TOPIC-FCM") {
        if (res.result === "SUCCESS") this.allUnsubscribeHandler();
      }
      this.loading = false;
    },

    // TODO
    // 1. mount 시 fcm 권한 조회 및 값 저장
    // 2. (앱 알림 on) isPushAlertSettingMode === true 일 때, fcm권한 없으면 권한 요청 webview 함수 실행
    // 3. topic 등록 webview 함수 실행: message = "topicName"
    // - 앱알림 on: notice, marketing topic subscribe
    // - 공지사항 on/off: notice topic subscribe/unsubscribe
    // - 마케팅 on/off: notice, marketing topic subscribe/unsubscribe
    listener() {
      window.addEventListener("message", (e) => {
        const data = JSON.parse(e.data);
        // alert(e.data);
        this.resultHandler(data.payload);
      });
      document.addEventListener("message", (e) => {
        const data = JSON.parse(e.data);
        // alert(e.data);
        this.resultHandler(data.payload);
      });
    },
    initialSetting() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      this.loading = true;
      this.listener();
      // 1. mount 시 fcm 권한 조회 및 값 저장
      Webview.getFcmAuthority();
    },
  },
  mounted() {
    this.initialSetting();
    this.loadNotiStatus();
    this.isKo = this.$i18n.locale.includes("ko");
  },
};
</script>

<style lang="scss" scoped>
.settings__wrapper {
  width: 100%;
  padding: 40px 30px;
  letter-spacing: -0.03em;
}

.pushAlert__container {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
}

.alert-header {
  height: 100%;
  display: flex;
  justify-content: space-between;
  color: #000000;
  font-weight: 500;
  font-size: 18px;
}

p {
  font-size: 14px;
  color: #646464;
  text-align: left;
  margin: 5px 0 10px;
}

.icon__wrapper {
  display: flex;
  width: 40px;
  height: 30px;
  align-items: center;
  justify-content: flex-end;
}

.underline {
  border-bottom: 0.5px solid #a7a7a7;
}

a {
  color: #000;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}

.alert-description {
  margin: 20px 0;
  padding: 23px 13px;
  font-size: 14px;
  letter-spacing: -0.03em;
  background-color: #f8f8f8;
  border-radius: 10px;
}
</style>
