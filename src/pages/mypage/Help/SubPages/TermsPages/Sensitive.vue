<template>
  <div class="container">
    <HeaderNav :pageName="pageName" />
    <div class="contents__wrapper" v-html="contents"></div>
  </div>
</template>
<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";

export default {
  name: "MypageNotices",
  components: { HeaderNav },
  data() {
    return {
      pageName: this.$i18n.t("sensitive_terms"),
      contents: this.$i18n.t("terms_sensitive_contents"),
    };
  },
};
</script>

<style scoped>
.container {
  width: 100%;
  height: 100%;
  text-align: left;
}

.contents__wrapper {
  width: 100%;
  font-size: 16px;
  /* height: 100%; */
  overflow-y: scroll;
  padding: 40px 30px;
}
</style>
