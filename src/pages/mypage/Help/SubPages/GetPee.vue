<template>
  <div class="accordion-container">
    <Accordion>
      <AccordionItem v-for="(item, idx) in accordion" :key="idx">
        <template class="accordion-menu" slot="accordion-trigger">
          <div class="question-title">
            Q
            <div class="accordion-title" v-html="item.title"></div>
          </div>
        </template>
        <template slot="accordion-content">
          <span v-html="item.content"></span>
        </template>
      </AccordionItem>
    </Accordion>
  </div>
</template>

<script>
import Accordion from "@/components/Common/Accordion.vue";
import AccordionItem from "@/components/Common/AccordionItem.vue";
export default {
  name: "GetPee",
  components: { Accordion, AccordionItem },
  data() {
    return {
      accordion: [
        {
          title: this.$i18n.t("what_time_test_title"),
          content: this.$i18n.t("what_time_test"),
        },
        {
          title: this.$i18n.t("can_reuse_title"),
          content: this.$i18n.t("can_reuse"),
        },
      ],
    };
  },
};
</script>

<style lang="scss">
.question-title {
  font-family: "GilroyMedium";
  width: 95%;
  height: 100%;
  color: #41d9e6;
  font-size: 20px;
  display: flex;
  align-items: flex-start;
  text-align: left;
  .accordion-title {
    // font-family: "Noto Sans KR";
    font-size: 18px;
    letter-spacing: -0.03em;
    display: flex;
    color: #000000;
    margin-left: 10px;
    padding-bottom: 2px;
  }
}

.fold {
  width: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

img {
  width: 100%;
}
</style>
