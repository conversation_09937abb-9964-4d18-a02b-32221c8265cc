<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="tab-container">
      <v-tabs
        v-model="tabName"
        color="#C9F4F8"
        class="history-tabs pa-0"
        center-active
        grow
        @change="changeTabs"
        mobile-breakpoint="xs"
        slider-color="#41D8E6"
        slider-size="3"
        height="40px"
      >
        <v-tab class="tab-title px-0 mx-0" href="#tab-beforeUseage" v-html="this.$i18n.t('before_use')"></v-tab>
        <v-tab class="tab-title px-0 mx-0" href="#tab-getPee" v-html="this.$i18n.t('peeing')"></v-tab>
        <v-tab class="tab-title px-0 mx-0" href="#tab-takeaPhoto" v-html="this.$i18n.t('photo')"></v-tab>
        <v-tab class="tab-title px-0 mx-0" href="#tab-result" v-html="this.$i18n.t('result')"></v-tab>
      </v-tabs>
    </div>
    <v-tabs-items v-model="tabName" color="transparent">
      <v-tab-item value="tab-beforeUseage" class="tab-item"><BeforeUseage /></v-tab-item>
      <v-tab-item value="tab-getPee" class="tab-item"><GetPee /></v-tab-item>
      <v-tab-item value="tab-takeaPhoto" class="tab-item"><TakeaPhoto /></v-tab-item>
      <v-tab-item value="tab-result" class="tab-item"><Result /></v-tab-item>
    </v-tabs-items>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import BeforeUseage from "./BeforeUseage.vue";
import GetPee from "./GetPee.vue";
import TakeaPhoto from "./TakeaPhoto.vue";
import Result from "./Result.vue";

export default {
  name: "FAQ",
  components: { HeaderNav, BeforeUseage, GetPee, TakeaPhoto, Result },
  data() {
    return {
      pageName: this.$i18n.t("faq"),
      tabName: "tab-beforeUseage",
      currentTabIndex: "",
      count: "recent",
    };
  },
  methods: {
    changeTabs(tabIndex) {
      this.currentTabIndex = tabIndex;
    },
  },
  created() {
    window.scrollTo(0, 0);
    let tab = this.$route.query.tab;

    if (tab === 0) {
      this.tabName = "tab-beforeUseage";
    } else if (tab == 1) {
      this.tabName = "tab-getPee";
    } else if (tab == 2) {
      this.tabName = "tab-takeaPhoto";
    } else if (tab == 3) {
      this.tabName = "tab-result";
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  /* text-align: left; */
}

::v-deep .v-tabs-slider-wrapper {
  color: #41d8e6;
  height: 3px !important;
}

::v-deep .v-tabs-slider {
  border-radius: 3px 3px 0px 0px !important;
}

::v-deep .v-tab {
  color: rgba(0, 0, 0, 0.3) !important;
  font-weight: 500 !important;
  line-height: 40px;
  letter-spacing: -0.05em !important;
  text-transform: none !important;
  font-size: 18px !important;
}
::v-deep .v-tab--active {
  font-size: 18px !important;
  font-weight: 700 !important;
  color: rgba(0, 0, 0, 0.87) !important;
}

::v-deep .v-tabs-bar__content {
  background: transparent !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

::v-deep .theme--light.v-tabs > .v-tabs-bar {
  background-color: transparent !important;
}

::v-deep .v-toolbar__content {
  height: auto !important;
}

::v-deep .v-tab {
  min-width: 60px !important;
  background-color: transparent !important;
}

// tab ripple 제거
::v-deep .v-ripple__container {
  display: none !important;
}

::v-deep .v-input--selection-controls__ripple {
  display: none !important;
}

::v-deep .v-tab:before {
  display: none !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

::v-deep .v-tabs--grow > .v-tabs-bar .v-tab {
  flex: 1 0 0;
  font-size: 16px !important;
}

.v-slide-group__wrapper {
  padding: 10px;
}

.tab-menu {
  font-weight: 700;
  /* color: rgba(0, 0, 0, 0.3); */
}

.tab-container {
  background-color: #c9f4f8;
  padding: 20px 30px 0 30px;
}

.tab-menu__wrapper {
  width: 100%;
  font-size: 16px;
  font-weight: 700;
  /* padding: 0 30px;; */

  /* height: 100%; */
  /* overflow-y: scroll; */
  /* padding: 30px 0; */
}

.accordion-container {
  padding: 20px 30px;
}

.accordion-menu {
  width: 100%;
  padding: 30px;
  border-bottom: 0.5px solid #a7a7a7;
}

.question-title {
  font-family: "GilroyMedium";
  color: #41d9e6;
  font-size: 22px;
  margin-right: 10px;
  align-items: flex-start;
}

.accordion-title {
  font-size: 18px;
  font-weight: 400;
  display: flex;
}
</style>
