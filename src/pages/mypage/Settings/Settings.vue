<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <CheckModal
      v-if="showLogoutModal"
      :content="content"
      :leftBtnTxt="this.$i18n.t('no')"
      :rightBtnTxt="this.$i18n.t('yes')"
      @cancelHandler="cancelHandler"
      @confirmHandler="confirmHandler"
    />
    <div class="settings__container">
      <div class="menu-items">
        <router-link to="/mypage/settings/account">
          <div class="menu-card">
            <div class="menu-card-header">
              <div class="menu-card-title">{{ $t("account_info") }}</div>
              <div class="icon__wrapper">
                <img
                  src="@/assets/images/chevron_right.png"
                  alt="right arrow"
                />
              </div>
            </div>
          </div>
        </router-link>
        <!-- <router-link to="/mypage/settings/sns">
          <div class="menu-card">
            <div class="menu-card-header">
              <div class="menu-card-title">{{ $t("sns_login_settings") }}</div>
              <div class="icon__wrapper">
                <img src="@/assets/images/chevron_right.png" alt="right arrow" />
              </div>
            </div>
          </div>
        </router-link> -->
        <div class="menu-item" @click="logoutModalHandler">
          <div class="menu-right">{{ $t("setting_logout") }}</div>
          <div class="icon__wrapper">
            <img src="@/assets/images/chevron_right.png" alt="right arrow" />
          </div>
        </div>
      </div>

      <div class="menu-card">
        <div v-for="(menu, idx) in menus" :key="idx">
          <router-link :to="{ path: menu.path }" class="menu-card-header">
            <div class="left-menu__wrapper">
              <div class="menu-card-title">
                {{ menu.title }}
              </div>
              <div class="tag" v-if="menu.isUpdate">N</div>
            </div>
            <div class="right-menu__wrapper">
              <!-- <div class="version-txt" v-show="idx === 1">v{{ curVersion }}</div> -->
              <div class="icon__wrapper">
                <img
                  src="@/assets/images/chevron_right.png"
                  alt="right arrow"
                />
              </div>
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import CheckModal from "@/components/Common/CheckModal.vue";
import { logout } from "@/api/user";

export default {
  components: {
    HeaderNav,
    CheckModal,
  },
  data() {
    return {
      showLogoutModal: false,
      pageName: this.$i18n.t("setting_header_title"),
      content: this.$i18n.t("setting_logout_confirm_txt"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
      curVersion: (this.curVersion =
        localStorage.getItem("curVersion") || "1.0.0"),
      recentVersion: (this.curVersion =
        localStorage.getItem("recentVersion") || "1.0.0"),
      menus: [
        {
          title: this.$i18n.t("setting_notification"),
          path: "/mypage/settings/alert",
          isUpdate: false,
        },
        // { title: this.$i18n.t("setting_version_txt"), path: "/mypage/settings/version", isUpdate: false },
        {
          title: this.$i18n.t("open_source"),
          path: "/mypage/settings/opensource",
          isUpdate: false,
        },
        // { title: this.$i18n.t("setting_delete_account"), path: "/mypage/settings/delete", isUpdate: false },
      ],
    };
  },

  methods: {
    logoutModalHandler() {
      this.showLogoutModal = true;
    },
    async confirmHandler(fromChild) {
      // console.log(fromChild);
      const { data, status } = await logout();
      const snsType = localStorage.getItem("snsType");

      console.log(data);

      if (status === 204) {
        snsType === "google" && Webview.googleLogout();

        this.$store.commit("LOGOUT");
      }
    },
    cancelHandler(fromChild) {
      // console.log(fromChild);
      this.showLogoutModal = false;
    },
    logoutRequest() {
      // console.log("logout");
      /*global Webview*/
      /*eslint no-undef: "error"*/
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.settings__container {
  padding: 40px 30px;
}
.menu-items {
  // margin: auto;
  // margin: 50px 30px 0px 30px;
  padding-bottom: 20px;
  border-bottom: 0.5px solid #a7a7a7;
}

.underline {
  border-bottom: 0.5px solid #a7a7a7;
}
.menu-item {
  display: flex;
  justify-content: space-between;
  // margin-bottom: 20px;
}

.menu-right {
  font-weight: 500;
  font-size: 18px;
  line-height: 23px;
}

.menu-card {
  width: 100%;
}

.menu-card-header {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
}

.left-menu__wrapper {
  display: flex;
  height: 100%;
  gap: 5px;
  align-items: center;
  justify-content: center;
}

.menu-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
}

.menu-sub-card {
  height: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.menu-sub-title {
  font-size: 16px;
  color: #646464;
  margin-left: 10px;
  line-height: 20px;
}

.v-input--selection-controls {
  margin: 0;
  padding: 0;
}

.right-menu__wrapper {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.icon__wrapper {
  display: flex;
  width: 20px;
  align-items: center;
  justify-content: flex-end;
  img {
    width: 10px;
  }
}

.menu-card-content {
  padding-left: 10px;
  color: #646464;
  font-size: 14px;
  line-height: 23px;
  /* margin-bottom: 10px; */
}

.version-txt {
  font-family: GilroyMedium;
  font-size: 18px;
  color: #41d8e6;
}

.tag {
  background-color: #ee0000;
  color: #ffffff;
  border-radius: 100%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  font-weight: 600;
  // line-height: 17px;
  padding-right: 1px;
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}
</style>
