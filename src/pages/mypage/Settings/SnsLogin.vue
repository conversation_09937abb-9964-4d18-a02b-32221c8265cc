<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="container">
      <div class="toggle__wrapper">
        <div class="toggle-title">카카오</div>
        <div class="switch__wrapper">
          <v-switch class="pa-1 mt-0 mr-2" color="#41d8e6" height="20px" inset></v-switch>
        </div>
      </div>
      <div class="toggle__wrapper">
        <div class="toggle-title">구글</div>
        <div class="switch__wrapper">
          <v-switch class="pa-1 mt-0 mr-2" color="#41d8e6" height="20px" inset></v-switch>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";

export default {
  components: {
    HeaderNav,
  },
  data() {
    return { pageName: "소셜 로그인 계정관리" };
  },
};
</script>

<style scoped>
.container {
  width: 100%;
  padding: 40px 30px;
}
.toggle__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.toggle-title {
  color: #000000;
  font-size: 16px;
  font-weight: 500;
}

.switch__wrapper {
  display: flex;
  width: 40px;
  align-items: center;
  justify-content: flex-end;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}
</style>
