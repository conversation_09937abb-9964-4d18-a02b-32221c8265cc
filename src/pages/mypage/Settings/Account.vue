<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="settings__container">
      <div class="menu-items">
        <router-link to="/profile/phone">
          <div class="menu-card-header">
            <div class="menu-card-title">{{ $t("profile_phone") }}</div>
            <div class="menu-right">
              <span class="user-info-phone">{{ phoneNumber }}</span>
              <div class="icon__wrapper">
                <img src="@/assets/images/chevron_right.png" alt="right arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <router-link to="/profile/password">
          <div class="menu-card-header">
            <div class="menu-card-title">{{ $t("input_pwd") }}</div>
            <div class="menu-right">
              <span class="user-info-phone">{{ $t("change") }}</span>
              <div class="icon__wrapper">
                <img src="@/assets/images/chevron_right.png" alt="right arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <router-link to="/mypage/settings/delete">
          <div class="menu-card-header">
            <div class="menu-card-title">{{ $t("setting_delete_account") }}</div>
            <div class="menu-right">
              <!-- <span class="user-info-phone">{{ $t("change") }}</span> -->
              <div class="icon__wrapper">
                <img src="@/assets/images/chevron_right.png" alt="right arrow" />
              </div>
            </div>
          </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import { getMainUserInfo } from "@/api/user/index";

export default {
  components: { HeaderNav },
  data() {
    return {
      pageName: this.$i18n.t("account_info"),
      phoneNumber: "",
    };
  },
  methods: {
    async getUserInfo() {
      const { data } = await getMainUserInfo();
      // console.log(data);
      this.phoneNumber = data.phone;
    },
  },
  mounted() {
    this.getUserInfo();
  },
};
</script>

<style lang="scss" scoped>
.settings__container {
  padding: 40px 30px;
}
.menu-items {
  // margin: auto;
  // margin: 50px 30px 0px 30px;
  // padding-bottom: 20px;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  // margin-bottom: 20px;
}

.menu-right {
  font-weight: 500;
  font-size: 18px;
  line-height: 23px;
  display: flex;
}

.menu-card {
  width: 100%;
}

.menu-card-header {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
}

.left-menu__wrapper {
  display: flex;
  height: 100%;
  gap: 5px;
  align-items: center;
  justify-content: center;
}

.icon__wrapper {
  display: flex;
  width: 20px;
  align-items: center;
  justify-content: flex-end;
  img {
    width: 10px;
  }
}

.menu-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
}

.user-info {
  font-family: GilroyMedium;
  font-weight: 500;
  font-size: 20px;
  margin-right: 5px;
  color: #41d8e6;
}

.user-info-phone {
  font-family: GilroyMedium;
  font-size: 20px;
  font-weight: 500;
  margin-right: 5px;
  color: #41d8e6;
}
</style>
