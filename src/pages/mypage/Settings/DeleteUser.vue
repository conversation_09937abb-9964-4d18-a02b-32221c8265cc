<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="delete-contents__wrapper">
      <div class="danger-contents__wrapper">
        <div class="img__wrapper">
          <img src="@/assets/images/mypage-icon/delete_icon.png" alt="delete" />
        </div>
        <div class="danger-contents">{{ $t("delete_text") }}</div>
      </div>
      <div class="notice-box">
        <div v-for="(notice, idx) in notices" :key="idx" class="notice">
          <div>❗</div>
          {{ notice }}
        </div>
      </div>
    </div>
    <div class="bottom-contents__wrapper">
      <div class="delete-reason-box">
        <p>
          {{ $t("delete_reason") }}
        </p>
        <div @click="formHandler" class="drop-dowm">
          <div class="drop-dowm-text">{{ $t("plz_select") }}</div>
          <div class="drop-dowm-img__wrapper">
            <img src="@/assets/images/mypage-icon/dropdown_arrow.png" alt="arrow" />
          </div>
        </div>
        <!-- <v-row align="center">
          <v-col class="d-flex" cols="12" sm="8">
            <v-select
              :items="reasons"
              :menu-props="{ top: true}"
              label="선택해주세요"
              dense
              outlined
            ></v-select>
          </v-col>
        </v-row> -->
      </div>
    </div>
    <div class="btn__wrapper">
      <button class="agree-btn">{{ $t("yes") }}</button>
      <button class="disagree-btn">{{ $t("no") }}</button>
    </div>
    <ReasonForm v-if="showForm" :showHandler="showHandler" @closeBtn="closeBtn" />
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import ReasonForm from "./ReasonForm.vue";

export default {
  components: {
    HeaderNav,
    ReasonForm,
  },
  data() {
    return {
      pageName: this.$i18n.t("setting_delete_account"),
      showForm: false,
      name: "",
      notices: [
        this.$i18n.t("delete_description1"),
        this.$i18n.t("delete_description2"),
        this.$i18n.t("delete_description3"),
      ],
    };
  },
  mounted() {
    this.name = this.$store.state.username;
  },
  methods: {
    formHandler() {
      this.showForm = true;
    },
    showHandler() {
      this.showForm = false;
    },
    closeBtn() {
      this.showForm = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.delete-contents__wrapper {
  padding: 30px 30px 50px 30px;
  background-color: #c9f4f8;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
}

.danger-contents__wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.danger-contents {
  text-align: left;
  color: #646464;
  font-size: 18px;
  font-weight: 500;
  padding-left: 20px;
  letter-spacing: -0.03em;
  display: flex;
  align-items: center;
}

.img__wrapper {
  width: 45px;
  display: flex;
  align-items: center;
  img {
    width: 100%;
    object-fit: contain;
  }
}

.notice-box {
  width: 100%;
  background-color: #fff;
  height: 140px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  letter-spacing: -0.03em;
}

.notice {
  width: 85%;
  color: #000000;
  margin: 2px 0;
  text-align: left;
  display: flex;
  font-size: 16px;
}

.bottom-contents__wrapper {
  padding: 30px;
}

.delete-reason-box {
  text-align: left;
  padding: 10px 30px;
  p {
    color: #000000;
    font-size: 16px;
    letter-spacing: -0.03em;
  }
}

.drop-dowm {
  width: 95%;
  height: 40px;
  border-radius: 10px;
  border: 1px solid #a7a7a7;
  padding: 8px 15px;
  display: flex;
  justify-content: space-between;
}

.drop-dowm-text {
  color: #a7a7a7;
  font-size: 14px;
}

.drop-dowm-img__wrapper {
  width: 14px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 100%;
    object-fit: contain;
  }
}

.btn__wrapper {
  margin: 0 auto;
  width: 100%;
  max-width: 640px;
  min-width: 320px;
  display: flex;
  justify-content: center;
  position: absolute;
  padding: 0 30px;
  bottom: 50px;
}
.agree-btn {
  width: 50%;
  // max-width: 140px;
  height: 50px;
  background: #c8c8c8;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  color: #ffffff;
  font-weight: 700;
  font-size: 20px;
  margin-right: 10px;
}
.disagree-btn {
  width: 50%;
  // max-width: 140px;
  height: 50px;
  background: #41d8e6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  color: #ffffff;
  font-weight: 700;
  font-size: 20px;
  margin-left: 10px;
}

.background-dim:after {
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.save-btn__wrapper {
  margin: 0 auto;
  width: 100%;
  max-width: 640px;
  min-width: 320px;
  display: flex;
  justify-content: center;
  position: absolute;
  padding: 0 30px;
  bottom: 50px;
}

.save-btn {
  background: #41d8e6;
  // z-index: 5;
  width: 100%;
  height: 50px;
  border-radius: 10px;
  color: #ffffff;
  font-weight: 700;
  font-size: 20px;
}
</style>
