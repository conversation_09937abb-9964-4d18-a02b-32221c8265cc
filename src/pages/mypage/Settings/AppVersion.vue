<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="version-page__wrapper">
      <div class="cym-logo__wrapper">
        <img src="@/assets/images/cym702_logo/Cym702.png" alt="Cym702" />
      </div>
      <div v-if="isUpdate" class="version-description">새로운 버전으로 업데이트 하세요</div>
      <p class="cur-version">{{ $t("cur_version") }} {{ curVersion }}</p>
      <button v-if="isUpdate">{{ $t("version") }} 1.0.1 {{ $t("update") }}</button>
    </div>
    <!-- <button @click="getVersion">get version</button> -->
    <p class="bottom-description" v-if="isIos">{{ $t("supported") }} iOS 13.0 {{ $t("above") }}</p>
    <p class="bottom-description" v-else>{{ $t("supported") }} Android 8.0 {{ $t("above") }}</p>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";

export default {
  components: { HeaderNav },
  data() {
    return {
      pageName: this.$i18n.t("setting_version_txt"),
      isUpdate: false,
      curVersion: localStorage.getItem("curVersion") || "1.0.0",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
};
</script>

<style lang="scss" scoped>
.version-page__wrapper {
  width: 100%;
  height: 100%;
  padding: 15vh 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.cym-logo__wrapper {
  width: 60vw;

  img {
    width: 100%;
  }
}

.version-description {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.03em;
  margin-top: 20px;
}

p {
  margin-top: 10px;
  font-size: 12px;
}

button {
  width: 100%;
  background-color: #41d8e6;
  color: #ffffff;
  max-width: 300px;
  height: 50px;
  border-radius: 10px;
  font-weight: 700;
  font-size: 20px;
  margin-top: 10px;
}

.cur-version {
  font-size: 14px;
}

.bottom-description {
  font-size: 14px;
  position: absolute;
  bottom: 12vh;
  // left: 35vw;
  width: 100%;
  color: #646464;
}
</style>
