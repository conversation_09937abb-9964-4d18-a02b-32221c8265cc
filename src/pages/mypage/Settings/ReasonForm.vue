<template>
  <div>
    <div class="bg-modal">
      <ErrorModal v-show="showErrModal" :error="error" @isClicked="isClicked" />
      <div class="close-icon__wrapper" @click="exitHandler">
        <img src="@/assets/images_assets/icons/wht-circle-ic.png" />
      </div>
      <div v-show="deleteConfirm" class="alert-window">
        <div class="alert-window__content" v-html="content"></div>
        <div class="btn__wrapper">
          <div class="left-btn" @click="cancelHandler">{{ $t("no") }}</div>
          <div class="right-btn" @click="nextHandler">{{ $t("yes") }}</div>
        </div>
      </div>
      <div class="form-wrapper" v-show="showReasonForm">
        <div class="reasons">
          <div class="resons-title">{{ $t("delete_account_plz_select") }}</div>
          <div
            class="reasons-list"
            v-for="(reason, idx) in reasons"
            :key="idx"
            @click="unClickHandler(reason.id)"
          >
            {{ reason.content }}
          </div>
          <div class="reasons-list" @click="inputReasonHandler">
            {{ $t("delete_input") }}
            <v-textarea
              v-model="customReason"
              @change="inputReason"
              v-if="inputClicked"
              class="pa-3 pb-0"
              outlined
              name="input-7-4"
            ></v-textarea>
          </div>
        </div>
      </div>
      <div class="save-btn__wrapper" v-show="showReasonForm">
        <v-btn
          class="main-large-btn"
          elevation="0"
          color="#41D8E6"
          type="submit"
          @click="submitHandler"
          :disabled="!btnClicked"
          >{{ $t("save") }}</v-btn
        >
        <!-- <button @click="submitHandler" class="save-btn">저장</button> -->
      </div>
    </div>
  </div>
</template>

<script>
import { deleteUser } from "@/api/user/index";
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  props: {
    resons: Array,
    showHandler: Function,
  },
  components: { ErrorModal },
  data() {
    return {
      content: this.$i18n.t("delete_account_confirm_txt"),
      error: "변경에 실패했습니다. <br/> 다시 시도해주세요.",
      inputClicked: false,
      btnClicked: false,
      deleteConfirm: false,
      showErrModal: false,
      showReasonForm: true,
      reason: 0,
      customReason: "",
      reasons: [
        { id: 1, content: this.$i18n.t("not_used") },
        { id: 2, content: this.$i18n.t("difficult_use") },
        { id: 3, content: this.$i18n.t("test_anymore") },
        { id: 4, content: this.$i18n.t("delete_records") },
      ],
    };
  },
  methods: {
    inputReasonHandler() {
      this.reason = 5;
      // console.log(this.reason);
      this.inputClicked = true;
    },
    unClickHandler(id) {
      this.reason = id;
      // console.log(this.reason);
      this.inputClicked = false;
      this.btnClicked = true;
    },
    submitHandler() {
      this.showReasonForm = false;
      this.deleteConfirm = true;
      // console.log("click");
    },
    inputReason() {
      // console.log(this.customReason);
    },
    exitHandler() {
      this.$emit("closeBtn", false);
    },
    nextHandler() {
      this.deleteConfirm = false;
      // console.log(this.reason, this.customReason);
      this.deleteAccount();
    },
    cancelHandler() {
      this.deleteConfirm = false;
      this.$emit("closeBtn", false);
    },
    isClicked() {
      this.showErrModal = false;
      this.showReasonForm = true;
    },
    reasonValidation(reason) {
      if (0 < reason.length < 255 && reason !== "") return true;
      else return false;
    },
    async deleteAccount() {
      try {
        /**  항목 선택 시 **/
        if (5 > this.reason > 0) {
          const reason = { reason: String(this.reason) };
          // console.log(reason);
          const res = await deleteUser(
            String(this.reasons[this.reason - 1].content)
          );
          // console.log(res);
          if (res.status === 204) {
            // this.$store.commit("setIsWithdrawal");
            localStorage.setItem("isWithdrawal", true);
            this.$store.commit("LOGOUT");
            // store에 모달 boolean 값 만들고 true로 commit 해주기
            // 데이터 삭제 후, 로그인 화면으로 이동 시 성공 모달 표시
          } else {
            this.showErrModal = true;
          }
        } else if (this.reason === 5) {
          /** 기타의견 선택 시 */
          // 입력받은 값 빈값이면 api호출 X
          if (this.reasonValidation(this.customReason)) {
            const reason = { reason: this.customReason };
            // console.log(reason);
            const res = await deleteUser(this.customReason);
            // console.log(res);
            if (res.status === 200) {
              // this.$store.commit("setIsWithdrawal");
              localStorage.setItem("isWithdrawal", true);
              this.$store.commit("LOGOUT");
              // console.log("success");
            } else {
              this.showErrModal = true;
            }
          } else {
            this.showErrModal = true;
          }
        } else {
          this.showErrModal = true;
        }
      } catch (e) {
        console.log(e.response);
        this.showErrModal = true;
      }
    },
  },
  watch: {
    customReason(newVal) {
      // console.log(this.reasonValidation(newVal));
      this.customReason = newVal;
      this.btnClicked = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px 15px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.close-icon__wrapper {
  text-align: left;
  position: absolute;

  top: 55px;
  left: 30px;
  img {
    width: 25px;
  }
}
.form-wrapper {
  width: 100%;
  max-width: 640px;
  min-width: 320px;
  padding: 0 30px;
  position: absolute;
  bottom: 110px;
  flex-direction: column;
  display: flex;
  justify-content: center;
}

.alert-window {
  background-color: #fff;
  width: 100%;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 15px 25px 15px;
}

.alert-window__content {
  font-size: 18px;
  padding-top: 15px;
}

.reasons {
  background-color: #ededed;
  border-radius: 10px;
}

.resons-title {
  font-size: 14px;
  color: #646464;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 10px 0;
}

.reasons-list {
  color: #000000;
  font-size: 18px;
  border-bottom: 0.5px solid #a7a7a7;
  padding: 15px 0;
}

.reasons-list:last-child {
  padding-bottom: 2vh;
  border: none;
}
.reasons-list:last-child:hover {
  border-radius: 0 0 10px 10px;
}

.reasons-list:hover {
  background-color: #c8c8c8;
}

.save-btn__wrapper {
  margin: 0 auto;
  width: 100%;
  max-width: 640px;
  min-width: 320px;
  display: flex;
  justify-content: center;
  position: absolute;
  padding: 0 30px;
  bottom: 50px;
}

.main-large-btn {
  width: 100%;
  // min-height: 45px !important;
  // max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

.btn__wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.left-btn {
  width: 45%;
  height: 55px;
  background: #c8c8c8;
  border-radius: 5px;
  margin: 20px 8px 0 8px;
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
  color: #fff;
}
.right-btn {
  width: 45%;
  height: 55px;
  background: #41d8e6;
  border-radius: 5px;
  margin: 20px 8px 0 8px;
  font-weight: 700;
  font-size: 20px;
  line-height: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
  color: #fff;
}

::v-deep .v-text-field__details {
  display: none;
}

::v-deep .v-application .pa-3 {
  padding: 15px 10px 0 10px !important;
}

::v-deep .v-input__slot {
  margin: 0 !important;
  padding-bottom: 10px !important;
  background-color: #fff !important;
}
::v-deep textarea {
  line-height: 18px !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
