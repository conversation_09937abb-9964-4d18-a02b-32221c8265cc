<template>
  <div>
    <div v-if="activeWeeklyCycle" class="weekly-button__wrapper">
      <!-- <button @click="weekBtnHandler(idx)" class="active-week-button" v-for="(day, idx) in days" v-bind:key="idx">
        {{ day }}
      </button> -->
      <v-btn-toggle v-model="selectedDays" multiple>
        <v-btn @change="selectedBtn(idx)" class="week-button" v-for="(day, idx) in days" v-bind:key="idx">
          <div class="btn-contents">{{ day }}</div>
        </v-btn>
      </v-btn-toggle>
    </div>
    <div v-else class="weekly-button__wrapper">
      <button class="week-button" v-for="(day, idx) in days" v-bind:key="idx">
        {{ day }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: "DayPicker",
  props: {
    activeWeeklyCycle: <PERSON><PERSON><PERSON>,
    initialSelectedDays: Array,
  },
  data() {
    return {
      selectedDays: [],
      days: [
        this.$i18n.t("mon"),
        this.$i18n.t("tue"),
        this.$i18n.t("wed"),
        this.$i18n.t("thu"),
        this.$i18n.t("fri"),
        this.$i18n.t("sat"),
        this.$i18n.t("sun"),
      ],
    };
  },
  methods: {
    selectedBtn(idx) {
      // this.initialSelectedDays.map()
      this.$emit("clickedDayButtons", idx);
    },
  },
  mounted() {
    this.selectedDays = this.initialSelectedDays;
  },
};
</script>

<style lang="scss" scoped>
.weekly-button__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.week-button {
  width: 10vw;
  height: 10vw;
  background-color: #ededed;
  border-radius: 5px;
  color: #a7a7a7;
  align-items: center;
  justify-content: center;
  display: flex;
  font-size: 0.9em;
  font-weight: 500;
  // min-width: 45px;
  // min-height: 45px;
  letter-spacing: -0.05em;
  object-fit: contain;
}

.btn-contents {
  width: 100%;
  display: flex;
  justify-content: center;
  text-align: left;
  margin: 0 auto;
}

.active-week-button {
  width: 10vw;
  height: 10vw;
  border-radius: 5px;
  color: #a7a7a7;
  background-color: #fff;
  font-size: 0.9em;
  font-weight: 500;
}

.select-week-button {
  width: 10vw;
  height: 10vw;
  border-radius: 5px;
  border: 2px solid #41d8e6;
  color: #a7a7a7;
  font-size: 22px;
  font-weight: 500;
}

.active-week-button:active,
.active-week-button:hover {
  border: 2px solid #41d8e6;
}

:v-deep .v-input {
  max-width: 164px;
}

::v-deep .theme--light.v-btn-toggle:not(.v-btn-toggle--group) .v-btn.v-btn {
  border-color: rgba(0, 0, 0, 0) !important;
  background-color: #fff;
  border-radius: 5px;
}

::v-deep .v-btn-toggle .v-btn.v-btn.v-size--default {
  min-width: 10vw;
  max-width: 10vw;
  width: 10vw;
}

::v-depp .v-btn-toggle:not(.v-btn-toggle--dense) .v-btn.v-btn.v-size--default {
  height: 10vw !important;
}

::v-deep .v-btn-toggle {
  border-radius: 5px;
}

::v-deep .v-btn__content {
  color: #a7a7a7;
  font-size: 16px;
  font-weight: 500;
}

::v-deep .v-btn:before {
  border: 2px solid #41d8e6 !important;
  background-color: #fff;
}

::v-deep .theme--light.v-btn--active:hover::before,
.theme--light.v-btn--active::before {
  opacity: 1;
}
::v-deep [data-v-330bd01f] .theme--light.v-btn-toggle:not(.v-btn-toggle--group) .v-btn.v-btn {
  background-color: #fff;
}

::v-deep .v-btn-toggle:not(.v-btn-toggle--dense) .v-btn.v-btn.v-size--default {
  width: 10vw;
  height: 10vw;
  /* margin: 0 5px; */
}

::v-deep .v-item-group {
  width: 100%;
  height: 100%;
  justify-content: space-between;
}
</style>
