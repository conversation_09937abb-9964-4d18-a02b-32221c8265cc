<template>
  <div class="container">
    <Loading v-if="loading" />
    <HeaderNav :pageName="pageName" />
    <ConfirmModal v-if="openModal" :error="resultText" @isClicked="modalHandler" />
    <div class="exam-alert__wrapper">
      <div class="save-btn__wrapper">
        <!-- <div class="switch__wrapper"> -->
        <v-switch
          @click="isUrineTestAlert"
          v-model="onUrineTestAlert"
          class="mt-0"
          color="#41d8e6"
          height="20px"
          inset
        ></v-switch>
      </div>
      <div class="time-settings__wrapper">
        <div class="time-btn__wrapper">
          <button @click="amBtnHandler" :class="[amActive, 'inactive-btn']">{{ $t("am") }}</button>
          <button @click="pmBtnHandler" :class="[pmActive, 'inactive-btn']">{{ $t("pm") }}</button>
        </div>
        <div v-if="!onUrineTestAlert" class="inactive-btn__wrapper">
          <div class="select-box">
            <button class="hour-btn">{{ defaultHour }}</button>
            <div class="colon">:</div>
            <button class="hour-btn">{{ defaultMinute }}</button>
            <div class="arrow-icon__wrapper">
              <img src="@/assets/images/mypage-icon/bottom_arrow.png" alt="arrow" />
            </div>
          </div>
        </div>
        <div v-else class="time-select__wrapper">
          <div class="select-box">
            <button @click="hourBtnClicked" class="hour-btn active-hour">{{ defaultHour }}</button>
            <div class="colon">:</div>
            <button @click="minuteBtnClicked" class="hour-btn active-hour">{{ defaultMinute }}</button>
            <ul v-if="showHourList" class="hour-list">
              <li v-for="hh in hour" :key="hh" class="hour-item" @click="hourBtnHandler(hh)">
                {{ hh }}
              </li>
            </ul>
            <ul v-if="showMinuteList" class="minute-list">
              <li v-for="(mm, id) in minute" :key="id" class="minute-item" @click="minuteHandler(mm)">
                {{ mm }}
              </li>
            </ul>
            <div class="arrow-icon__wrapper">
              <img src="@/assets/images/mypage-icon/bottom_arrow.png" alt="arrow" />
            </div>
          </div>
        </div>
      </div>
      <div class="underline"></div>
      <div class="cycle__wrapper">
        <div class="cycle-title">{{ $t("cycle") }}</div>
        <div class="weekly-cycle__wrapper">
          <div class="cycle-subtitle">{{ $t("weekly_cycle") }}</div>
          <div class="switch__wrapper">
            <v-switch
              class="pa-1 mt-0 mr-2"
              color="#41d8e6"
              height="20px"
              v-model="isWeeklyCycle"
              inset
              @click="setWeeklyCycleMode"
            ></v-switch>
          </div>
        </div>
        <DayPicker
          v-if="loaded"
          :activeWeeklyCycle="activeWeeklyCycle"
          :initialSelectedDays="initialSelectedDays"
          @clickedDayButtons="clickedDayButtons"
        />
        <div class="weekly-cycle__wrapper">
          <div class="cycle-subtitle">{{ $t("monthly_cycle") }}</div>
          <div class="switch__wrapper">
            <v-switch
              class="pa-1 mt-0 mr-2"
              color="#41d8e6"
              height="20px"
              v-model="isMonthlyCycle"
              inset
              @click="setMonthlyCycleMode"
            ></v-switch>
          </div>
        </div>
        <DatePicker
          v-if="loaded"
          :activeMonthlyCycle="activeMonthlyCycle"
          :initialSelectedDate="initialSelectedDate"
          @datePickerHandler="datePickerHandler"
        />
      </div>
    </div>
    <div class="btn__wrapper">
      <v-btn :disabled="!saveActive" elevation="0" color="#41D8E6" class="save-btn" @click="saveHandler">
        {{ $t("save") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import Loading from "@/components/Common/Loading.vue";
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import DayPicker from "@/pages/mypage/ExamAlert/DayPicker.vue";
import DatePicker from "@/pages/mypage/ExamAlert/DatePicker.vue";
import ConfirmModal from "@/components/Common/ErrorModal.vue";

export default {
  name: "ExamAlertSettings",
  components: {
    HeaderNav,
    DatePicker,
    DayPicker,
    ConfirmModal,
    Loading,
  },
  data() {
    return {
      loaded: false,
      loading: false,
      pageName: this.$i18n.t("test_alert_settings"),
      defaultHour: 8,
      defaultMinute: "00",
      hour: 12,
      minute: ["00", "10", "20", "30", "40", "50"],
      minuteListClicked: false,
      minuteItemClicked: false,
      onUrineTestAlert: false,
      showHourList: false,
      showMinuteList: false,
      amBtnClicked: true,
      pmBtnClicked: false,
      isWeeklyCycle: false,
      activeWeeklyCycle: false,
      isMonthlyCycle: false,
      activeMonthlyCycle: false,
      selectedDays: [],
      initialSelectedDays: [],
      initialSavedArr: [],
      selectedDate: 1,
      initialSelectedDate: null,
      initialSavedMonthlyArr: [],
      openModal: false,
      resultText: this.$i18n.t("alert_success_modal"),
      // timeInfo: JSON.parse(localStorage.getItem("UrineTestTime")) || "",
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  watch: {
    onUrineTestAlert(newVal) {},
  },
  methods: {
    datePickerHandler(date) {
      this.selectedDate = date;
    },
    clickedDayButtons(days) {
      if (this.selectedDays.includes(days)) {
        this.selectedDays = this.selectedDays.filter((i) => i !== days);
      } else {
        this.selectedDays = [...this.selectedDays, days];
      }
      // console.log("selectedDays:", this.selectedDays);
    },

    hourBtnHandler(id) {
      this.defaultHour = id;
      this.showHourList = false;
    },
    minuteHandler(idx) {
      this.defaultMinute = idx;
      this.showMinuteList = false;
    },
    hourBtnClicked() {
      if (this.showMinuteList === true) {
        this.showHourList = true;
        this.showMinuteList = false;
      } else if (this.showMinuteList === false) {
        this.showHourList = !this.showHourList;
      }
    },
    minuteBtnClicked() {
      if (this.showHourList === true) {
        this.showHourList = false;
        this.showMinuteList = true;
      } else if (this.showHourList === false) {
        this.showMinuteList = !this.showMinuteList;
      }
    },
    amBtnHandler() {
      if (this.pmBtnClicked === true) {
        this.pmBtnClicked = false;
        this.amBtnClicked = true;
      }
      this.amBtnClicked = true;
    },
    pmBtnHandler() {
      if (this.amBtnClicked === true) {
        this.amBtnClicked = false;
        this.pmBtnClicked = true;
      }
      this.pmBtnClicked = true;
    },

    isUrineTestAlert() {
      const onMessage = {
        action: "onUrineTestAlert",
      };
      const offMessage = {
        action: "offUrineTestAlert",
      };
      if (!this.onUrineTestAlert) {
        this.isWeeklyCycle = false;
        this.activeMonthlyCycle = false;
        this.isMonthlyCycle = false;
        this.activeWeeklyCycle = false;
        // localStorage.setItem("UrineTestAlert", false);
        // Webview.offUrineTestAlert(offMessage);
      }
      if (this.onUrineTestAlert) {
        // localStorage.setItem("UrineTestAlert", true);
        // Webview.onUrineTestAlert(onMessage);
      }
    },
    setWeeklyCycleMode() {
      if (this.onUrineTestAlert) {
        // when weekly cycle on
        if (this.isWeeklyCycle) {
          this.isMonthlyCycle = false;
          this.selectedDate = null;
          this.activeWeeklyCycle = true;
          this.activeMonthlyCycle = false;
        } // when weekly cycle off
        else if (!this.isWeeklyCycle) {
          this.activeWeeklyCycle = false;
        }
      }
    },
    setMonthlyCycleMode() {
      if (this.onUrineTestAlert) {
        // when Monthly cycle on
        if (this.isMonthlyCycle) {
          this.isWeeklyCycle = false;
          this.selectedDays = [];
          this.activeMonthlyCycle = true;
          this.activeWeeklyCycle = false;
        } // when Monthly cycle off
        else if (!this.isMonthlyCycle) {
          this.activeMonthlyCycle = false;
        }
      }
    },

    modalHandler() {
      this.openModal = false;
      this.$router.go(-1);
    },

    compareArrays(initialArr, newArr) {
      const deleteValue = initialArr.filter((value) => !newArr.includes(value));
      const addValue = newArr.filter((value) => !initialArr.includes(value));

      return [deleteValue, addValue];
    },

    saveHandler() {
      if (this.activeWeeklyCycle) {
        if (this.initialSelectedDays.length !== 0) {
          const [deletedDays, addedDays] = this.compareArrays(this.initialSelectedDays, this.selectedDays);
          if (deletedDays.length !== 0 || addedDays.length !== 0) {
            this.deleteMonthlyAlerts();
            this.deleteWeeklyAlerts();
            this.$nextTick(() => this.createWeeklyAlert());
          }
        }
        this.deleteMonthlyAlerts();
        this.createWeeklyAlert();
      } else {
        if (this.initialSelectedDate !== null) {
          // const [deletedDate, addedDate] = this.compareArrays(this.initialSelectedDate, this.selectedDate);
          if (this.initialSelectedDate !== this.selectedDate) {
            this.deleteWeeklyAlerts();
            this.deleteMonthlyAlerts();
            this.$nextTick(() => this.createMonthlyAlert());
          }
        }
        this.deleteWeeklyAlerts();
        this.createMonthlyAlert();
      }
    },

    convertHour() {
      let hours = this.defaultHour;
      if (this.pmBtnClicked) {
        if (hours !== 12) {
          hours += 12;
        }
      } else {
        if (hours === 12) {
          hours = 0;
        }
      }
      return Number(hours);
    },

    convertTime(day, hour) {
      const convertDay = day === 6 ? 0 : day + 1;
      const startDate = new Date();
      const currentDay = startDate.getDay();
      const currentHour = startDate.getHours();

      if (convertDay === currentDay && hour > currentHour) {
        startDate.setHours(hour, 0, 0, 0);
      } else {
        const daysToAdd = (convertDay - currentDay + 7) % 7;
        startDate.setDate(startDate.getDate() + daysToAdd);
        startDate.setHours(hour, this.defaultMinute, 0, 0);
      }

      const startTime = startDate.getTime() + 1000 * 5;
      return startTime;
    },

    convertDate(hour) {
      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();

      const startDate = new Date(currentYear, currentMonth, this.selectedDate, hour, this.defaultMinute);
      const startTime = startDate.getTime() + 1000 * 5;

      return startTime;
    },

    createWeeklyAlert() {
      const hour = this.convertHour();
      const message = new Array(0);
      this.selectedDays.map((day) => {
        const startTime = this.convertTime(day, hour);
        message.push({
          id: `${day}-${hour}-${this.defaultMinute}`,
          title: this.$i18n.t("exam_alert_title"),
          body: this.$i18n.t("exam_alert_body"),
          start: startTime,
        });
      });
      // console.log(message);
      Webview.createtWeeklyAlerts(message);
    },
    createMonthlyAlert() {
      const hour = this.convertHour();
      const startTime = this.convertDate(hour);
      const message = [
        {
          id: `${this.selectedDate}-${hour}-${this.defaultMinute}`,
          title: this.$i18n.t("exam_alert_title"),
          body: this.$i18n.t("exam_alert_body"),
          start: startTime,
        },
      ];
      Webview.createtMonthlyAlerts(message);
    },

    deleteWeeklyAlerts() {
      const message = this.initialSavedArr;
      Webview.deleteWeeklyAlerts(message);
    },
    deleteMonthlyAlerts() {
      const message = this.initialSavedMonthlyArr;
      Webview.deleteMonthlyAlerts(message);
    },

    setInitialWeeklyAlert(arr) {
      if (arr.length === 0) {
        this.onUrineTestAlert = this.isMonthlyCycle ? false : true;
        this.isWeeklyCycle = false;
        this.activeWeeklyCycle = false;
        // this.loading = false;
        // this.loaded = true;
      } else {
        this.onUrineTestAlert = true;
        this.isWeeklyCycle = true;
        this.activeWeeklyCycle = true;
        this.initialSavedArr = arr;
        const [dayIdx, hour, min] = arr[0].split("-");
        this.defaultHour = Number(hour);
        this.defaultMinute = min;
        if (this.defaultHour >= 12) {
          this.amBtnClicked = false;
          this.pmBtnClicked = true;
          this.defaultHour = this.defaultHour === 12 ? 12 : this.defaultHour - 12;
        }
        const daysArr = arr.map((day) => Number(day.slice(0, 1)));

        this.selectedDays = daysArr;
        this.initialSelectedDays = daysArr;
        // this.loading = false;
        // this.loaded = true;
        // alert(this.initialSelectedDays);
      }
    },
    setInitialMonthlyAlert(arr) {
      try {
        if (arr.length === 0) {
          this.onUrineTestAlert = this.isWeeklyCycle ? true : false;
          this.isMonthlyCycle = false;
          this.activeMonthlyCycle = false;
          this.loading = false;
          this.loaded = true;
        } else {
          this.onUrineTestAlert = true;
          this.isMonthlyCycle = true;
          this.activeMonthlyCycle = true;
          this.initialSavedMonthlyArr = arr;
          const [dayIdx, hour, min] = arr[0].split("-");
          // alert(dayIdx, hour, min);
          this.defaultHour = Number(hour);
          this.defaultMinute = min;
          if (this.defaultHour >= 12) {
            this.amBtnClicked = false;
            this.pmBtnClicked = true;
            this.defaultHour = this.defaultHour === 12 ? 12 : this.defaultHour - 12;
          }

          this.selectedDate = Number(dayIdx);
          this.initialSelectedDate = Number(dayIdx);
          this.loading = false;
          this.loaded = true;
        }
      } catch (e) {
        // alert(e);
      }
    },
    setAlertState(response) {
      switch (response.action) {
        case "REQ-LIST-LN-WEEKLY": {
          this.setInitialWeeklyAlert(response.result);
          break;
        }
        case "REQ-LIST-LN-MONTHLY": {
          this.setInitialMonthlyAlert(response.result);
          break;
        }
        default:
          break;
      }
      switch (response.result) {
        case "SUCCESS": {
          this.resultText = this.$i18n.t("alert_success_modal");
          this.openModal = true;
          break;
        }
        case "FAIL": {
          this.resultText = this.$i18n.t("alert_fail_modal");
          this.openModal = true;
          break;
        }
        case "NO-GRANTED": {
          this.resultText = this.$i18n.t("notification_permission");
          this.openModal = true;
          break;
        }
        default:
          break;
      }
    },
    listener() {
      this.isIos
        ? window.addEventListener("message", this.handleMessage)
        : document.addEventListener("message", this.handleMessage);
    },

    handleMessage(e) {
      const data = JSON.parse(e.data);
      // alert(e.data);
      this.setAlertState(data.payload);
    },
    initialSetting() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      this.loading = true;
      // this.loaded = true;
      this.listener();
      Webview.getWeeklyAlerts();
      Webview.getMonthlyAlerts();
    },
  },
  computed: {
    amActive() {
      return this.amBtnClicked ? "time-btn" : false;
    },
    pmActive() {
      return this.pmBtnClicked ? "time-btn" : false;
    },
    saveActive() {
      // 요일, 날짜 말고 시간만 변경되어도 저장가능하도록 수정
      if (this.onUrineTestAlert) {
        if (this.isWeeklyCycle) {
          if (this.initialSelectedDays.length !== 0) {
            const [deletedDays, addedValue] = this.compareArrays(this.initialSelectedDays, this.selectedDays);
            return (this.selectedDays.length !== 0 && deletedDays.length !== 0) || addedValue.length !== 0;
          } else if (this.selectedDays.length !== 0) return true;
          return false;
        } else if (this.isMonthlyCycle) {
          if (this.initialSelectedDate !== null) {
            // const [deletedDays, addedValue] = this.compareArrays(this.initialSelectedDate, this.selectedDate);
            return this.initialSelectedDate !== this.selectedDate;
          } else if (this.selectedDate !== null) return true;
          return false;
        }
      }
      return false;
      // return (
      //   (this.onUrineTestAlert && this.isWeeklyCycle && this.selectedDays.length !== 0) ||
      //   (this.onUrineTestAlert && this.isMonthlyCycle)
      // );
    },
  },
  mounted() {
    this.initialSetting();
  },
  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
    document.removeEventListener("message", this.handleMessage);
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}

.exam-alert__wrapper {
  width: 100%;
  padding: 40px 30px 100px 30px;
}
.save-btn__wrapper {
  position: absolute;
  top: 64px;
  right: 30px;
}
.btn__wrapper {
  width: 100%;
  position: fixed;
  bottom: 3.5vh;
  left: 0;
  padding: 0px 30px;
}

.save-btn {
  height: 50px !important;
  position: relative;
  z-index: 2;
  // max-width: 340px;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 20px;
  font-weight: bold;
  background-color: #41d8e6;
  color: #fff !important;
  margin: 0 auto;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

.time-settings__wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.time-btn__wrapper {
  width: 140px;
  height: 55px;
  background-color: #ededed;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.time-btn {
  width: 55px;
  height: 37.5px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  margin: 0 5px;
  color: #000000 !important;
  font-size: 20px;
  font-weight: 500;
}

.inactive-btn {
  width: 55px;
  height: 37.5px;
  margin: 0 5px;
  font-size: 20px;
  font-weight: 500;
  color: #a7a7a7;
}

.inactive-btn__wrapper {
  width: 140px;
  height: 55px;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #fff;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: GilroyMedium;
}

.time-select__wrapper {
  width: 140px;
  height: 55px;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #ededed;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
}
.time-select__wrapper:hover {
  border: 2px solid #41d8e6;
}

.colon {
  font-size: 24px;
  font-weight: 500;
  color: #a7a7a7;
  margin: 0 5px;
}

.arrow-icon__wrapper {
  width: 14px;
  display: flex;
  align-items: center;
  margin-left: 5px;
  img {
    width: 100%;
  }
}
.select-box {
  width: 140px;
  display: flex;
  padding: 10px;
}
.hour-btn {
  color: #a7a7a7;
  width: 50%;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
}

.active-hour {
  color: #000000 !important;
}

.hour-list {
  background-color: #fff;
  width: 140px;
  height: 100%;
  min-height: 310px;
  max-height: 65vh;
  overflow: scroll;
  position: absolute;
  top: 203px;
  right: 30px;
  border-radius: 5px;
  border: 2px solid #41d8e6;
}

ul {
  padding: 0 !important;
  list-style: none;
  z-index: 5;
}

.hour-item {
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  width: 100%;
  padding: 5px 10px 5px 30px;
}
.hour-item:hover {
  background-color: #c9f4f8;
}

.minute-list {
  background-color: #fff;
  width: 140px;
  height: 340px;
  max-height: 70vh;
  overflow: scroll;
  position: absolute;
  top: 203px;
  right: 30px;
  border-radius: 5px;
  border: 2px solid #41d8e6;
}
.minute-item {
  font-family: GilroyMedium;
  letter-spacing: -0.03em;
  font-size: 24px;
  font-weight: 500;
  text-align: right;
  width: 100%;
  padding: 10px 30px 10px 10px;
}

.minute-item:hover {
  background-color: #c9f4f8;
}

.underline {
  border-bottom: 0.5px solid #a7a7a7;
}

.cycle__wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.cycle-title {
  // width: 100%;
  font-weight: 500;
  font-size: 20px;
  margin: 20px 0;
  text-align: left;
  color: #000000;
}

.weekly-cycle__wrapper {
  display: flex;
  justify-content: space-between;
}

.cycle-subtitle {
  font-size: 18px;
  color: #646464;
}

.switch__wrapper {
  display: flex;
  width: 40px;
  align-items: center;
  justify-content: flex-end;
}

.monthly-setting__wrapper {
  display: flex;
  justify-content: space-between;
}

.monthly-button__wrapper {
  display: flex;
  background-color: #ededed;
  width: 165px;
  height: 50px;
  border-radius: 5px;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.monthly-button {
  height: 100%;
  width: 90%;
  color: #a7a7a7;
  font-size: 24px;
  font-family: GilroyMedium;
}

.active-monthly-button__wrapper {
  display: flex;
  width: 165px;
  height: 50px;
  border-radius: 5px;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.monthly-text {
  font-weight: 500;
  font-size: 16px;
  color: #a7a7a7;
  height: 50px;
  display: flex;
  align-items: center;
}

::v-deep .v-input--selection-controls__input {
  width: 36px !important;
}

::v-deep .theme--light.v-input--switch .v-input--switch__track {
  color: #dadada;
}

::v-deep .v-input--switch__track {
  opacity: 1 !important;
  width: 45px !important;
  height: 25px !important;
}

::v-deep .v-input--switch__thumb {
  color: #ffffff !important;
  width: 17px !important;
  height: 17px !important;
}

::v-deep .v-messages {
  min-height: 5px;
}
</style>
