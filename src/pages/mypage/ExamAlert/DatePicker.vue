<template>
  <div>
    <div class="monthly-setting__wrapper">
      <!-- class="monthly-button__wrapper" -->
      <div v-if="!activeMonthlyCycle" class="monthly-button__wrapper">
        <button class="monthly-button">{{ defaultDate }}</button>
        <div class="arrow-icon__wrapper">
          <img src="@/assets/images/mypage-icon/bottom_arrow.png" alt="arrow" />
        </div>
      </div>
      <div @click="switchHandler" v-if="activeMonthlyCycle" class="active-monthly-button__wrapper">
        <button class="active monthly-button">{{ defaultDate }}</button>
        <div class="arrow-icon__wrapper">
          <img src="@/assets/images/mypage-icon/bottom_arrow.png" alt="arrow" />
        </div>
      </div>
      <div class="monthly-text">{{ $t("repeat") }}</div>
    </div>
    <div>
      <table v-if="activeMonthlyCycle && switchDatePicker" class="date-picker__wrapper">
        <tr>
          <th @click="datePickerHandler(date)" v-for="date in firstWeek" v-bind:key="date">
            {{ date }}
          </th>
        </tr>
        <tr>
          <td @click="datePickerHandler(date)" v-for="date in secondtWeek" v-bind:key="date">{{ date }}</td>
        </tr>
        <tr>
          <td @click="datePickerHandler(date)" v-for="date in thirdWeek" v-bind:key="date">{{ date }}</td>
        </tr>
        <tr>
          <td @click="datePickerHandler(date)" v-for="date in fourthWeek" v-bind:key="date">{{ date }}</td>
        </tr>
        <tr>
          <td @click="datePickerHandler(date)" v-for="date in fifthWeek" v-bind:key="date">{{ date }}</td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: "DatePicker",
  props: {
    activeMonthlyCycle: Boolean,
    initialSelectedDate: Number,
    // switchDatePicker: Boolean,
    // id: Number,
    // date: Number,
  },
  data: () => ({
    defaultDate: 1,
    firstWeek: [1, 2, 3, 4, 5],
    secondtWeek: [6, 7, 8, 9, 10],
    thirdWeek: [11, 12, 13, 14, 15],
    fourthWeek: [16, 17, 18, 19, 20],
    fifthWeek: [21, 22, 23, 24, 25],
    switchDatePicker: false,
  }),
  methods: {
    datePickerHandler(date) {
      this.$emit("datePickerHandler", date);
      this.defaultDate = date;
      this.switchDatePicker = false;
    },
    switchHandler() {
      // console.log("clicked");
      this.switchDatePicker = !this.switchDatePicker;
    },
  },
  computed: {
    active() {
      return this.date === this.id ? "active-date" : false;
    },
  },
  mounted() {
    this.defaultDate = this.initialSelectedDate !== null ? this.initialSelectedDate : 1;
  },
};
</script>

<style lang="scss" scoped>
.monthly-setting__wrapper {
  display: flex;
  justify-content: space-between;
}

.monthly-button__wrapper {
  display: flex;
  background-color: #ededed;
  width: 170px;
  height: 50px;
  border-radius: 5px;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.active {
  color: #000000 !important;
}

.monthly-button {
  height: 100%;
  width: 90%;
  color: #a7a7a7;
  font-size: 26px;
  font-family: GilroyMedium;
}

.active-monthly-button__wrapper {
  display: flex;
  width: 170px;
  height: 50px;
  border-radius: 5px;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.arrow-icon__wrapper {
  width: 14px;
  display: flex;
  align-items: center;
  margin-left: 5px;
  img {
    width: 100%;
  }
}

.monthly-text {
  font-weight: 500;
  font-size: 18px;
  color: #a7a7a7;
  height: 50px;
  display: flex;
  align-items: center;
}

.date-picker__wrapper {
  width: 100%;
}

table {
  border-collapse: separate;
  border: 1px solid #ededed;
  border-spacing: 0px;
  border-radius: 5px;
  font-family: GilroyMedium;
  font-size: 26px;
}

td,
th {
  border-left: 1px solid #ededed;
  border-top: 1px solid #ededed;
  font-weight: 500;
  width: 20%;
  padding: 5px 0;
  // height: 35px;
}

th {
  border-top: none;
}

td:first-child,
th:first-child {
  border-left: none;
}
// .active-date {

th:hover,
td:hover {
  background-color: #41d8e6;
  color: #fff;
}
</style>
