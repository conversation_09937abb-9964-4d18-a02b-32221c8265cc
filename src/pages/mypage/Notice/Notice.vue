<template>
  <!-- TODO: server에서 데이터 받아와서 화면에 그림 -->
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="no-data">{{ $t("no_notice") }}</div>
    <!-- <div class="notice__wrapper">
      <div class="card__wrapper">
        <div class="notice-card">
          <div class="notice-card-header" @click="clickToFold">
            <div class="notice-card-title" v-html="this.$i18n.t('notice_title')"></div>
            <div :class="[unfold, 'fold']">
              <img class="icon" src="@/assets/images/bottom-arrow.png" />
            </div>
          </div>
          <div class="notice-sub-content">
            <div class="notice-date">2023.01.03</div>
          </div>
          <div class="underline"></div>
          <div :class="[active, '']">
            <div class="notice-card-text">
              <div v-html="this.$i18n.t('notice_content')"></div>
              <div class="img__wrapper"><img src="@/assets/images/notice_CES2023.png" /></div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";

export default {
  name: "MypageNotices",
  components: { HeaderNav },
  data() {
    return {
      pageName: this.$i18n.t("category_notice"),
      clicked: false,
      noData: this.$i18n.t("no_notice"),
    };
  },
  methods: {
    clickToFold() {
      // console.log("clicked!");
      return (this.clicked = !this.clicked);
    },
  },
  computed: {
    active() {
      return this.clicked ? false : "notice-card-content";
    },
    unfold() {
      return this.clicked ? "unfold" : false;
    },
  },
};
</script>

<style scoped>
.no-data {
  color: #646464;
  padding: 50px 30px 0;
  text-align: left;
}

.notice__wrapper {
  width: 100%;
  text-align: left;
}

.card__wrapper {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  padding: 40px 30px;
}

.notice-card {
  width: 100%;
  margin-bottom: 20px;
}

.notice-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.notice-card-title {
  width: 95%;
  font-size: 18px;
  font-weight: 500;
  color: #000000;
  letter-spacing: -0.03em;
}
.notice-date {
  color: #646464;
  font-weight: 400;
  font-size: 12px;
  margin: 10px 0;
}

.notice-sub-content {
  border-bottom: 0.5px solid #a7a7a7;
}

.notice-card-content {
  display: none;
}

.fold {
  width: 12px;
  display: flex;
}

img {
  width: 100%;
}

.unfold {
  transform: rotate(180deg);
}

.notice-card-text {
  color: #000000;
  font-size: 16px;
  margin-top: 20px;
  letter-spacing: -0.03em;
  line-height: 20px;
}

.img__wrapper {
  width: 100%;
  padding: 30px 0;
}
</style>
