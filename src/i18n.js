import Vue from "vue";
import VueI18n from "vue-i18n";

Vue.use(VueI18n);

function loadLocaleMessages() {
  const locales = require.context("./locales", true, /[A-Za-z0-9-_,\s]+\.json$/i);
  const messages = {};
  locales.keys().forEach((key) => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i);
    if (matched && matched.length > 1) {
      const locale = matched[1];
      messages[locale] = locales(key);
    }
  });
  return messages;
}

const userLocale = navigator.language || navigator.languages[0] || "en";
const language = navigator.language.split(/-|_/);

const systemLocale = language.includes("CN")
  ? "cn"
  : language[0] === "en"
  ? "en"
  : language[0] === "ja"
  ? "ja"
  : userLocale;

console.debug(navigator.language, userLocale);

export default new VueI18n({
  locale: systemLocale,
  fallbackLocale: "en",
  messages: loadLocaleMessages(),
});
