export function useClipboard() {
  function getClipboard(result = "", { onSuccess = () => {}, onError = () => {} }) {
    // result 길이 0일 때 (빈 값)
    if (result.length === 0) {
      return;
    }

    // navigator clipboard 사용
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard
        .writeText(result)
        // success
        .then(() => onSuccess())
        // error
        .catch((err) => onError(err));
    } else {
      // webview 환경에서 clipboard 사용
      try {
        const textArea = document.createElement("textarea");

        textArea.value = result;

        // textarea style
        textArea.style.position = "fixed";
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.width = "1px";
        textArea.style.height = "1px";
        textArea.style.padding = "0";
        textArea.style.border = "none";
        textArea.style.outline = "none";
        textArea.style.boxShadow = "none";
        textArea.style.background = "transparent";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // copy success variable
        const isSuccess = document?.execCommand("copy");

        document.body.removeChild(textArea);

        if (isSuccess) {
          // success
          onSuccess();
          return;
        }

        // error
        throw new Error("copy-clipboard failed");
      } catch (err) {
        console.error(err);
        onError(err);
      }
    }
  }

  return { getClipboard };
}
