import Vue from "vue";
import Vuetify from "vuetify/lib/framework";
import colors from "vuetify/lib/util/colors";

// Navigation icons
import HomeOff from "@/assets/svg/nav/line/home.svg";
import HomeOn from "@/assets/svg/nav/solid/home.svg";
import ExamOff from "@/assets/svg/nav/line/exam.svg";
import ExamOn from "@/assets/svg/nav/solid/exam.svg";
import ShopOff from "@/assets/svg/nav/line/shop.svg";
import ShopOn from "@/assets/svg/nav/solid/shop.svg";
import SolutionOff from "@/assets/svg/nav/line/solution.svg";
import SolutionOn from "@/assets/svg/nav/solid/solution.svg";
import MyOff from "@/assets/svg/nav/line/mypage.svg";
import MyOn from "@/assets/svg/nav/solid/mypage.svg";

// Button icons
import EyeOff from "@/assets/svg/eye-off.svg";
import EyeShow from "@/assets/svg/eye-show.svg";
import Share from "@/assets/svg/share.svg";
import xCircle from "@/assets/svg/x-circle.svg";
import xCircleWht from "@/assets/svg/x-circle_wht.svg";
import DisabledArrow from "@/assets/svg/disable-arrrow.svg";
import BackBtn from "@/assets/svg/left.svg";
import RighArrow from "@/assets/svg/right.svg";
import Setting from "@/assets/svg/setting.svg";
import Edit from "@/assets/svg/edit.svg";
import chevron from "@/assets/svg/chevron.svg";
import del from "@/assets/svg/del.svg";
import xCircleSolid from "@/assets/svg/solid.svg";
import backBtnBold from "@/assets/svg/back_btn_bold.svg";
import zoomBtn from "@/assets/svg/zoomText.svg";
import smallGaText from "@/assets/svg/small_ga_text.svg";
import largeGaText from "@/assets/svg/large_ga_text.svg";
import checkBox from "@/assets/svg/checkbox.svg";
import checkBoxInactive from "@/assets/svg/solid_checkbox.svg";

// Mypage icons
import GuideIcon from "@/assets/svg/guide.svg";
import NoticeIcon from "@/assets/svg/notice.svg";
import CustomerServiceIcon from "@/assets/svg/customer_service.svg";
import UserPlus from "@/assets/svg/user-plus.svg";
import User from "@/assets/svg/user.svg";
import DefaultProfileIcon from "@/assets/svg/default_profile.svg";
import FoldIcon from "@/assets/svg/fold_arrow.svg";
import UnFoldIcon from "@/assets/svg/unfold_arrow.svg";

// History icons
import PrevIcon from "@/assets/svg/prev-btn.svg";
import nextIcon from "@/assets/svg/next-btn.svg";

Vue.use(Vuetify);

export default new Vuetify({
  theme: {
    themes: {
      light: {
        primary: "C9F4F8", // #C9F4F8
        secondary: colors.red.lighten4, // #FFCDD2
        accent: colors.indigo.base, // #3F51B5
        login: "323232", // #323232
      },
    },
  },
  icons: {
    values: {
      home_off: {
        component: HomeOff,
      },
      home_on: {
        component: HomeOn,
      },
      shop_off: {
        component: ShopOff,
      },
      shop_on: {
        component: ShopOn,
      },
      exam_off: {
        component: ExamOff,
      },
      exam_on: {
        component: ExamOn,
      },
      solution_off: {
        component: SolutionOff,
      },
      solution_on: {
        component: SolutionOn,
      },
      my_off: {
        component: MyOff,
      },
      my_on: {
        component: MyOn,
      },
      eye_off: {
        component: EyeOff,
      },
      eye_show: {
        component: EyeShow,
      },
      x_circle: {
        component: xCircle,
      },
      share: {
        component: Share,
      },
      x_circle_wht: {
        component: xCircleWht,
      },
      disable_arrow: {
        component: DisabledArrow,
      },
      back_btn: {
        component: BackBtn,
      },
      setting_btn: {
        component: Setting,
      },
      right_arrow: {
        component: RighArrow,
      },
      edit: {
        component: Edit,
      },
      chevron: {
        component: chevron,
      },
      del: {
        component: del,
      },
      customer_icon: {
        component: CustomerServiceIcon,
      },
      notice_icon: {
        component: NoticeIcon,
      },
      guide_icon: {
        component: GuideIcon,
      },
      x_circle_solid: {
        component: xCircleSolid,
      },
      back_btn_bold: {
        component: backBtnBold,
      },
      zoom_btn: {
        component: zoomBtn,
      },
      large_text: {
        component: largeGaText,
      },
      small_text: {
        component: smallGaText,
      },
      check_box: {
        component: checkBox,
      },
      check_box_inactive: {
        component: checkBoxInactive,
      },
      user_plus: {
        component: UserPlus,
      },
      user: {
        component: User,
      },
      default_profile_icon: {
        component: DefaultProfileIcon,
      },
      fold_icon: {
        component: FoldIcon,
      },
      unfold_icon: {
        component: UnFoldIcon,
      },
      prev_icon: {
        component: PrevIcon,
      },
      next_icon: {
        component: nextIcon,
      },
    },
  },
});
