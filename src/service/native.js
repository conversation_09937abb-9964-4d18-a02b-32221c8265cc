import router from "../router/index.js";
import store from "../store/index.js";
import i18n from "../../src/i18n.js";

// App -> Web Bridge
export default {
//  requestFromNative(command, params) {
//   switch(command){
//       case "TEST"
//         break;
//   }
// },
  // getVersion(version) {
  //   console.log("get app version");
  //   console.log(version);
  //   localStorage.setItem("curVersion", version);
  // },
  // getLanguage(lang) {
  //   console.log(lang);
  //   localStorage.setItem("locale", lang);
  //   i18n.locale = lang;
  //   console.log("localStorage get value locale:");
  //   console.log(localStorage.getItem("locale"));
  // },
  // // 검사 완료에 대한 response 브릿지
  // successAnalysis(message) {
  //   console.log("success Analysis message:");
  //   console.log(message);
  //   message === "boat" ? store.commit("setIsBoat", true) : store.commit("setIsBoat", false);
  //   store.commit("completeAlert");
  //   router.push("/home?analysis=done");
  // },
  // pageMainReload() {
  //   router.go(router.currentRoute);
  //   store.commit("bluetoothSuccess");
  // },
  // writeDeviceInfo(device) {
  //   store.dispatch("GETDEVICEINFO", device);
  //   console.log("deviceInfo:", device);
  // },

  // // ? 카카오 로그인 완료 후 데이터 받기용 브릿지
  // kakaoSignResponse(kakaoSignData) {
  //   console.log("kakao data:");
  //   console.log(kakaoSignData);
  //   // * kakaoSignResponse: id, profile, name, email, birthyear, birthday, gender, valid, status;
  //   // ! status === 201 DB에 미존재(유효함): 회원가입
  //   if (kakaoSignData.status === 201) {
  //     router.push({
  //       name: "Join",
  //       params: {
  //         id: kakaoSignData.id,
  //         snsType: "kakao",
  //         email: kakaoSignData.email || "",
  //       },
  //     });
  //   }
  //   // ! status === 401 이미 DB에 존재(유효하지 않음): 로그인
  //   else if (kakaoSignData.status === 401)  {
  //     const account = { account: String(kakaoSignData.id) };
  //     const type = "kakao";
  //     store
  //       .dispatch("SNS_LOGIN", { type, account })
  //       .then((res) => {
  //         localStorage.setItem("snsType", type);
  //         router.push({ path: "/home" });
  //       })
  //       .catch((message) => {
  //         console.log(message);
  //       });
  //   } else {
  //     console.log("유저정보 조회 실패");
  //   }
  // },

  // // ? 구글 로그인 후 데이터 수신
  // googleSignResponse(googleSignData) {
  //   console.log("google data:");
  //   console.log(googleSignData);
  //   // ! status === 201 DB에 미존재(유효함): 회원가입
  //   if (googleSignData.status === 201) {
  //     router.push({
  //       name: "Join",
  //       params: {
  //         id: String(googleSignData.uid),
  //         snsType: "google",
  //         email: googleSignData.email || "",
  //         profileImg: googleSignData.photoURL || "",
  //         name: googleSignData.displayName || "",
  //       },
  //     });
  //   }
  //   // ! status === 401 이미 DB에 존재(유효하지 않음): 로그인
  //   else if (googleSignData.status === 401) {
  //     const account = { account: String(googleSignData.uid) };
  //     console.log("google uid:");
  //     console.log(googleSignData.uid);
  //     const type = "google";
  //     store
  //       .dispatch("SNS_LOGIN", { type, account })
  //       .then((res) => {
  //         localStorage.setItem("snsType", type);
  //         router.push({ path: "/home" });
  //       })
  //       .catch((message) => {
  //         console.log(message);
  //       });
  //   } else {
  //     console.log("유저정보 조회 실패");
  //   }
  // },

  // // * appleSignResponse: useridentifier, fullName, email, valid, status
  // // ? 애플 로그인 후 데이터 수신
  // appleSignResponse(appleSignData) {
  //   console.log("apple data:");
  //   console.log(appleSignData);

  //   if (appleSignData.status === 201) {
  //     router.push({
  //       name: "Join",
  //       params: {
  //         id: appleSignData.useridentifier,
  //         snsType: "apple",
  //         email: appleSignData.email || "",
  //         profileImg: appleSignData.photoURL || "",
  //         name: appleSignData.fullName || "",
  //       },
  //     });
  //   }
  //   // ! status === 401 이미 DB에 존재(유효하지 않음): 로그인
  //   else if (appleSignData.status === 401) {
  //     const account = { account: String(appleSignData.useridentifier) };
  //     const type = "apple";
  //     store
  //       .dispatch("SNS_LOGIN", { type, account })
  //       .then((res) => {
  //         localStorage.setItem("snsType", type);
  //         router.push({ path: "/home" });
  //       })
  //       .catch((message) => {
  //         console.log(message);
  //       });
  //   } else {
  //     console.log("유저정보 조회 실패");
  //   }
  // },

  // // 카메라 검사하기에서 뒤로가기 시 검사 시작하기 페이지 이동용 브릿지
  // gotoAnalizeStart() {
  //   router.push("/exam/wait");
  // },
  // // 자동 로그인 브릿지
  // autologin(user) {
  //   return user;
  // },

  // // 알림 허용여부
  // notificationStatus(status) {
  //   // store.dispatch("")
  //   console.log("noti:");
  //   console.log(status);
  // },
};
