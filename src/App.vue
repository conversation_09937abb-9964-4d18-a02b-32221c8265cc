<template>
  <v-app :style="fontStyles">
    <v-main>
      <router-view />
    </v-main>
  </v-app>
</template>

<script>
export default {
  name: "App",
  computed: {
    fontStyles() {
      return {
        "font-family": this.fontFamily,
        "letter-spacing": this.letterSpacing,
      };
    },

    fontFamily() {
      const locale = this.$i18n.locale;

      const fontMap = {
        ja: "Noto Sans JP", // 일본어인 경우 Noto Sans JP
        ko: "Noto Sans KR", // 한국어인 경우 Noto Sans KR
        en: "GilroyMedium", // 영어인 경우 GilroyMedium
        // 필요한 만큼 여기에 언어 코드와 폰트 패밀리를 추가하세요.
      };

      const langCode = locale.split("-")[0];

      // fontMap에서 해당 langCode에 맞는 폰트가 있는지 확인하고 반환합니다.
      // 만약 langCode에 맞는 폰트가 없으면 기본값인 "GilroyMedium"을 반환합니다.
      return fontMap[langCode] || "GilroyMedium";
    },

    letterSpacing() {
      return this.$i18n.locale.includes("ko") ? "-0.03em" : "0em";
    },
  },
};
</script>

<style lang="scss">
@import "@/styles/global.scss";

body,
html {
  font-size: 18px;
  // letter-spacing: -0.03em;
  // overscroll-behavior-y: none;
}

img,
a {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-select: none;
  user-drag: none;
}

b,
strong {
  font-weight: 700 !important;
}

sub,
sup {
  font-size: 50% !important;
  top: -0.7em !important;
}
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  width: 100% !important;
  height: 100%;
  max-width: 450px !important;
  margin: auto;
}

@media screen and (min-width: 789px) {
  #app {
    display: none !important;
  }
}
</style>
