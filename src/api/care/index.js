import { cym702 } from "../index";

export default {
  GetCareTargetData(subjectId) {
    return cym702.get(`/cares/target/${subjectId}`);
  },

  GetCareData(subjectId, type, start, end, utcOffset, periodType) {
    return cym702.get(`/cares/${type}/${subjectId}?start=${start}&end=${end}&utcOffset=${utcOffset}${periodType}`);
  },
  DeleteCareData(subjectId, type, id) {
    return cym702.delete(`/cares/${type}/${subjectId}`, { data: id });
  },

  // care 데이터 저장, 업데이트
  FetchUpdateCareData(subjectId, type, value) {
    return cym702.post(`/cares/${type}/${subjectId}`, value);
  },
};
