import { cym702 } from "../index";

export default {
  fetchCym702History: function(count) {
    return cym702.post("/cym/histories/total/" + count, {});
  },

  fetchBloodHistory: function(count) {
    return cym702.post("/cym/histories/blood/" + count, {});
  },

  fetchGlucoseHistory: function(count) {
    return cym702.post("/cym/histories/glucose/" + count, {});
  },

  fetchProteinHistory: function(count) {
    return cym702.post("/cym/histories/protein/" + count, {});
  },

  fetchPhHistory: function(count) {
    return cym702.post("/cym/histories/pH/" + count, {});
  },

  fetchKetoneHistory: function(count) {
    return cym702.post("/cym/histories/ketone/" + count, {});
  }
};

