import { cym702 } from "../index";

export default {
  /** 5종 검사결과 데이터 가져오는 API */
  GetCymHome(subjectId, page) {
    return cym702.get(`/analysis/home/<USER>
  },
  /**  체중, 수분, 배뇨의 오늘 측정된 데이터 가져오는 API */
  GetHomeCareData(subjectId, today) {
    return cym702.get(`/cares/home/<USER>
  },
  getAnalysisScoreData(subjectId, page, limit) {
    return cym702.get(`/analysis/score/${subjectId}`, {
      params: {
        subjectId,
        page,
        limit,
      },
    });
  },
  /** 소변검사결과 모든   데이터 가져오는 API */
  GetCymYearData(subjectId, page, limit) {
    return cym702.get(`/analysis/score/${subjectId}`, {
      params: {
        page,
        limit,
        subjectId,
      },
    });
  },
  getAnalysisDetailData(subjectId, cymType, page, limit) {
    return cym702.get(`/analysis/${cymType}/${subjectId}`, {
      params: {
        subjectId,
        cymType,
        page,
        limit,
      },
    });
  },

  /** New API 심점수 + 검사점수 결과  */
  GetCymScoreData(subjectId, page, limit) {
    return cym702.get(`/analysis/score/${subjectId}`, {
      params: {
        page,
        limit,
      },
    });
  },
  /** New API [히스토리] 항목별 검사결과  */
  GetUrinalysisData(subjectId, cymType, page, limit) {
    return cym702.get(`/analysis/${cymType}/${subjectId}`, {
      params: {
        page,
        limit,
      },
    });
  },
};
