import { cym702, refreshInstance } from "../index";

const fetchGetMyProfile = () => {
  return cym702.post("/user/mypage");
};

const fetchGetUserInfo = () => {
  return cym702.get("/subjects");
};

const fetchGetSubjectInfo = (subjectId) => {
  return cym702.get(`/subjects/${subjectId}`);
};

const getMainUserInfo = () => {
  return cym702.get("/accounts/personal-info");
};

const fetchGetSubUserInfo = (id) => {
  return cym702.get(`/user/sub/${id}`);
};

const fetchImageUpload = () => {
  return cym702.post("/user/image");
};

const fetchUserEmail = () => {
  return cym702.post("/user/email");
};

const checkUserPw = (pw) => {
  return cym702.post("/accounts/personal-info/check-password", pw);
};

const updateUserPw = (password) => {
  return cym702.patch("/accounts/personal-info/change-password", password);
};

// const updateUserPwd = (password) => {
//   return cym702.put("/user/pwd", password);
// };

const fetchUserPhone = () => {
  return cym702.post("/user/phone");
};

const updateUserPhone = (phoneNum) => {
  return cym702.put("/user/main/phone", phoneNum);
};

const fetchGetUsername = () => {
  return cym702.post("/user/name");
};

const updateUsername = (userNickname) => {
  return cym702.put("/user/main/nickname", userNickname);
};

const updateSubjectInfo = (subjectId, userData) => {
  // if (selectUser > 0) return cym702.patch(`/subjects/${selectUser}`, userData);
  // else
  return cym702.patch(`/subjects/${subjectId}`, userData);
};

const getUserPhysical = () => {
  return cym702.get("/user/main/physical");
};

// const fetchUserPhysical = (userData) => {
//   return cym702.patch("/user/main/physical", userData);
// };
// const fetchGetUserSurvey = () => {
//   return cym702.post("/user/survey");
// };

// const updateSurvey = (survey) => {
//   return cym702.put("/user/survey", survey);
// };

const updateUserDetail = (userData) => {
  return cym702.put("/user/detail", userData);
};

const fetchGetDeviceInfo = (device) => {
  return cym702.post("/user/device", device);
};

const postSubUser = (userData) => {
  return cym702.post("/subjects", userData);
};

const updateSubUserData = (id, data) => {
  return cym702.patch(`/user/sub/${id}`, data);
};

const deleteSubUser = (id) => {
  return cym702.delete(`/subjects/${id}`);
};

const deleteUser = (reason) => {
  return cym702.delete("/accounts/delete", { data: { reason } });
};

const refresh = () => {
  return refreshInstance.post("/auth/token", {
    refreshToken: localStorage.refresh,
  });
};

const getHealthInfo = () => {
  return cym702.get("/health-info");
};

const logout = () => {
  return refreshInstance.delete("/accounts/logout");
};

export {
  // updateSurvey,
  // fetchGetUserSurvey,
  getUserPhysical,
  updateUsername,
  updateSubjectInfo,
  fetchGetUsername,
  updateUserPhone,
  fetchUserPhone,
  checkUserPw,
  updateUserPw,
  fetchUserEmail,
  fetchImageUpload,
  fetchGetUserInfo,
  fetchGetSubjectInfo,
  getMainUserInfo,
  fetchGetMyProfile,
  updateUserDetail,
  fetchGetDeviceInfo,
  postSubUser,
  fetchGetSubUserInfo,
  updateSubUserData,
  deleteSubUser,
  deleteUser,
  getHealthInfo,
  refresh,
  logout,
  // fetchUserPhysical,
};
