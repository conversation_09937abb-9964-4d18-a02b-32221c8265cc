import { instance } from "../index";

export default {
  /** 휴대폰 번호로 가입한 계정 존재여부 확인 후 문자 전송 body: countryNumber, phone, birth */
  getCheckPhoneNum(userData) {
    return instance.post(`/auth/phone/accounts/send-code`, userData, {
      headers: {
        "x-device-id": userData.device,
      },
    });
  },
  /** 인증 코드 확인 전송 body: countryNumber, phone, birth, code */
  postCheckVerifyCode(userData) {
    return instance.post(`/auth/phone/accounts/verify-code`, userData, {
      headers: {
        "x-device-id": userData.device,
      },
    });
  },
  /** 휴대폰 번호와 id 일치 여부 확인 후 문자 전송 body: countryNumber, phone, account  */
  getCheckPhoneNumId(userData) {
    return instance.post(`/auth/phone/password/send-code`, userData, {
      headers: {
        "x-device-id": userData.device,
      },
    });
  },
  /** 인증 코드 유효성 체크 및 비밀번호 재설정 body: countryNumber, phone, account, code, resetppasword  */
  postCheckVerifyCodePw(userData) {
    return instance.post(`/auth/phone/password/verify-code`, userData, {
      headers: {
        "x-device-id": userData.device,
      },
    });
  },

  postOauthValid(sns, token) {
    return instance.post(`/accounts/login/${sns}`, token);
  },
  fetchLoginRequest(userData) {
    return instance.post(`/accounts/login/general`, userData);
  },
  fetchSnsLoginRequest(sns, account) {
    return instance.post(`/accounts/login/${sns}`, account);
  },
  getNewAccessToken() {
    return instance.post(`/auth/token`, {
      headers: {
        Authorization: `Bearer ${localStorage.auth}`,
      },
    });
  },
  logoutRequest() {
    const token = localStorage.auth || "";
    return instance.delete(`/account/logout`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },

  /** 문자 전송 */
  fetchSendSms(phoneNumber) {
    /*global Webview*/
    /*eslint no-undef: "error"*/

    return instance.post(`/auth/phone/signup/send-code`, phoneNumber, {
      headers: {
        "x-device-id": phoneNumber.device,
      },
    });
  },

  /**  인증번호 확인 > 휴대폰 번호 등록 */
  fetchVerifyAuthCode(authNumber) {
    return instance.post(`/auth/phone/signup/verify-code`, authNumber, {
      headers: {
        "x-device-id": authNumber.device,
      },
    });
  },

  /**  general id 유효성 검사 */
  fetchCheckUserId(userId) {
    return instance.post(`/accounts/general/valid`, userId);
  },

  fetchChecknickname(nickname) {
    return instance.post("/account/valid/nickname", nickname);
  },

  // subjects type list
  getTypeList(category) {
    return instance.get(`/subjects/types?category=${category}`);
  },

  /**  신규회원 설문조사 완료, 등록 */
  registerUser(userData) {
    return instance.post(`/accounts/general`, userData);
  },

  registerSnsUser(snsType, userData) {
    return instance.post(`/accounts/${snsType}`, userData);
  },

  fetchCheckPhone(phoneNumber) {
    console.log(phoneNumber);
    return instance.post(
      "/auth/phone/update",
      {
        countryNumber: phoneNumber.countryNumber,
        phone: phoneNumber.phone,
      },
      {
        headers: {
          "x-device-id": phoneNumber.device,
          Authorization: `Bearer ${localStorage.auth}`,
        },
        params: {
          "x-device-id": phoneNumber.device,
        },
      },
    );
  },

  fetchCheckAuth(phoneNumber) {
    return instance.post(
      "/auth/phone/update",
      {
        countryNumber: phoneNumber.countryNumber,
        phone: phoneNumber.phone,
        code: phoneNumber.code,
      },
      {
        headers: {
          "x-device-id": phoneNumber.device,
          Authorization: `Bearer ${localStorage.auth}`,
        },
        params: {
          "x-device-id": phoneNumber.device,
        },
      },
    );
  },
};
