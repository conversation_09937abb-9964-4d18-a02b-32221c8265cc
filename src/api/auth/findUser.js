import { instance } from "../index";

export default {
  /** 휴대폰 번호와 생일이 일치하는 계정 존재여부 확인 후 문자 전송 body: countryNumber, phone, birth */
  getCheckPhoneNumBirth(type, userData) {

    return instance.post(`/auth/phone/accounts/send-code${type}`, userData, {
      headers: {
        "x-device-id": userData.device,
      },
    });
  },
  /** 인증 코드 확인 전송 body: countryNumber, phone, birth, code */
  postCheckVerifyCode(type, userData) {
    return instance.post(`/auth/phone/accounts/verify-code${type}`, userData, {
      headers: {
        "x-device-id": userData.device,
      },
    });
  },
  /** 휴대폰 번호와 계정 일치 여부 확인 후 문자 전송 body: countryNumber, phone, account  */
  getCheckPhoneNumId(type, userData) {
    return instance.post(`/auth/phone/password/send-code${type}`, userData, {
      headers: {
        "x-device-id": userData.device,
      },
    });
  },
  /** 인증 코드 유효성 체크 및 비밀번호 재설정 body: countryNumber, phone, account, code, resetppasword  */
  postCheckVerifyCodePw(type, userData) {
    return instance.post(`/auth/phone/password/verify-code${type}`, userData, {
      headers: {
        "x-device-id": userData.device,
      },
    });
  },
};
