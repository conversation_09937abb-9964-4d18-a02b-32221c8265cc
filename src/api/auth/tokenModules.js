import store from "@/store/index";
import base64 from "base-64";
import { refresh, logout } from "../user/index";

export const getRefreshToken = async () => {
  try {
    const { data, status } = await refresh();

    return data?.accessToken;
  } catch (error) {
    console.log(error.response.status);
    if (
      error.response?.status === 401 ||
      error.response?.status === 400 ||
      // 500대 에러일떄 (500, 502, 504 같은거)
      error.response?.status >= 500
    ) {
      const { status } = await logout();

      if (status === 204 || error.response?.status >= 500) {
        console.log("logout successful");
        store.commit("LOGOUT");
      }
    }
    throw error;
  }
};

export const isExpiringSoon = (expireAt) => {
  const expirationDate = new Date(expireAt * 1000);
  const currentDate = new Date();

  const expirationDateUTC = expirationDate.getTime();
  const currentDateUTC = currentDate.getTime();

  const secondsUntilExpiration = Math.floor((expirationDateUTC - currentDateUTC) / 1000);

  return secondsUntilExpiration < 30;
};

export const setExp = (token) => {
  const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
  const decoded = base64.decode(payload);
  const { exp } = JSON.parse(decoded);
  localStorage.exp = exp;
};
