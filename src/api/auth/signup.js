import { instance } from "../index";

/**
 * check phone => /sign/valid/phone
 * send sms => /sign/send/sms
 * check auth => /sign/check/auth
 * check duplicated phone => /sign/find/m
 * check mail => /sign/valid/mail
 */

//=======================================
//[] 휴대폰 번호 기준 계정 유무 확인
const fetchCheckPhone = (phoneNumber) => {
  return instance.post("/auth/phone/code", phoneNumber);
};

//=======================================
//[] 인증번호 확인 > 휴대폰 번호 등록
const fetchCheckAuth = (authNumber) => {
  return instance.post("/sign/smsauth/check", authNumber);
};

//=======================================
//[] 회원가입 -> 설문조사 리스트
const fetchGetSurvey = () => {
  return instance.get("/survey/health");
};

//=======================================
//[] 신규회원 추가정보 업데이트
const fetchUpdateUserDatail = (userData) => {
  return instance.put("/sign/up/detail", userData);
};

//=======================================
//[] 신규회원 설문조사 완료, 등록
const registerUser = (userData) => {
  return instance.post("/sign/up", userData);
};

//=======================================
//[] SNS 회원가입
const registerSnsUser = (userData) => {
  return instance.post("/sign/up/sns", userData);
};

//=======================================
//[] 이메일 찾기
const fetchFindEmail = (phoneNum) => {
  return instance.post("/sign/find/m", phoneNum);
};

export {
  fetchCheckPhone,
  // fetchSendSms,
  fetchCheckAuth,
  fetchUpdateUserDatail,
  fetchGetSurvey,
  registerUser,
  registerSnsUser,
  fetchFindEmail,
};
