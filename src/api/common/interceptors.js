import store from "@/store/index";
import base64 from "base-64";
import axios from "axios";
import { getRefreshToken, isExpiringSoon } from "../auth/tokenModules";
import { generateRequestId } from "@/utils/generateRequestId";

const delay = 500;
const timeout = 10000; // 10초 타임아웃
const pendingCancels = {};

export function setInterceptors(instance) {
  instance.interceptors.request.use(
    async (config) => {
      const expiration = localStorage.exp;
      let token = localStorage.auth;
      const requestId = generateRequestId(config);

      if (pendingCancels[requestId]) {
        pendingCancels[requestId]("이전 요청 취소");
      }

      config.cancelToken = new axios.CancelToken(function executor(c) {
        pendingCancels[requestId] = c;
      });

      setTimeout(() => {
        if (pendingCancels[requestId]) {
          pendingCancels[requestId]("요청 시간 초과");
          delete pendingCancels[requestId];
        }
      }, timeout + delay);

      if (isExpiringSoon(expiration)) {
        // console.log("refresh token");
        token = await getRefreshToken();
        const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
        const decoded = base64.decode(payload);
        const { exp } = JSON.parse(decoded);
        localStorage.auth = token;
        localStorage.exp = exp;
      }
      config.headers["Authorization"] = `Bearer ${token}`;
      return new Promise((resolve) => setTimeout(() => resolve(config), delay));
    },
    (error) => {
      return Promise.reject(error);
    },
  );

  instance.interceptors.response.use(
    (response) => {
      console.log("response use success");
      const requestId = generateRequestId(response.config); // 요청 ID 생성
      delete pendingCancels[requestId];
      return response;
    },

    async (error) => {
      console.error(error);

      // 타임아웃 에러 처리
      if (axios.isCancel(error)) {
        return Promise.reject(error);
      }

      // 네트워크 에러 처리
      if (error.message === "Network Error") {
        return Promise.reject(error);
      }

      // 401 에러 처리
      if (error.response && error.response.status === 401) {
        console.log("refresh token");
        const token = await getRefreshToken();
        const payload = token.substring(token.indexOf(".") + 1, token.lastIndexOf("."));
        const decoded = base64.decode(payload);
        const { exp } = JSON.parse(decoded);
        localStorage.auth = token;
        localStorage.exp = exp;
        location.reload();
      }

      return Promise.reject(error);
    },
  );

  return instance;
}

export function authInterceptors(instance) {
  instance.interceptors.request.use(
    (config) => {
      config.headers["Authorization"] = `Bearer ${localStorage.auth}`;

      return config;
    },
    (error) => {
      return Promise.reject(error);
    },
  );

  return instance;
}
