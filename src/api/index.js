import axios from "axios";
import { setInterceptors, authInterceptors } from "./common/interceptors";

const VUE_APP_API_URL = "https://dev.yellosis.com/cym702-pet";

function createInstance() {
  return axios.create({
    baseURL: VUE_APP_API_URL,
    withCredentials: true,
    withXSRFToken: true,
    timeout: 10000,
  });
}

// 엑시오스 초기화 함수
function createInstanceWithAuth() {
  const instance = axios.create({
    baseURL: VUE_APP_API_URL,
    withCredentials: true,
    withXSRFToken: true,
    timeout: 10000,
  });

  return setInterceptors(instance);
}

function createInstanceAuth() {
  const instance = axios.create({
    baseURL: VUE_APP_API_URL,
    withCredentials: true,
    withXSRFToken: true,
    timeout: 10000,
  });

  return authInterceptors(instance);
}

export const instance = createInstance();
export const cym702 = createInstanceWithAuth();
export const refreshInstance = createInstanceAuth();
