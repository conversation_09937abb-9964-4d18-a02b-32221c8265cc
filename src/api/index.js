import axios from "axios";
import { setInterceptors, authInterceptors } from "./common/interceptors";

const VUE_APP_API_URL = "https://dev.yellosis.com/cym702-pet";

const AXIOS_CREATE_OPTIONS = {
  // baseURL: VUE_APP_API_URL,
  withCredentials: true,
  withXSRFToken: true,
  timeout: 10000,
};

function createInstance() {
  return axios.create(AXIOS_CREATE_OPTIONS);
}

// 엑시오스 초기화 함수
function createInstanceWithAuth() {
  const instance = axios.create(AXIOS_CREATE_OPTIONS);

  return setInterceptors(instance);
}

function createInstanceAuth() {
  const instance = axios.create(AXIOS_CREATE_OPTIONS);

  return authInterceptors(instance);
}

export const instance = createInstance();
export const cym702 = createInstanceWithAuth();
export const refreshInstance = createInstanceAuth();
