# API 에러 코드 가이드

이 문서는 CYM Pet WebView 애플리케이션에서 사용되는 API 에러 코드들을 정리한 문서입니다.

## 목차

- [000 Auth (인증)](#000-auth-인증)
- [100 Accounts (계정)](#100-accounts-계정)
- [200 Personal Info (개인정보)](#200-personal-info-개인정보)
- [300 Subject (주제)](#300-subject-주제)
- [400 Analysis (분석)](#400-analysis-분석)
- [500 Care (케어)](#500-care-케어)
- [600 Solution (솔루션)](#600-solution-솔루션)

---

## 000 Auth (인증)

### 문자 관련

| 코드 | HTTP 상태 | 설명 | 대응 방안 |
|------|-----------|------|-----------|
| C001 | 500 | 문자 전송 실패 | 네트워크 상태 확인 후 재시도 |
| C002 | 403 | 문자 3회 초과 요청 | 일정 시간 후 재시도 안내 |
| C003 | 400 | 인증코드 문자 틀림 | 올바른 인증코드 입력 요청 |

### 계정 관련

| 코드 | HTTP 상태 | 설명 | 대응 방안 |
|------|-----------|------|-----------|
| C004 | 401 | 아이디(대소문자) 또는 비밀번호 일치하지 않음 | 로그인 정보 재확인 요청 |
| C005 | 404 | 아이디가 데이터베이스에 없음 | 회원가입 안내 또는 아이디 확인 |
| C006 | 401 | 30일 보관(제한) 또는 탈퇴된 계정으로 로그인 시도 | 계정 복구 또는 새 계정 생성 안내 |
| C007 | 401 | refresh token이 cookie에 존재하지 않음 (로그아웃 상태) | 로그인 페이지로 리다이렉트 |

---

## 100 Accounts (계정)

| 코드 | HTTP 상태 | 설명 | 대응 방안 |
|------|-----------|------|-----------|
| C100 | 500 | 회원가입 실패 | 서버 오류, 잠시 후 재시도 |
| C101 | 409 | 회원가입시, 이미 아이디 존재함 | 다른 아이디 사용 요청 |
| C110 | 500 | 회원탈퇴 실패 | 서버 오류, 고객센터 문의 안내 |

---

## 200 Personal Info (개인정보)

| 코드 | HTTP 상태 | 설명 | 대응 방안 |
|------|-----------|------|-----------|
| C201 | 400 | 휴대폰 번호 변경시 변경 전 휴대폰 번호와 같을 경우 | 다른 휴대폰 번호 입력 요청 |
| C202 | 500 | 비밀번호 변경 실패 | 서버 오류, 잠시 후 재시도 |

---

## 300 Subject (주제)

| 코드 | HTTP 상태 | 설명 | 대응 방안 |
|------|-----------|------|-----------|
| C300 | 403 | 유효하지 않은 subject ID 사용 (권한이 없는/삭제되었거나 존재하지 않는) | 유효한 subject ID 확인 또는 권한 확인 |
| C302 | 400 | 유효하지 않은 subject Type ID 사용 (존재하지 않는 ID로 잘못 요청됨) | 올바른 subject Type ID 사용 |
| C303 | 403 | Main Type Subject는 삭제 불가 | Main Type Subject 삭제 불가 안내 |
| C304 | 404 | Main Subject가 존재하지 않음 | Main Subject 생성 또는 확인 필요 |

---

## 400 Analysis (분석)

| 코드 | HTTP 상태 | 설명 | 대응 방안 |
|------|-----------|------|-----------|
| C400 | 500 | 분석 실패 | 서버 오류, 잠시 후 재시도 |
| C401 | 500 | 검사결과가 데이터베이스에 저장이 실패 | 서버 오류, 데이터 재전송 필요 |

---

## 500 Care (케어)

| 코드 | HTTP 상태 | 설명 | 대응 방안 |
|------|-----------|------|-----------|
| C501 | 404 | 삭제할 데이터가 존재하지 않음 | 이미 삭제되었거나 존재하지 않는 데이터 |

---

## 600 Solution (솔루션)

| 코드 | HTTP 상태 | 설명 | 대응 방안 |
|------|-----------|------|-----------|
| C600 | 400 | 음식 데이터가 유효하지 않음 | 올바른 음식 데이터 형식 확인 |
| C601 | 403 | 음식 데이터 최대 조회 10개 이상 넘음 | 조회 개수를 10개 이하로 제한 |

---

## 에러 처리 가이드라인

### 프론트엔드에서의 에러 처리

1. **사용자 친화적 메시지**: 에러 코드를 사용자가 이해하기 쉬운 메시지로 변환
2. **재시도 로직**: 5xx 에러의 경우 자동 재시도 구현
3. **로그인 리다이렉트**: C007 에러 시 자동으로 로그인 페이지로 이동
4. **입력 검증**: 4xx 에러 방지를 위한 클라이언트 사이드 검증

### 에러 코드 사용 예시

```javascript
// API 응답 처리 예시
try {
  const response = await API.fetchCheckPhone(phoneNumber);
} catch (error) {
  const errorCode = error?.response?.data?.response_code;
  
  switch (errorCode) {
    case 'C001':
      showErrorMessage('문자 전송에 실패했습니다. 다시 시도해주세요.');
      break;
    case 'C002':
      showErrorMessage('문자 요청 횟수를 초과했습니다. 잠시 후 다시 시도해주세요.');
      break;
    case 'C003':
      showErrorMessage('인증코드가 올바르지 않습니다.');
      break;
    default:
      showErrorMessage('알 수 없는 오류가 발생했습니다.');
  }
}
```

---

## 업데이트 이력

- 2025-06-19: 초기 문서 생성
