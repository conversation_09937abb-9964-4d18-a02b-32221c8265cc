# Axios Interceptor Logic

이 문서는 `/src/api/common/interceptors.js`의 Axios 인터셉터 구조와 동작 원리를 설명합니다.

---

## 목적
- 모든 API 요청/응답에 대해 공통적으로 처리해야 하는 로직(토큰 갱신, 중복/타임아웃 취소, 인증 헤더, 에러 처리 등)을 담당합니다.

## 요청(Request) 인터셉터

- **중복 요청 취소**: 동일한 요청이 여러 번 발생할 경우, 이전 요청을 취소하고 새 요청만 처리합니다.
- **타임아웃 처리**: 10초(기본값) + 0.5초(delay) 내 응답이 없으면 요청을 자동 취소합니다.
- **토큰 만료 체크 및 갱신**: 토큰 만료가 임박하면 `getRefreshToken()`을 통해 자동으로 갱신 후, localStorage에 저장합니다.
- **Authorization 헤더 자동 추가**: 모든 요청에 Bearer 토큰을 자동으로 추가합니다.
- **요청 딜레이**: UX 개선을 위해 0.5초의 인위적 딜레이를 추가합니다.

## 응답(Response) 인터셉터

- **요청 완료 시 pendingCancels 정리**: 요청이 정상적으로 끝나면 해당 요청의 취소 핸들러를 삭제합니다.
- **에러 처리**:
  - 요청 취소(axios.isCancel) 및 네트워크 에러는 그대로 reject
  - 401(인증 만료) 발생 시 토큰을 갱신하고, 자동으로 페이지를 새로고침하여 재인증을 유도합니다.

## 보조 함수

- **authInterceptors**: 인증이 필요한 별도의 axios 인스턴스에 Authorization 헤더만 추가하는 간단한 인터셉터.

## 사용 예시

```js
import axios from 'axios';
import { setInterceptors } from '@/api/common/interceptors';

const api = setInterceptors(axios.create({ baseURL: '/api' }));
```

---

> 이 문서는 프로젝트 내 Axios 기반 API 통신의 신뢰성과 보안, 사용자 경험을 높이기 위한 공통 처리 로직을 명확히 이해하고 유지보수할 수 있도록 작성되었습니다.
