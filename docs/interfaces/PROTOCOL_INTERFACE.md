# webview.js Documentation

## Overview

`webview.js` is a service module that acts as a bridge for sending messages from the web (WebView) to the native app (React Native). Each function sends a specific action and payload as a JSON string to the app using `window.ReactNativeWebView.postMessage`.

## Function List & Descriptions

### Authentication & Login

- **kakaoLogin()**  
  Requests Kakao login.  
  Action: `REQ-KAKAO-LOGIN`

- **googleLogin()**  
  Requests Google login.  
  Action: `REQ-GOOGLE-LOGIN`

- **googleLogout()**  
  Requests Google logout.  
  Action: `REQ-GOOGLE-LOGOUT`

- **appleLogin()**  
  Requests Apple login.  
  Action: `REQ-APPLE-LOGIN`

- **loginSuccess(message = {})**  
  Notifies the app of a successful login.  
  Action: `REQ-LOGIN-SUCCESS`, Payload: login info

### Camera & URL

- **requestCamera(message)**  
  Requests the app to open the camera.  
  Action: `REQ-TAKE-SHEET`, Payload: request info

- **openUrl(message)**  
  Requests the app to open an external URL.  
  Action: `REQ-OPEN-URL`, Payload: URL info

### Cache & Device

- **cacheClear(message = {})**  
  Requests the app to clear its cache.  
  Action: `REQ-CACHE-CLEAR`, Payload: options

- **getDeviceId()**  
  Requests the device ID.  
  Action: `REQ-DEVICE-ID`

### Exam Alerts

- **getWeeklyAlerts()**  
  Requests the list of weekly alerts.  
  Action: `REQ-LIST-LN-WEEKLY`

- **getMonthlyAlerts()**  
  Requests the list of monthly alerts.  
  Action: `REQ-LIST-LN-MONTHLY`

- **createtWeeklyAlerts(message)**  
  Creates a weekly alert.  
  Action: `REQ-CREATE-LN-WEEKLY`, Payload: alert info

- **deleteWeeklyAlerts(message)**  
  Deletes a weekly alert.  
  Action: `REQ-DELETE-LN-WEEKLY`, Payload: alert info

- **createtMonthlyAlerts(message)**  
  Creates a monthly alert.  
  Action: `REQ-CREATE-LN-MONTHLY`, Payload: alert info

- **deleteMonthlyAlerts(message)**  
  Deletes a monthly alert.  
  Action: `REQ-DELETE-LN-MONTHLY`, Payload: alert info

### FCM (Push Notification)

- **getFcmAuthority()**  
  Checks FCM authorization status.  
  Action: `REQ-IS-AUTHORIZED-FCM`

- **requestPermissionFcm()**  
  Requests FCM permission from the app.  
  Action: `REQ-PREMISSION-FCM`

- **registerFcm()**  
  Requests FCM device registration.  
  Action: `REQ-REGISTER-DEVICE-FCM`

- **unRegisterFcm()**  
  Requests FCM device unregistration.  
  Action: `REQ-UNREGISTER-DEVICE-FCM`

- **subscribeTopic(message)**  
  Requests to subscribe to an FCM topic.  
  Action: `REQ-SUBSCRIBE-TOPIC-FCM`, Payload: topic info

- **unsubscribeTopic(message)**  
  Requests to unsubscribe from an FCM topic.  
  Action: `REQ-UNSUBSCRIBE-TOPIC-FCM`, Payload: topic info

---

## Usage Example

```js
import webview from "@/service/webview";

//  If it's your first time using the file, use the ignore comment below
/*global Webview*/
/*eslint no-undef: "error"*/
// Request camera
webview.requestCamera({ type: "profile" });

// Request Google login
webview.googleLogin();
```

---

This module allows the web to call various native features of the app. Each function must follow the communication protocol (action name, payload), and the app must implement logic to handle each action accordingly.
