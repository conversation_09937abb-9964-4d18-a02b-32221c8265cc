# Quick Start Guide

이 문서는 Cym702: For Pet WebView 프로젝트의 빠른 시작을 위한 요약 가이드입니다.

---

## 1. 환경 준비

- Node.js v16 이상 설치
- Yarn 또는 npm 설치

## 2. 프로젝트 클론

```bash
git clone https://github.com/your-org/cym-pet-webview.git
cd cym-pet-webview
```

## 3. 의존성 설치

```bash
npm install
# 또는
yarn install
```

## 4. 개발 서버 실행

```bash
npm run local
# 또는
npm run serve
```

- 기본 개발 서버: http://localhost:8080

## 5. 빌드

```bash
# 개발 빌드
npm run build:dev

# 운영 빌드
npm run build
```

## 6. 배포

```bash
# 개발 서버 배포
npm run deploy-dev

# 운영 서버 배포
npm run deploy
```

## 7. 주요 명령어 요약

| 명령어                | 설명                |
|----------------------|---------------------|
| npm run local        | 로컬 개발 서버 실행 |
| npm run dev          | 개발 서버 실행      |
| npm run build:dev    | 개발 빌드           |
| npm run build        | 운영 빌드           |
| npm run deploy-dev   | 개발 배포           |
| npm run deploy       | 운영 배포           |

## 8. 기타 참고

- 환경 변수: `.env.*` 파일 참고
- 주요 설정: `vue.config.js`, `babel.config.js`
- 폴더 구조 및 상세 설명: `README.md` 참고

---

자세한 내용은 README.md를 참고하세요.
